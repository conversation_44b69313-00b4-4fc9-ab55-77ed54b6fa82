# APIMatic Proxy Libary for JavaScript

> This library is currently in preview.

Provides utility functions to generate HTTP and HTTPS proxy agents based on configuration. It supports conditional behavior for both Node.js and browser environments.

The exported helper functions and interfaces include:

1. **createProxyAgents**: Creates and returns HTTP and HTTPS proxy agents using provided proxy settings. In browser environments, this function returns undefined with a warning, as proxy agents are not supported in browsers.
2. **ProxySettings**: Defines the proxy configuration, including a required address and optional port and authentication credentials.

This library is used by JavaScript SDKs generated by the [APIMatic Code Generator](http://www.apimatic.io).

## Builds

The following environments are supported:

1. Node.js v14+ and v16+
1. Bundlers like Rollup or Webpack
1. Web browsers

To support multiple environments, we export various builds:

| Environment | Usage                                                                      |
| --- |----------------------------------------------------------------------------|
| Common.js | Import like this: `require('@apimatic/proxy')`.                            |
| ES Module | Import like this: `import { /* your imports */ } from '@apimatic/proxy'`.  |
| Browsers | *Use script: `https://unpkg.com/@apimatic/proxy@VERSION/umd/schema.js`     |
| Modern Browsers (supports ESM and uses modern JS) | *Use script: `https://unpkg.com/@apimatic/proxy@VERSION/umd/schema.esm.js` |

_* Don't forget to replace VERSION with the version number._

**Note**: We discourage importing files or modules directly from the package. These are likely to change in the future and should not be considered stable.