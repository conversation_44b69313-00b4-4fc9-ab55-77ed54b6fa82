const express = require('express');
const router = express.Router();
const productController = require('../controllers/productController');
const {
  authenticate,
  requireAdmin,
  requireProductManagement,
  requireProductView,
  auditAdminAction
} = require('../middleware/auth');
const { validateProduct, validateId, validatePagination } = require('../middleware/validation');
const { uploadMultipleProductImages, handleUploadError } = require('../middleware/upload');

// @route   GET /api/products
// @desc    Get all products with filtering and pagination
// @access  Public
router.get('/', validatePagination, productController.getProducts);

// @route   GET /api/products/featured
// @desc    Get featured products
// @access  Public
router.get('/featured', productController.getFeaturedProducts);

// @route   GET /api/products/best-sellers
// @desc    Get best seller products
// @access  Public
router.get('/best-sellers', productController.getBestSellerProducts);

// @route   GET /api/products/search
// @desc    Search products
// @access  Public
router.get('/search', productController.searchProducts);

// @route   GET /api/products/:id (numeric ID)
// @desc    Get single product by ID
// @access  Public
router.get('/:id(\\d+)', validateId('id'), productController.getProductById);

// @route   GET /api/products/:slug
// @desc    Get single product by slug
// @access  Public
router.get('/:slug', productController.getProductBySlug);

// @route   POST /api/products
// @desc    Create new product (with optional image upload)
// @access  Private (Admin only - requires products.create permission)
router.post('/',
  authenticate,
  requireProductManagement,
  auditAdminAction('CREATE_PRODUCT', 'PRODUCT'),
  uploadMultipleProductImages,
  handleUploadError,
  validateProduct,
  productController.createProduct
);

// @route   PUT /api/products/:id
// @desc    Update product
// @access  Private (Admin only - requires products.edit permission)
router.put('/:id',
  authenticate,
  requireProductManagement,
  auditAdminAction('UPDATE_PRODUCT', 'PRODUCT'),
  validateId('id'),
  validateProduct,
  productController.updateProduct
);

// @route   DELETE /api/products/:id
// @desc    Delete product
// @access  Private (Admin only - requires products.delete permission)
router.delete('/:id',
  authenticate,
  requireProductManagement,
  auditAdminAction('DELETE_PRODUCT', 'PRODUCT'),
  validateId('id'),
  productController.deleteProduct
);

// Product variant routes
// @route   GET /api/products/:id/variants
// @desc    Get product variants
// @access  Public
router.get('/:id/variants', validateId('id'), productController.getProductVariants);

// @route   POST /api/products/:id/variants
// @desc    Add product variant
// @access  Private (Admin only - requires products.edit permission)
router.post('/:id/variants',
  authenticate,
  requireProductManagement,
  auditAdminAction('CREATE_PRODUCT_VARIANT', 'PRODUCT'),
  validateId('id'),
  productController.addProductVariant
);

// @route   PUT /api/products/:id/variants/:variantId
// @desc    Update product variant
// @access  Private (Admin only - requires products.edit permission)
router.put('/:id/variants/:variantId',
  authenticate,
  requireProductManagement,
  auditAdminAction('UPDATE_PRODUCT_VARIANT', 'PRODUCT'),
  validateId('id'),
  productController.updateProductVariant
);

// @route   DELETE /api/products/:id/variants/:variantId
// @desc    Delete product variant
// @access  Private (Admin only - requires products.delete permission)
router.delete('/:id/variants/:variantId',
  authenticate,
  requireProductManagement,
  auditAdminAction('DELETE_PRODUCT_VARIANT', 'PRODUCT'),
  validateId('id'),
  productController.deleteProductVariant
);

module.exports = router;
