#!/bin/bash
# Make script executable: chmod +x diagnose-404-assets.sh

# Nirvana Organics - 404 Assets Diagnosis Script
# Diagnoses missing admin frontend assets and static file serving issues

set -e

echo "🔍 Nirvana Organics - 404 Assets Diagnosis"
echo "==========================================="

# Color codes
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}✅ $1${NC}"
}

warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

error() {
    echo -e "${RED}❌ $1${NC}"
}

# Step 1: Check source build files
log "Step 1: Checking source build files..."

echo "📁 Source Build Directories:"
if [ -d "dist" ]; then
    echo "  ✅ dist/ (main frontend)"
    ls -la dist/ | head -5
else
    echo "  ❌ dist/ (main frontend) - MISSING"
fi

echo ""
if [ -d "dist-admin" ]; then
    echo "  ✅ dist-admin/ (admin frontend)"
    ls -la dist-admin/ | head -5
else
    echo "  ❌ dist-admin/ (admin frontend) - MISSING"
fi

echo ""
if [ -d "dist-admin/assets" ]; then
    echo "  ✅ dist-admin/assets/ (admin assets)"
    ls -la dist-admin/assets/
else
    echo "  ❌ dist-admin/assets/ (admin assets) - MISSING"
fi

# Step 2: Check deployment target directories
log "Step 2: Checking deployment target directories..."

TARGET_MAIN="/var/www/nirvana-frontend-test/main"
TARGET_ADMIN="/var/www/nirvana-frontend-test/admin"

echo "📁 Deployment Target Directories:"
if [ -d "$TARGET_MAIN" ]; then
    echo "  ✅ $TARGET_MAIN"
    ls -la "$TARGET_MAIN" | head -5
else
    echo "  ❌ $TARGET_MAIN - MISSING"
fi

echo ""
if [ -d "$TARGET_ADMIN" ]; then
    echo "  ✅ $TARGET_ADMIN"
    ls -la "$TARGET_ADMIN" | head -5
else
    echo "  ❌ $TARGET_ADMIN - MISSING"
fi

echo ""
if [ -d "$TARGET_ADMIN/assets" ]; then
    echo "  ✅ $TARGET_ADMIN/assets/"
    ls -la "$TARGET_ADMIN/assets/"
else
    echo "  ❌ $TARGET_ADMIN/assets/ - MISSING"
fi

# Step 3: Check specific missing assets
log "Step 3: Checking specific missing assets..."

MISSING_JS="admin-DitisM-I.js"
MISSING_CSS="admin-TY7ZtfqV.css"

echo "🔍 Specific Asset Files:"
echo "JavaScript: $MISSING_JS"
if [ -f "dist-admin/assets/$MISSING_JS" ]; then
    echo "  ✅ Source exists: dist-admin/assets/$MISSING_JS"
else
    echo "  ❌ Source missing: dist-admin/assets/$MISSING_JS"
fi

if [ -f "$TARGET_ADMIN/assets/$MISSING_JS" ]; then
    echo "  ✅ Deployed exists: $TARGET_ADMIN/assets/$MISSING_JS"
else
    echo "  ❌ Deployed missing: $TARGET_ADMIN/assets/$MISSING_JS"
fi

echo ""
echo "CSS: $MISSING_CSS"
if [ -f "dist-admin/assets/$MISSING_CSS" ]; then
    echo "  ✅ Source exists: dist-admin/assets/$MISSING_CSS"
else
    echo "  ❌ Source missing: dist-admin/assets/$MISSING_CSS"
fi

if [ -f "$TARGET_ADMIN/assets/$MISSING_CSS" ]; then
    echo "  ✅ Deployed exists: $TARGET_ADMIN/assets/$MISSING_CSS"
else
    echo "  ❌ Deployed missing: $TARGET_ADMIN/assets/$MISSING_CSS"
fi

# Step 4: Check file permissions
log "Step 4: Checking file permissions..."

if [ -d "$TARGET_ADMIN" ]; then
    echo "📋 File Permissions:"
    ls -la "$TARGET_ADMIN" | head -5
    
    if [ -d "$TARGET_ADMIN/assets" ]; then
        echo ""
        echo "Assets Directory Permissions:"
        ls -la "$TARGET_ADMIN/assets/"
    fi
else
    warning "Target admin directory doesn't exist, skipping permission check"
fi

# Step 5: Check admin server configuration
log "Step 5: Checking admin server static file configuration..."

ADMIN_SERVER="/var/www/nirvana-backend-test/server/admin-server.js"

if [ -f "$ADMIN_SERVER" ]; then
    echo "🔧 Admin Server Static File Configuration:"
    grep -n "static\|express.static\|adminFrontendPath" "$ADMIN_SERVER" | head -10
else
    error "Admin server file not found: $ADMIN_SERVER"
fi

# Step 6: Check PM2 process status
log "Step 6: Checking PM2 process status..."

echo "📊 PM2 Process Status:"
pm2 list | grep nirvana || warning "No nirvana processes found"

echo ""
echo "Recent Admin Server Logs:"
pm2 logs nirvana-backend-admin-test --lines 5 --nostream 2>/dev/null || warning "Could not retrieve admin server logs"

# Step 7: Test HTTP requests
log "Step 7: Testing HTTP requests..."

echo "🌐 HTTP Request Tests:"

# Test admin.html
echo "Testing admin.html:"
curl -I "https://test.shopnirvanaorganics.com/admin/" 2>/dev/null | head -3 || warning "Could not test admin.html"

echo ""
# Test admin assets
echo "Testing admin JavaScript:"
curl -I "https://test.shopnirvanaorganics.com/admin/assets/$MISSING_JS" 2>/dev/null | head -3 || warning "Could not test admin JavaScript"

echo ""
echo "Testing admin CSS:"
curl -I "https://test.shopnirvanaorganics.com/admin/assets/$MISSING_CSS" 2>/dev/null | head -3 || warning "Could not test admin CSS"

# Step 8: Check Nginx configuration
log "Step 8: Checking Nginx configuration..."

NGINX_CONF="/etc/nginx/sites-available/nirvana-organics-test"

if [ -f "$NGINX_CONF" ]; then
    echo "🔧 Nginx Configuration for /admin/ location:"
    grep -A 10 -B 2 "location /admin" "$NGINX_CONF" 2>/dev/null || warning "Could not find /admin location in Nginx config"
else
    warning "Nginx configuration file not found: $NGINX_CONF"
fi

# Step 9: Provide diagnosis summary
log "Step 9: Diagnosis Summary"

echo ""
echo "📋 DIAGNOSIS SUMMARY"
echo "==================="

# Check if assets exist in source
if [ -f "dist-admin/assets/$MISSING_JS" ] && [ -f "dist-admin/assets/$MISSING_CSS" ]; then
    success "✅ Assets exist in source build directory"
else
    error "❌ Assets missing from source build directory"
    echo "   Solution: Run 'npm run build:admin:test' to rebuild admin frontend"
fi

# Check if assets exist in deployment
if [ -f "$TARGET_ADMIN/assets/$MISSING_JS" ] && [ -f "$TARGET_ADMIN/assets/$MISSING_CSS" ]; then
    success "✅ Assets exist in deployment directory"
else
    error "❌ Assets missing from deployment directory"
    echo "   Solution: Run './deploy-frontend-assets.sh' to deploy assets"
fi

# Check if deployment directory exists
if [ -d "$TARGET_ADMIN" ]; then
    success "✅ Admin deployment directory exists"
else
    error "❌ Admin deployment directory missing"
    echo "   Solution: Create directory and deploy assets"
fi

echo ""
echo "🔧 RECOMMENDED ACTIONS"
echo "====================="

if [ ! -f "dist-admin/assets/$MISSING_JS" ]; then
    echo "1. Rebuild admin frontend: npm run build:admin:test"
fi

if [ ! -d "$TARGET_ADMIN" ] || [ ! -f "$TARGET_ADMIN/assets/$MISSING_JS" ]; then
    echo "2. Deploy frontend assets: ./deploy-frontend-assets.sh"
fi

echo "3. Restart admin server: pm2 restart nirvana-backend-admin-test"
echo "4. Test assets in browser: https://test.shopnirvanaorganics.com/admin/"

echo ""
success "🎉 Diagnosis completed!"
