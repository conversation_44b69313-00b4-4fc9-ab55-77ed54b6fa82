#!/bin/bash
# Make script executable: chmod +x deploy-and-fix.sh

# Nirvana Organics Test Environment - Critical Fix Deployment Script
# This script addresses both frontend path and database schema issues

set -e  # Exit on any error

echo "🚀 Nirvana Organics Test Environment - Critical Fix Deployment"
echo "=============================================================="

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}✅ $1${NC}"
}

warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if running as correct user
if [ "$USER" != "Nirvana" ]; then
    error "This script must be run as the 'Nirvana' user"
    echo "Please run: sudo -u Nirvana $0"
    exit 1
fi

# Step 1: Stop PM2 processes
log "Step 1: Stopping PM2 processes..."
pm2 stop nirvana-backend-main-test nirvana-backend-admin-test 2>/dev/null || warning "Some processes may not have been running"
pm2 delete nirvana-backend-main-test nirvana-backend-admin-test 2>/dev/null || warning "Some processes may not have existed"
success "PM2 processes stopped and deleted"

# Step 2: Deploy updated server files
log "Step 2: Deploying updated server configurations..."

# Ensure deployment directory exists
if [ ! -d "/var/www/nirvana-backend-test" ]; then
    error "Deployment directory /var/www/nirvana-backend-test does not exist"
    exit 1
fi

# Copy updated server files
cp -f server/admin-server.js /var/www/nirvana-backend-test/server/
cp -f server/index.js /var/www/nirvana-backend-test/server/
cp -f server/models/* /var/www/nirvana-backend-test/server/models/ 2>/dev/null || true
cp -f server/config/* /var/www/nirvana-backend-test/server/config/ 2>/dev/null || true

# Copy database management script
cp -f database-manager.js /var/www/nirvana-backend-test/ 2>/dev/null || warning "Database manager script not found"

success "Server files deployed to /var/www/nirvana-backend-test/"

# Step 3: Verify environment files
log "Step 3: Verifying environment configuration..."

cd /var/www/nirvana-backend-test

if [ ! -f ".env.test" ]; then
    error "Missing .env.test file in deployment directory"
    exit 1
fi

if [ ! -f ".env.admin.test" ]; then
    error "Missing .env.admin.test file in deployment directory"
    exit 1
fi

# Check NODE_ENV in environment files
if ! grep -q "NODE_ENV=test" .env.test; then
    warning "NODE_ENV not set to 'test' in .env.test"
fi

if ! grep -q "NODE_ENV=test" .env.admin.test; then
    warning "NODE_ENV not set to 'test' in .env.admin.test"
fi

success "Environment files verified"

# Step 4: Test database connection
log "Step 4: Testing database connection..."

# Set NODE_ENV for database operations
export NODE_ENV=test

# Run comprehensive database fix using the new database manager
if [ -f "database-manager.js" ]; then
    log "Running comprehensive database fix..."
    node database-manager.js fix
    if [ $? -ne 0 ]; then
        error "Database fix failed"
        exit 1
    fi
    success "Database fix completed successfully"
else
    warning "Database manager script not found, using fallback initialization"
    # Fallback to old method
    node -e "
const { initializeDatabase } = require('./server/models/database');
(async () => {
  try {
    console.log('🔄 Initializing database...');
    const success = await initializeDatabase();
    if (success) {
      console.log('✅ Database initialization completed successfully');
      process.exit(0);
    } else {
      console.error('❌ Database initialization failed');
      process.exit(1);
    }
  } catch (error) {
    console.error('❌ Database initialization error:', error);
    process.exit(1);
  }
})();
"
    if [ $? -ne 0 ]; then
        error "Database initialization failed"
        exit 1
    fi
fi

# Step 5: Verify frontend directories exist
log "Step 5: Verifying frontend deployment directories..."

if [ ! -d "/var/www/nirvana-frontend-test" ]; then
    warning "Frontend deployment directory does not exist, creating..."
    mkdir -p /var/www/nirvana-frontend-test/main
    mkdir -p /var/www/nirvana-frontend-test/admin
    success "Created frontend deployment directories"
fi

if [ ! -d "/var/www/nirvana-frontend-test/main" ]; then
    mkdir -p /var/www/nirvana-frontend-test/main
fi

if [ ! -d "/var/www/nirvana-frontend-test/admin" ]; then
    mkdir -p /var/www/nirvana-frontend-test/admin
fi

# Create placeholder files if they don't exist
if [ ! -f "/var/www/nirvana-frontend-test/main/index.html" ]; then
    warning "Main frontend not deployed, creating placeholder..."
    echo '<!DOCTYPE html><html><head><title>Nirvana Organics - Main (Test)</title></head><body><h1>Main Frontend - Test Environment</h1><p>Frontend build not yet deployed.</p></body></html>' > /var/www/nirvana-frontend-test/main/index.html
fi

if [ ! -f "/var/www/nirvana-frontend-test/admin/admin.html" ]; then
    warning "Admin frontend not deployed, creating placeholder..."
    echo '<!DOCTYPE html><html><head><title>Nirvana Organics - Admin (Test)</title></head><body><h1>Admin Frontend - Test Environment</h1><p>Admin frontend build not yet deployed.</p></body></html>' > /var/www/nirvana-frontend-test/admin/admin.html
fi

success "Frontend directories verified"

# Step 6: Start PM2 processes
log "Step 6: Starting PM2 processes with updated configuration..."

pm2 start ecosystem.config.js
pm2 save

success "PM2 processes started successfully"

# Step 7: Health checks
log "Step 7: Performing health checks..."

sleep 5  # Give services time to start

# Check if processes are running
if pm2 list | grep -q "nirvana-backend-main-test.*online"; then
    success "Main server is running"
else
    error "Main server failed to start"
fi

if pm2 list | grep -q "nirvana-backend-admin-test.*online"; then
    success "Admin server is running"
else
    error "Admin server failed to start"
fi

# Step 8: Display status and next steps
log "Step 8: Deployment completed!"

echo ""
echo "📋 DEPLOYMENT SUMMARY"
echo "===================="
success "✅ PM2 processes restarted with updated server configurations"
success "✅ Database schema initialized (Products table should now exist)"
success "✅ Frontend directories verified and placeholders created"
success "✅ Environment-aware frontend paths now active"

echo ""
echo "🔍 VERIFICATION STEPS"
echo "===================="
echo "1. Check PM2 status:     pm2 status"
echo "2. View logs:            pm2 logs nirvana-backend-admin-test"
echo "3. Test health endpoint: curl https://test.shopnirvanaorganics.com/api/health"
echo "4. Test admin endpoint:  curl https://test.shopnirvanaorganics.com/admin/api/health"

echo ""
echo "📝 NEXT STEPS"
echo "============="
echo "1. Deploy actual frontend builds to:"
echo "   - /var/www/nirvana-frontend-test/main/"
echo "   - /var/www/nirvana-frontend-test/admin/"
echo "2. Monitor PM2 logs for any remaining issues"
echo "3. Test the application functionality"

echo ""
success "🎉 Critical fix deployment completed successfully!"
