const { User, Order, sequelize } = require('../models');
const { Op } = require('sequelize');

/**
 * Customer Analytics Service
 * Handles customer membership tracking, analytics, and insights
 */
class CustomerAnalyticsService {
  /**
   * Determine customer membership type based on order history
   */
  static async determineMembershipType(userId) {
    try {
      const user = await User.findByPk(userId);
      if (!user) return 'first-time';

      // Get order statistics
      const orderStats = await Order.findOne({
        attributes: [
          [sequelize.fn('COUNT', sequelize.col('id')), 'totalOrders'],
          [sequelize.fn('SUM', sequelize.col('total')), 'totalSpent'],
          [sequelize.fn('AVG', sequelize.col('total')), 'averageOrderValue']
        ],
        where: {
          userId,
          status: { [Op.in]: ['completed', 'delivered'] }
        }
      });

      const stats = orderStats?.dataValues || {};
      const totalOrders = parseInt(stats.totalOrders) || 0;
      const totalSpent = parseFloat(stats.totalSpent) || 0;

      // Determine membership type based on criteria
      if (totalOrders === 0) {
        return 'first-time';
      } else if (totalOrders >= 10 || totalSpent >= 1000) {
        return 'premium';
      } else if (totalOrders >= 3 || totalSpent >= 300) {
        return 'regular';
      } else {
        return 'first-time';
      }
    } catch (error) {
      console.error('Error determining membership type:', error);
      return 'first-time';
    }
  }

  /**
   * Update customer membership type
   */
  static async updateCustomerMembership(userId) {
    try {
      const membershipType = await this.determineMembershipType(userId);
      
      await User.update(
        { membershipType },
        { where: { id: userId } }
      );

      return membershipType;
    } catch (error) {
      console.error('Error updating customer membership:', error);
      throw error;
    }
  }

  /**
   * Calculate customer lifetime value
   */
  static async calculateLifetimeValue(userId) {
    try {
      const result = await Order.findOne({
        attributes: [
          [sequelize.fn('SUM', sequelize.col('total')), 'lifetimeValue']
        ],
        where: {
          userId,
          status: { [Op.in]: ['completed', 'delivered'] }
        }
      });

      return parseFloat(result?.dataValues?.lifetimeValue) || 0;
    } catch (error) {
      console.error('Error calculating lifetime value:', error);
      return 0;
    }
  }

  /**
   * Check if this is customer's first order
   */
  static async isFirstOrder(userId) {
    try {
      const orderCount = await Order.count({
        where: {
          userId,
          status: { [Op.in]: ['completed', 'delivered'] }
        }
      });

      return orderCount === 0;
    } catch (error) {
      console.error('Error checking first order:', error);
      return true;
    }
  }

  /**
   * Get customer analytics summary
   */
  static async getCustomerAnalytics(userId) {
    try {
      const user = await User.findByPk(userId);
      if (!user) throw new Error('User not found');

      // Get order statistics
      const orderStats = await Order.findOne({
        attributes: [
          [sequelize.fn('COUNT', sequelize.col('id')), 'totalOrders'],
          [sequelize.fn('SUM', sequelize.col('total')), 'totalSpent'],
          [sequelize.fn('AVG', sequelize.col('total')), 'averageOrderValue'],
          [sequelize.fn('MAX', sequelize.col('createdAt')), 'lastOrderDate'],
          [sequelize.fn('MIN', sequelize.col('createdAt')), 'firstOrderDate']
        ],
        where: {
          userId,
          status: { [Op.in]: ['completed', 'delivered'] }
        }
      });

      const stats = orderStats?.dataValues || {};

      // Get recent orders
      const recentOrders = await Order.findAll({
        where: { userId },
        order: [['createdAt', 'DESC']],
        limit: 5,
        attributes: ['id', 'orderNumber', 'total', 'status', 'createdAt']
      });

      // Calculate days since last order
      const lastOrderDate = stats.lastOrderDate ? new Date(stats.lastOrderDate) : null;
      const daysSinceLastOrder = lastOrderDate 
        ? Math.floor((new Date() - lastOrderDate) / (1000 * 60 * 60 * 24))
        : null;

      return {
        customer: {
          id: user.id,
          firstName: user.firstName,
          lastName: user.lastName,
          email: user.email,
          membershipType: user.membershipType,
          trafficSource: user.trafficSource,
          createdAt: user.createdAt
        },
        orderStats: {
          totalOrders: parseInt(stats.totalOrders) || 0,
          totalSpent: parseFloat(stats.totalSpent) || 0,
          averageOrderValue: parseFloat(stats.averageOrderValue) || 0,
          firstOrderDate: stats.firstOrderDate,
          lastOrderDate: stats.lastOrderDate,
          daysSinceLastOrder
        },
        recentOrders,
        insights: {
          isActive: daysSinceLastOrder !== null && daysSinceLastOrder <= 90,
          isHighValue: parseFloat(stats.totalSpent) >= 500,
          orderFrequency: this.calculateOrderFrequency(stats.firstOrderDate, parseInt(stats.totalOrders))
        }
      };
    } catch (error) {
      console.error('Error getting customer analytics:', error);
      throw error;
    }
  }

  /**
   * Calculate order frequency (orders per month)
   */
  static calculateOrderFrequency(firstOrderDate, totalOrders) {
    if (!firstOrderDate || totalOrders <= 1) return 0;

    const monthsSinceFirst = Math.max(1, 
      (new Date() - new Date(firstOrderDate)) / (1000 * 60 * 60 * 24 * 30)
    );

    return (totalOrders / monthsSinceFirst).toFixed(2);
  }

  /**
   * Get shipping cost based on method and order total
   */
  static calculateShippingCost(shippingMethod, orderTotal, customerMembershipType = 'first-time') {
    const shippingRates = {
      regular: 9.99,
      express: 19.99
    };

    switch (shippingMethod) {
      case 'regular':
        // Free shipping over $100
        if (orderTotal >= 100) {
          return { cost: 0, method: 'regular-free', description: 'Free Standard Shipping' };
        }
        return { 
          cost: shippingRates.regular, 
          method: 'regular', 
          description: 'Standard Shipping (5-7 business days)' 
        };

      case 'express':
        let expressCost = shippingRates.express;
        // Premium members get $5 off express shipping
        if (customerMembershipType === 'premium') {
          expressCost = Math.max(0, expressCost - 5);
        }
        return { 
          cost: expressCost, 
          method: 'express', 
          description: 'Express Shipping (2-3 business days)' 
        };

      case 'regular-free':
        return { cost: 0, method: 'regular-free', description: 'Free Standard Shipping' };

      default:
        return { cost: shippingRates.regular, method: 'regular', description: 'Standard Shipping' };
    }
  }

  /**
   * Track traffic source from referrer or UTM parameters
   */
  static determineTrafficSource(req) {
    const { utm_source, utm_medium, utm_campaign, ref } = req.query;
    const referrer = req.get('Referrer') || '';

    // Check UTM parameters first
    if (utm_source) {
      if (utm_medium === 'social' || ['facebook', 'instagram', 'twitter', 'tiktok'].includes(utm_source)) {
        return { source: 'social-media', details: utm_source };
      }
      if (utm_medium === 'cpc' || utm_medium === 'paid') {
        return { source: 'paid', details: `${utm_source}-${utm_campaign || 'campaign'}` };
      }
      if (utm_source === 'email') {
        return { source: 'referral', details: 'email-campaign' };
      }
    }

    // Check referral parameter
    if (ref) {
      return { source: 'referral', details: ref };
    }

    // Check referrer header
    if (referrer) {
      const referrerDomain = new URL(referrer).hostname.toLowerCase();
      
      // Social media referrers
      if (['facebook.com', 'instagram.com', 'twitter.com', 'tiktok.com', 'youtube.com'].some(domain => 
          referrerDomain.includes(domain))) {
        return { source: 'social-media', details: referrerDomain };
      }
      
      // Search engines
      if (['google.com', 'bing.com', 'yahoo.com', 'duckduckgo.com'].some(domain => 
          referrerDomain.includes(domain))) {
        return { source: 'organic', details: referrerDomain };
      }
      
      // Other referrers
      if (!referrerDomain.includes(process.env.FRONTEND_URL || 'localhost')) {
        return { source: 'referral', details: referrerDomain };
      }
    }

    // Default to direct traffic
    return { source: 'direct', details: null };
  }

  /**
   * Get customer segmentation data
   */
  static async getCustomerSegmentation() {
    try {
      const segments = await User.findAll({
        attributes: [
          'membershipType',
          [sequelize.fn('COUNT', sequelize.col('id')), 'count']
        ],
        group: ['membershipType']
      });

      const trafficSources = await User.findAll({
        attributes: [
          'trafficSource',
          [sequelize.fn('COUNT', sequelize.col('id')), 'count']
        ],
        where: {
          trafficSource: { [Op.not]: null }
        },
        group: ['trafficSource']
      });

      return {
        membershipSegments: segments.map(s => ({
          type: s.membershipType,
          count: parseInt(s.dataValues.count)
        })),
        trafficSources: trafficSources.map(s => ({
          source: s.trafficSource,
          count: parseInt(s.dataValues.count)
        }))
      };
    } catch (error) {
      console.error('Error getting customer segmentation:', error);
      throw error;
    }
  }
}

module.exports = CustomerAnalyticsService;
