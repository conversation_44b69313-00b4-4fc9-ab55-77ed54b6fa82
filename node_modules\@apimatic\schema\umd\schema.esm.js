function e(e){const t=[];for(let n=0;n<e.length;n++){const a=e[n];t.push([n,a])}return t}function t(e){const t=Object.keys(e);let n=t.length;const a=new Array(n);for(;n--;)a[n]=[t[n],e[t[n]]];return a}function n(e){return"string"==typeof e?`"${e.replace(/"/g,'"')}"`:`${e}`}function a(e){return e}function r(e){return(t,n)=>e(t,n.strictValidation)?[]:n.fail()}function o(e){return t={type:()=>e.type,validateBeforeMap:e.validate,validateBeforeUnmap:e.validate,map:e.map,unmap:e.map},Object.assign(Object.assign({},t),{validateBeforeMapXml:t.validateBeforeUnmap,mapXml:t.map,unmapXml:t.unmap});var t}function i(e,t){return t?"number"==typeof e:"number"==typeof e||"string"==typeof e&&!isNaN(e)}function l(e){return"number"==typeof e?e:+e}function p(e){return"bigint"==typeof e?e:BigInt(e)}function u(e,t){const n=new Set(t),a={};for(const t in e)Object.prototype.hasOwnProperty.call(e,t)&&!n.has(t)&&(a[t]=e[t]);return a}function c(e){return-1!==e.indexOf(" ")?n(e):e}function m(e){return null==e}function f(e,t){return function(e){return e.startsWith("Optional<Nullable<")||e.startsWith("Nullable<Optional<")}(e)&&m(t)}function s(e,t){const n=b(h(e,t.type())),a=t.validateBeforeMap(e,n);return 0===a.length?f(t.type(),e)?{errors:!1,result:e}:{errors:!1,result:t.map(e,n)}:{errors:a}}function d(e,t){const n=b(h(e,t.type())),a=t.validateBeforeUnmap(e,n);return 0===a.length?{errors:!1,result:t.unmap(e,n)}:{errors:a}}function y(e,t){const n=b(h(e,t.type())),a=t.validateBeforeMapXml(e,n);return 0===a.length?{errors:!1,result:t.mapXml(e,n)}:{errors:a}}function v(e,t){const n=b(h(e,t.type())),a=t.validateBeforeUnmap(e,n);return 0===a.length?{errors:!1,result:t.unmapXml(e,n)}:{errors:a}}function h(e,t,n){return{value:e,type:t,branch:[e],path:[],strictValidation:n}}function b(e){const t=(t,n,a)=>b({value:n,type:a.type(),branch:e.branch.concat(n),path:e.path.concat(t),strictValidation:e.strictValidation}),n=(e,n,a)=>e.map((e=>a(e,t(e[0],e[1],n))));return Object.assign(Object.assign({},e),{createChild:t,flatmapChildren:(...e)=>function(e){const t=[];for(const n of e)for(const e of n)t.push(e);return t}(n(...e)),mapChildren:n,fail:t=>[{value:e.value,type:e.type,branch:e.branch,path:e.path,message:j(e,t)}]})}function j(e,t){const n=JSON.stringify(e.value,((e,t)=>"bigint"==typeof t?t.toString():t));if(t=(null!=t?t:`Expected value to be of type '${e.type}' but found '${typeof e.value}'.`)+"\n"+`\nGiven value: ${n}`+`\nType: '${typeof e.value}'`+`\nExpected type: '${e.type}'`,e.path.length>0){t+=`\nPath: ${e.path.map((e=>c(e.toString()))).join(" › ")}`}return t}function B(e,t,n){return t&&n?function(e,t,n){return{type:()=>`OneOf<${e.map((e=>e.type())).join(" | ")}>`,validateBeforeMap:(a,r)=>{const o=a&&"object"==typeof a&&a[n];return o&&t[o]?t[o].validateBeforeMap(a,r):X(e,a,r)},validateBeforeUnmap:(a,r)=>{const o=a&&"object"==typeof a&&a[n];return o&&t[o]?t[o].validateBeforeUnmap(a,r):O(e,a,r)},map:(a,r)=>{const o=a&&a[n];return o&&t[o]?t[o].map(a,r):$(e,a,r)},unmap:(a,r)=>{const o=a&&a[n];return o&&t[o]?t[o].unmap(a,r):x(e,a,r)},validateBeforeMapXml:(a,r)=>{const o=a&&"object"==typeof a&&a[n];return o&&t[o]?t[o].validateBeforeMapXml(a,r):g(e,a,r)},mapXml:(a,r)=>{const o=a&&a[n];return o&&t[o]?t[o].mapXml(a,r):U(e,a,r)},unmapXml:(a,r)=>{const o=a&&a[n];return o&&t[o]?t[o].unmapXml(a,r):S(e,a,r)}}}(e,t,n):function(e){return{type:()=>`OneOf<${e.map((e=>e.type())).join(" | ")}>`,validateBeforeMap:(t,n)=>X(e,t,n),validateBeforeUnmap:(t,n)=>O(e,t,n),map:(t,n)=>$(e,t,n),unmap:(t,n)=>x(e,t,n),validateBeforeMapXml:(t,n)=>g(e,t,n),mapXml:(t,n)=>U(e,t,n),unmapXml:(t,n)=>S(e,t,n)}}(e)}function X(e,t,n){const a=[];for(const r of e){0===r.validateBeforeMap(t,n).length&&a.push(r)}return M(a,n)}function O(e,t,n){const a=[];for(const r of e){0===r.validateBeforeUnmap(t,n).length&&a.push(r)}return M(a,n)}function g(e,t,n){const a=[];for(const r of e){0===r.validateBeforeMapXml(t,n).length&&a.push(r)}return M(a,n)}function M(e,t){return e.length>0?[]:t.fail("Could not match against any acceptable type.")}function $(e,t,n){const a=[];for(const r of e)0===r.validateBeforeMap(t,n).length&&a.push(r);return a.length>0?a[0].map(t,n):void 0}function x(e,t,n){const a=[];for(const r of e)0===r.validateBeforeUnmap(t,n).length&&a.push(r);return a.length>0?a[0].unmap(t,n):void 0}function U(e,t,n){const a=[];for(const r of e)0===r.validateBeforeMapXml(t,n).length&&a.push(r);return a.length>0?a[0].mapXml(t,n):void 0}function S(e,t,n){const a=[];for(const r of e)0===r.validateBeforeMapXml(t,n).length&&a.push(r);return a.length>0?a[0].unmapXml(t,n):void 0}function A(t,n){let a;return a={type:()=>`Array<${t.type()}>`,validateBeforeMap:(n,a)=>Array.isArray(n)?a.flatmapChildren(e(n),t,((e,n)=>t.validateBeforeMap(e[1],n))):a.fail(),validateBeforeUnmap:(n,a)=>Array.isArray(n)?a.flatmapChildren(e(n),t,((e,n)=>t.validateBeforeUnmap(e[1],n))):a.fail(),map:(n,a)=>a.mapChildren(e(n),t,((e,n)=>t.map(e[1],n))),unmap:(n,a)=>a.mapChildren(e(n),t,((e,n)=>t.unmap(e[1],n))),mapXml:(a,r)=>{let o=a;return(null==n?void 0:n.xmlItemName)&&(o=a[n.xmlItemName],r=r.createChild(n.xmlItemName,o,t)),r.mapChildren(e(o),t,((e,n)=>t.mapXml(e[1],n)))},unmapXml:(a,r)=>{const o=r.mapChildren(e(a),t,((e,n)=>t.unmapXml(e[1],n)));return(null==n?void 0:n.xmlItemName)?{[n.xmlItemName]:o}:o},validateBeforeMapXml:(a,r)=>{let o=a;if(null==n?void 0:n.xmlItemName){const e=`Expected array to be wrapped with XML element ${n.xmlItemName}.`;if("object"!=typeof a||null===a||!(n.xmlItemName in a))return r.fail(e);o=a[n.xmlItemName],r=r.createChild(n.xmlItemName,o,t)}return Array.isArray(o)?r.flatmapChildren(e(o),t,((e,n)=>t.validateBeforeMapXml(e[1],n))):r.fail()}},a}function N(e,t){return t?"bigint"==typeof e:"bigint"==typeof e||"number"==typeof e||"string"==typeof e&&/^-?\d+$/.test(e)}function P(){return o({type:"bigint",validate:r(N),map:p})}function w(e,t){return t?"boolean"==typeof e:"boolean"==typeof e||"string"==typeof e&&("true"===e||"false"===e)}function C(){return o({type:"boolean",validate:r(w),map:e=>"boolean"==typeof e?e:"true"===e})}function k(e,t){return{type:()=>`Defaults<${e.type()},${n(t)}>`,validateBeforeMap:(n,a)=>E(n,t)?[]:e.validateBeforeMap(n,a),validateBeforeUnmap:(n,a)=>E(n,t)?[]:e.validateBeforeUnmap(n,a),map:(n,a)=>E(n,t)?t:e.map(n,a),unmap:(n,a)=>E(n,t)?t:e.unmap(n,a),validateBeforeMapXml:(n,a)=>E(n,t)?[]:e.validateBeforeMapXml(n,a),mapXml:(n,a)=>E(n,t)?t:e.mapXml(n,a),unmapXml:(n,a)=>E(n,t)?t:e.unmapXml(n,a)}}function E(e,t){return null==e||e===t}function T(e){const n=(n,a,r)=>{if("object"!=typeof a||null===a)return r.fail();const o=a;return r.flatmapChildren(t(o),e,((t,a)=>e[n](t[1],a)))};return{type:()=>`Record<string,${e.type()}>`,validateBeforeMap:(...e)=>n("validateBeforeMap",...e),validateBeforeUnmap:(...e)=>n("validateBeforeUnmap",...e),map:(t,n)=>{const a={};for(const r in t)if(Object.prototype.hasOwnProperty.call(t,r)){const o=t[r];a[r]=e.map(o,n.createChild(r,o,e))}return a},unmap:(t,n)=>{const a={};for(const r in t)if(Object.prototype.hasOwnProperty.call(t,r)){const o=t[r];a[r]=e.unmap(o,n.createChild(r,o,e))}return a},validateBeforeMapXml:(...e)=>n("validateBeforeMapXml",...e),mapXml:(t,n)=>{const a={};for(const r in t)if(Object.prototype.hasOwnProperty.call(t,r)){const o=t[r];a[r]=e.mapXml(o,n.createChild(r,o,e))}return a},unmapXml:(t,n)=>{const a={};for(const r in t)if(Object.prototype.hasOwnProperty.call(t,r)){const o=t[r];a[r]=e.unmapXml(o,n.createChild(r,o,e))}return a}}}function V(e){const n=T(e),a=Object.assign({},n);return a.unmapXml=(e,a)=>({entry:t(n.unmapXml(e,a)).map((([e,t])=>({$:{key:e},_:t})))}),a.mapXml=(e,t)=>{if(!("entry"in e))return{};let{entry:a}=e;Array.isArray(a)||(a=[a]);const r={};for(const e of a)r[e.$.key]=e._;return n.mapXml(r,t)},a.validateBeforeMapXml=(e,t)=>{if("object"!=typeof e||null===e)return t.fail();if(!("entry"in e))return[];let a=e.entry;Array.isArray(a)||(a=[a]);const r={};for(const e of a){if("object"!=typeof e||null===e)return t.fail('Expected "entry" to be an XML element.');if(!("$"in e)||!("key"in e.$))return t.fail('Expected "entry" element to have an attribute named "key".');const n=e;r[n.$.key]=n._}return n.validateBeforeMapXml(r,t)},a}function I(e,n,a,r,o){const i=Object.values(a).reverse(),l=(e,t,n,o)=>{const l=((e,t,n)=>{if("object"==typeof e&&null!==e&&(n&&function(e,t){return"$"in e&&"object"==typeof e.$&&t in e.$}(e,t)||!n&&t in e)){const r=n?e.$[t]:e[t];if("string"==typeof r&&r in a)return a[r]}})(e,t,o);if(void 0!==l)return l;for(const e in i)if(0===n(i[e]).length)return i[e];return a[r]},p=(e,t)=>l(e,n,(n=>n.validateBeforeMap(e,t))),u=(e,t)=>{var a;return l(e,null!==(a=null==o?void 0:o.xmlName)&&void 0!==a?a:n,(n=>n.validateBeforeMapXml(e,t)),null==o?void 0:o.isAttr)},c=(t,n)=>l(t,e,(e=>e.validateBeforeUnmap(t,n)));return{type:()=>`DiscriminatedUnion<${n},[${t(a).map((([e,t])=>t.type)).join(",")}]>`,map:(e,t)=>p(e,t).map(e,t),unmap:(e,t)=>c(e,t).unmap(e,t),validateBeforeMap:(e,t)=>p(e,t).validateBeforeMap(e,t),validateBeforeUnmap:(e,t)=>c(e,t).validateBeforeUnmap(e,t),mapXml:(e,t)=>u(e,t).mapXml(e,t),unmapXml:(e,t)=>c(e,t).unmapXml(e,t),validateBeforeMapXml:(e,t)=>u(e,t).validateBeforeMapXml(e,t)}}function W(e){const t=function(e){let t,n=!1;return function(...a){return n||(n=!0,t=e.apply(this,a)),t}}(e);return{type:()=>`Lazy<${t().type()}>`,map:(...e)=>t().map(...e),unmap:(...e)=>t().unmap(...e),validateBeforeMap:(...e)=>t().validateBeforeMap(...e),validateBeforeUnmap:(...e)=>t().validateBeforeUnmap(...e),mapXml:(...e)=>t().mapXml(...e),unmapXml:(...e)=>t().unmapXml(...e),validateBeforeMapXml:(...e)=>t().validateBeforeMapXml(...e)}}function L(e){return o({type:`Literal<${n(e)}>`,validate:r((t=>e===t)),map:()=>e})}function _(e){return{type:()=>`Nullable<${e.type()}>`,validateBeforeMap:(t,n)=>m(t)?[]:e.validateBeforeMap(t,n),validateBeforeUnmap:(t,n)=>null===t?[]:e.validateBeforeUnmap(t,n),map:(t,n)=>m(t)?null:e.map(t,n),unmap:(t,n)=>null===t?null:e.unmap(t,n),validateBeforeMapXml:(t,n)=>null===t?[]:e.validateBeforeMapXml(t,n),mapXml:(t,n)=>null===t?null:e.mapXml(t,n),unmapXml:(t,n)=>null===t?null:e.unmapXml(t,n)}}function D(){return o({type:"number",validate:r(i),map:l})}function z(e,t=!1){const n=r(function(e,t=!1){const n=Object.values(e);return t?e=>i(e):e=>i(e)&&n.includes(l(e))}(e,t));return o({type:`Enum<${Object.values(e).filter((e=>"number"==typeof e)).join(",")}>`,map:l,validate:n})}function G(e,t){var n={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.indexOf(a)<0&&(n[a]=e[a]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(a=Object.getOwnPropertySymbols(e);r<a.length;r++)t.indexOf(a[r])<0&&Object.prototype.propertyIsEnumerable.call(e,a[r])&&(n[a[r]]=e[a[r]])}return n}function J(e){return{type:()=>`Optional<${e.type()}>`,validateBeforeMap:(t,n)=>m(t)?[]:e.validateBeforeMap(t,n),validateBeforeUnmap:(t,n)=>void 0===t?[]:e.validateBeforeUnmap(t,n),map:(t,n)=>m(t)?void 0:e.map(t,n),unmap:(t,n)=>void 0===t?void 0:e.unmap(t,n),validateBeforeMapXml:(t,n)=>void 0===t?[]:e.validateBeforeMapXml(t,n),mapXml:(t,n)=>void 0===t?void 0:e.mapXml(t,n),unmapXml:(t,n)=>void 0===t?void 0:e.unmapXml(t,n)}}function R(e){const t=Z(e,!1,!1);return t.type=()=>`StrictObject<{${Object.keys(e).map(c).join(",")}}>`,t}function q(e){return Z(e,!0,!0)}function F(e,t,n){return Z(e,!0,[t,J(T(n))])}function H(e){const t=Z(e,!0,!1);return t.type=()=>`Object<{${Object.keys(e).map(c).join(",")}}>`,t}function K(e,t){return R(Object.assign(Object.assign({},e.objectSchema),t))}function Q(e,t){return q(Object.assign(Object.assign({},e.objectSchema),t))}function Y(e,t){return H(Object.assign(Object.assign({},e.objectSchema),t))}function Z(e,t,n){const a=Object.keys(e),r=pe(e),o=function(e){var t,n;const a={},r={};for(const o in e)if(Object.prototype.hasOwnProperty.call(e,o)){const[i,,l]=e[o];!0===(null==l?void 0:l.isAttr)?r[null!==(t=l.xmlName)&&void 0!==t?t:i]=o:a[null!==(n=null==l?void 0:l.xmlName)&&void 0!==n?n:i]=o}return{elementsToProps:a,attributesToProps:r}}(e),i=function(e){var t;const n={},a={};for(const r in e)if(Object.prototype.hasOwnProperty.call(e,r)){const o=e[r],[i,l,p]=o;((null==p?void 0:p.isAttr)?a:n)[r]=[null!==(t=null==p?void 0:p.xmlName)&&void 0!==t?t:i,l,p]}return{elementsSchema:n,attributesSchema:a}}(e),l=function(e){return{attributesSchema:pe(e.attributesSchema),elementsSchema:pe(e.elementsSchema)}}(i);return{type:()=>`Object<{${a.map(c).join(",")},...}>`,validateBeforeMap:ie(e,"validateBeforeMap",t,n),validateBeforeUnmap:ie(r,"validateBeforeUnmap",t,n),map:le(e,"map",n),unmap:le(r,"unmap",n),validateBeforeMapXml:ee(e,o,t,n),mapXml:te(i,n),unmapXml:ne(l,n),objectSchema:e}}function ee(e,t,n,a){const{elementsToProps:r,attributesToProps:o}=t;return(t,i)=>{if("object"!=typeof t||null===t)return i.fail();if(Array.isArray(t))return i.fail(`Expected value to be of type '${i.type}' but found 'Array<${typeof t}>'.`);const l=t,{$:p}=l,u=G(l,["$"]);let c={validationMethod:"validateBeforeMapXml",propTypeName:"child elements",propTypePrefix:"element",valueTypeName:"element",propMapping:r,objectSchema:e,valueObject:u,ctxt:i,skipAdditionalPropValidation:n,mapAdditionalProps:a};const m=ae(c);c=Object.assign(Object.assign({},c),{propTypeName:"attributes",propTypePrefix:"@",propMapping:o,valueObject:null!=p?p:{}});const f=ae(c);return m.concat(f)}}function te(e,n){const{elementsSchema:a,attributesSchema:r}=e,o=le(a,"mapXml",n),i=le(r,"mapXml",!1),l=t(r).map((([e,[t]])=>t));return(e,t)=>{const a=e,{$:r}=a,p=G(a,["$"]),c=null!=r?r:{},m=Object.assign(Object.assign({},i(c,t)),o(p,t));if(n){const e=u(c,l);Object.keys(e).length>0&&(m.$=e)}return m}}function ne(e,n){const{elementsSchema:a,attributesSchema:r}=e,o=le(a,"unmapXml",n),i=le(r,"unmapXml",!1),l=t(r).map((([e,[t]])=>t));return(e,t)=>{const a=e,{$:r}=a,p=G(a,["$"]),c="object"==typeof r&&null!==r&&n?r:{};return Object.assign(Object.assign({},o(u(p,l),t)),{$:Object.assign(Object.assign({},c),i(e,t))})}}function ae({validationMethod:e,propTypeName:t,propTypePrefix:n,valueTypeName:a,propMapping:r,objectSchema:o,valueObject:i,ctxt:l,skipAdditionalPropValidation:p,mapAdditionalProps:u}){const c=[],m=new Set,f=new Set,s=new Set(Object.keys(i));if("validateBeforeMap"!==e&&"boolean"!=typeof u&&u[0]in i)for(const[e,t]of Object.entries(i[u[0]]))Object.prototype.hasOwnProperty.call(o,e)&&f.add(e);oe(f,(e=>re("Some keys in additional properties are conflicting with the keys in",a,e)),c,l);for(const t in r)if(Object.prototype.hasOwnProperty.call(r,t)){const a=o[r[t]][1];s.delete(t),t in i?a[e](i[t],l.createChild(n+t,i[t],a)).forEach((e=>c.push(e))):(d=a.type()).startsWith("Optional<")||d.startsWith("Nullable<")||m.add(t)}var d;return p||oe(s,(e=>re(`Some unknown ${t} were found in the`,a,e)),c,l),oe(m,(e=>re(`Some ${t} are missing in the`,a,e)),c,l),c}function re(e,t,a){return`${e} ${t}: ${a.map(n).join(", ")}.`}function oe(e,t,n,a){const r=Array.from(e);if(r.length>0){const e=t(r);a.fail(e).forEach((e=>n.push(e)))}}function ie(e,t,n,a){const r=function(e){const t={};for(const n in e)if(Object.prototype.hasOwnProperty.call(e,n)){t[e[n][0]]=n}return t}(e);return(o,i)=>"object"!=typeof o||null===o?i.fail():Array.isArray(o)?i.fail(`Expected value to be of type '${i.type}' but found 'Array<${typeof o}>'.`):ae({validationMethod:t,propTypeName:"properties",propTypePrefix:"",valueTypeName:"object",propMapping:r,objectSchema:e,valueObject:o,ctxt:i,skipAdditionalPropValidation:n,mapAdditionalProps:a})}function le(e,t,n){return(a,r)=>{const o={},i=Object.assign({},a),l="unmap"===t||"unmapXml"===t;return l&&"boolean"!=typeof n&&n[0]in i&&(Object.entries(i[n[0]]).forEach((([e,t])=>i[e]=t)),delete i[n[0]]),Object.entries(e).forEach((([e,n])=>{const a=n[0],l=i[a];if(delete i[a],f(n[1].type(),l)){if(void 0===l)return;o[e]=null}else(function(e,t){return e.startsWith("Optional<")&&void 0===t})(n[1].type(),l)||(o[e]=n[1][t](l,r.createChild(a,l,n[1])))})),Object.entries(function(e,t,n){const a={};if(!n)return a;if("boolean"==typeof n)return Object.entries(e).forEach((([e,t])=>a[e]=t)),a;if(Object.entries(e).forEach((([e,r])=>{const o={[e]:r},i=t?d(o,n[1]):s(o,n[1]);i.errors||(a[e]=i.result[e])})),t||0===Object.entries(a).length)return a;return{[n[0]]:a}}(i,l,n)).forEach((([e,t])=>o[e]=t)),o}}const pe=e=>Object.entries(e).reduce(((e,[t,n])=>Object.assign(Object.assign({},e),{[n[0]]:[t,n[1],n[2]]})),{});function ue(e,t,n){return t&&n?function(e,t,n){return{type:()=>`OneOf<${e.map((e=>e.type())).join(" | ")}>`,validateBeforeMap:(a,r)=>{const o=a&&"object"==typeof a&&a[n];return o&&t[o]?t[o].validateBeforeMap(a,r):ce(e,a,r)},validateBeforeUnmap:(a,r)=>{const o=a&&"object"==typeof a&&a[n];return o&&t[o]?t[o].validateBeforeUnmap(a,r):me(e,a,r)},map:(a,r)=>{const o=a&&a[n];return o&&t[o]?t[o].map(a,r):de(e,a,r)},unmap:(a,r)=>{const o=a&&a[n];return o&&t[o]?t[o].unmap(a,r):ye(e,a,r)},validateBeforeMapXml:(a,r)=>{const o=a&&"object"==typeof a&&a[n];return o&&t[o]?t[o].validateBeforeMapXml(a,r):fe(e,a,r)},mapXml:(a,r)=>{const o=a&&a[n];return o&&t[o]?t[o].mapXml(a,r):ve(e,a,r)},unmapXml:(a,r)=>{const o=a&&a[n];return o&&t[o]?t[o].unmapXml(a,r):he(e,a,r)}}}(e,t,n):function(e){return{type:()=>`OneOf<${e.map((e=>e.type())).join(" | ")}>`,validateBeforeMap:(t,n)=>ce(e,t,n),validateBeforeUnmap:(t,n)=>me(e,t,n),map:(t,n)=>de(e,t,n),unmap:(t,n)=>ye(e,t,n),validateBeforeMapXml:(t,n)=>fe(e,t,n),mapXml:(t,n)=>ve(e,t,n),unmapXml:(t,n)=>he(e,t,n)}}(e)}function ce(e,t,n){const a=[];n.strictValidation=!0;for(const r of e){0===r.validateBeforeMap(t,n).length&&a.push(r)}return se(a,n)}function me(e,t,n){const a=[];n.strictValidation=!0;for(const r of e){0===r.validateBeforeUnmap(t,n).length&&a.push(r)}return se(a,n)}function fe(e,t,n){const a=[];n.strictValidation=!0;for(const r of e){0===r.validateBeforeMapXml(t,n).length&&a.push(r)}return se(a,n)}function se(e,t){return 1===e.length?[]:0===e.length?t.fail("Could not match against any acceptable type."):t.fail(`Matched more than one type. Matched types include: ${e.map((e=>e.type())).join(", ")}`)}function de(e,t,n){const a=[];n.strictValidation=!0;for(const r of e)0===r.validateBeforeMap(t,n).length&&a.push(r);return 1===a.length?a[0].map(t,n):void 0}function ye(e,t,n){const a=[];n.strictValidation=!0;for(const r of e)0===r.validateBeforeUnmap(t,n).length&&a.push(r);return 1===a.length?a[0].unmap(t,n):void 0}function ve(e,t,n){const a=[];n.strictValidation=!0;for(const r of e)0===r.validateBeforeMapXml(t,n).length&&a.push(r);return 1===a.length?a[0].mapXml(t,n):void 0}function he(e,t,n){const a=[];n.strictValidation=!0;for(const r of e)0===r.validateBeforeMapXml(t,n).length&&a.push(r);return 1===a.length?a[0].unmapXml(t,n):void 0}function be(e){return"string"==typeof e}function je(){return o({type:"string",validate:r(be),map:a})}function Be(e,t=!1){const i=r(function(e,t=!1){const n=Object.values(e);return t?e=>"string"==typeof e:e=>"string"==typeof e&&n.includes(e)}(e,t));return o({type:`Enum<${Object.values(e).map(n).join(",")}>`,map:a,validate:i})}function Xe(){return o({type:"unknown",validate:()=>[],map:a})}export{B as anyOf,A as array,P as bigint,C as boolean,k as defaults,T as dict,V as dictWithXmlEntries,I as discriminatedObject,q as expandoObject,Q as extendExpandoObject,Y as extendObject,K as extendStrictObject,W as lazy,L as literal,_ as nullable,D as number,z as numberEnum,H as object,ue as oneOf,J as optional,R as strictObject,je as string,Be as stringEnum,F as typedExpandoObject,Xe as unknown,s as validateAndMap,y as validateAndMapXml,d as validateAndUnmap,v as validateAndUnmapXml};
