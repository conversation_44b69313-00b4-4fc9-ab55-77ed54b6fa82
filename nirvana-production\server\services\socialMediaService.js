const axios = require('axios');
const crypto = require('crypto');
const { URLSearchParams } = require('url');

/**
 * Social Media Service
 * Handles OAuth authentication and API interactions for various social media platforms
 */
class SocialMediaService {
  constructor() {
    this.platforms = {
      facebook: {
        authUrl: 'https://www.facebook.com/v18.0/dialog/oauth',
        tokenUrl: 'https://graph.facebook.com/v18.0/oauth/access_token',
        apiUrl: 'https://graph.facebook.com/v18.0',
        scopes: ['pages_manage_posts', 'pages_read_engagement', 'pages_show_list', 'instagram_basic', 'instagram_content_publish']
      },
      twitter: {
        authUrl: 'https://twitter.com/i/oauth2/authorize',
        tokenUrl: 'https://api.twitter.com/2/oauth2/token',
        apiUrl: 'https://api.twitter.com/2',
        scopes: ['tweet.read', 'tweet.write', 'users.read', 'offline.access']
      },
      instagram: {
        authUrl: 'https://api.instagram.com/oauth/authorize',
        tokenUrl: 'https://api.instagram.com/oauth/access_token',
        apiUrl: 'https://graph.instagram.com',
        scopes: ['user_profile', 'user_media']
      },
      linkedin: {
        authUrl: 'https://www.linkedin.com/oauth/v2/authorization',
        tokenUrl: 'https://www.linkedin.com/oauth/v2/accessToken',
        apiUrl: 'https://api.linkedin.com/v2',
        scopes: ['r_liteprofile', 'r_emailaddress', 'w_member_social']
      },
      youtube: {
        authUrl: 'https://accounts.google.com/o/oauth2/v2/auth',
        tokenUrl: 'https://oauth2.googleapis.com/token',
        apiUrl: 'https://www.googleapis.com/youtube/v3',
        scopes: ['https://www.googleapis.com/auth/youtube.upload', 'https://www.googleapis.com/auth/youtube.readonly']
      },
      pinterest: {
        authUrl: 'https://www.pinterest.com/oauth/',
        tokenUrl: 'https://api.pinterest.com/v5/oauth/token',
        apiUrl: 'https://api.pinterest.com/v5',
        scopes: ['boards:read', 'pins:read', 'pins:write']
      }
    };
  }

  /**
   * Generate OAuth authorization URL
   */
  async getAuthUrl(platform) {
    if (!this.platforms[platform]) {
      throw new Error(`Unsupported platform: ${platform}`);
    }

    const config = this.platforms[platform];
    const state = crypto.randomBytes(32).toString('hex');
    
    const params = new URLSearchParams({
      client_id: process.env[`${platform.toUpperCase()}_CLIENT_ID`],
      redirect_uri: `${process.env.BACKEND_URL}/api/admin/social-media/callback/${platform}`,
      scope: config.scopes.join(' '),
      response_type: 'code',
      state
    });

    // Platform-specific parameters
    if (platform === 'twitter') {
      params.append('code_challenge', 'challenge');
      params.append('code_challenge_method', 'plain');
    }

    return `${config.authUrl}?${params.toString()}`;
  }

  /**
   * Handle OAuth callback and exchange code for tokens
   */
  async handleCallback(platform, code, state) {
    if (!this.platforms[platform]) {
      throw new Error(`Unsupported platform: ${platform}`);
    }

    const config = this.platforms[platform];
    
    try {
      // Exchange code for access token
      const tokenData = await this.exchangeCodeForToken(platform, code);
      
      // Get user profile information
      const profileData = await this.getUserProfile(platform, tokenData.access_token);
      
      return {
        id: profileData.id,
        name: profileData.name,
        handle: profileData.handle || profileData.username,
        profilePicture: profileData.profile_picture || profileData.avatar,
        accessToken: tokenData.access_token,
        refreshToken: tokenData.refresh_token,
        expiresAt: tokenData.expires_in ? new Date(Date.now() + tokenData.expires_in * 1000) : null,
        followersCount: profileData.followers_count || 0,
        followingCount: profileData.following_count || 0,
        postsCount: profileData.posts_count || 0,
        platformData: profileData,
        permissions: tokenData.scope ? tokenData.scope.split(' ') : config.scopes
      };
    } catch (error) {
      console.error(`Error handling ${platform} callback:`, error);
      throw new Error(`Failed to authenticate with ${platform}: ${error.message}`);
    }
  }

  /**
   * Exchange authorization code for access token
   */
  async exchangeCodeForToken(platform, code) {
    const config = this.platforms[platform];
    
    const params = {
      client_id: process.env[`${platform.toUpperCase()}_CLIENT_ID`],
      client_secret: process.env[`${platform.toUpperCase()}_CLIENT_SECRET`],
      code,
      redirect_uri: `${process.env.BACKEND_URL}/api/admin/social-media/callback/${platform}`
    };

    // Platform-specific parameters
    if (platform === 'facebook' || platform === 'instagram') {
      params.grant_type = 'authorization_code';
    } else if (platform === 'twitter') {
      params.grant_type = 'authorization_code';
      params.code_verifier = 'challenge';
    } else if (platform === 'linkedin') {
      params.grant_type = 'authorization_code';
    } else if (platform === 'youtube') {
      params.grant_type = 'authorization_code';
    }

    const response = await axios.post(config.tokenUrl, params, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Accept': 'application/json'
      }
    });

    return response.data;
  }

  /**
   * Get user profile information
   */
  async getUserProfile(platform, accessToken) {
    const config = this.platforms[platform];
    
    let profileUrl;
    let headers = {
      'Authorization': `Bearer ${accessToken}`,
      'Accept': 'application/json'
    };

    switch (platform) {
      case 'facebook':
        profileUrl = `${config.apiUrl}/me?fields=id,name,picture.width(200).height(200)`;
        break;
      case 'twitter':
        profileUrl = `${config.apiUrl}/users/me?user.fields=id,name,username,profile_image_url,public_metrics`;
        break;
      case 'instagram':
        profileUrl = `${config.apiUrl}/me?fields=id,username,media_count`;
        break;
      case 'linkedin':
        profileUrl = `${config.apiUrl}/people/~:(id,firstName,lastName,profilePicture(displayImage~:playableStreams))`;
        break;
      case 'youtube':
        profileUrl = `${config.apiUrl}/channels?part=snippet,statistics&mine=true`;
        break;
      case 'pinterest':
        profileUrl = `${config.apiUrl}/user_account`;
        break;
      default:
        throw new Error(`Profile endpoint not configured for ${platform}`);
    }

    const response = await axios.get(profileUrl, { headers });
    
    // Normalize response data across platforms
    return this.normalizeProfileData(platform, response.data);
  }

  /**
   * Normalize profile data across different platforms
   */
  normalizeProfileData(platform, data) {
    switch (platform) {
      case 'facebook':
        return {
          id: data.id,
          name: data.name,
          profile_picture: data.picture?.data?.url,
          followers_count: 0 // Facebook doesn't provide this in basic profile
        };
      
      case 'twitter':
        return {
          id: data.data.id,
          name: data.data.name,
          username: data.data.username,
          profile_picture: data.data.profile_image_url,
          followers_count: data.data.public_metrics?.followers_count || 0,
          following_count: data.data.public_metrics?.following_count || 0,
          posts_count: data.data.public_metrics?.tweet_count || 0
        };
      
      case 'instagram':
        return {
          id: data.id,
          name: data.username,
          username: data.username,
          posts_count: data.media_count || 0
        };
      
      case 'linkedin':
        const firstName = data.firstName?.localized?.en_US || '';
        const lastName = data.lastName?.localized?.en_US || '';
        return {
          id: data.id,
          name: `${firstName} ${lastName}`.trim(),
          profile_picture: data.profilePicture?.displayImage?.elements?.[0]?.identifiers?.[0]?.identifier
        };
      
      case 'youtube':
        const channel = data.items?.[0];
        return {
          id: channel?.id,
          name: channel?.snippet?.title,
          profile_picture: channel?.snippet?.thumbnails?.default?.url,
          followers_count: parseInt(channel?.statistics?.subscriberCount) || 0,
          posts_count: parseInt(channel?.statistics?.videoCount) || 0
        };
      
      case 'pinterest':
        return {
          id: data.id,
          name: data.username,
          username: data.username,
          profile_picture: data.profile_image,
          followers_count: data.follower_count || 0,
          following_count: data.following_count || 0,
          posts_count: data.pin_count || 0
        };
      
      default:
        return data;
    }
  }

  /**
   * Refresh access token
   */
  async refreshToken(platform, refreshToken) {
    if (!this.platforms[platform]) {
      throw new Error(`Unsupported platform: ${platform}`);
    }

    const config = this.platforms[platform];
    
    const params = {
      client_id: process.env[`${platform.toUpperCase()}_CLIENT_ID`],
      client_secret: process.env[`${platform.toUpperCase()}_CLIENT_SECRET`],
      refresh_token: refreshToken,
      grant_type: 'refresh_token'
    };

    try {
      const response = await axios.post(config.tokenUrl, params, {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'Accept': 'application/json'
        }
      });

      return {
        accessToken: response.data.access_token,
        refreshToken: response.data.refresh_token || refreshToken,
        expiresAt: response.data.expires_in ? new Date(Date.now() + response.data.expires_in * 1000) : null
      };
    } catch (error) {
      console.error(`Error refreshing ${platform} token:`, error);
      throw new Error(`Failed to refresh ${platform} token: ${error.message}`);
    }
  }

  /**
   * Revoke access token
   */
  async revokeToken(platform, accessToken) {
    // Implementation varies by platform
    switch (platform) {
      case 'facebook':
        await axios.delete(`https://graph.facebook.com/v18.0/me/permissions?access_token=${accessToken}`);
        break;
      case 'twitter':
        await axios.post('https://api.twitter.com/2/oauth2/revoke', 
          { token: accessToken },
          {
            headers: {
              'Content-Type': 'application/x-www-form-urlencoded',
              'Authorization': `Basic ${Buffer.from(`${process.env.TWITTER_CLIENT_ID}:${process.env.TWITTER_CLIENT_SECRET}`).toString('base64')}`
            }
          }
        );
        break;
      case 'youtube':
        await axios.post(`https://oauth2.googleapis.com/revoke?token=${accessToken}`);
        break;
      default:
        console.warn(`Token revocation not implemented for ${platform}`);
    }
  }

  /**
   * Sync account data (followers, metrics, etc.)
   */
  async syncAccountData(platform, accessToken) {
    try {
      const profileData = await this.getUserProfile(platform, accessToken);
      
      return {
        followersCount: profileData.followers_count || 0,
        followingCount: profileData.following_count || 0,
        postsCount: profileData.posts_count || 0,
        profileData: profileData
      };
    } catch (error) {
      console.error(`Error syncing ${platform} account data:`, error);
      throw error;
    }
  }

  /**
   * Publish post to social media platform
   */
  async publishPost(post) {
    const results = {
      success: true,
      platformPostIds: {},
      errors: {}
    };

    for (const platform of post.platforms) {
      try {
        const account = await require('../models/SocialMediaAccount').findOne({
          where: { platform, isActive: true }
        });

        if (!account) {
          throw new Error(`No active account found for ${platform}`);
        }

        const postId = await this.publishToPlatform(platform, account.accessToken, {
          content: post.content,
          mediaUrls: post.mediaUrls,
          mediaType: post.mediaType,
          hashtags: post.hashtags
        });

        results.platformPostIds[platform] = postId;
      } catch (error) {
        console.error(`Error publishing to ${platform}:`, error);
        results.errors[platform] = error.message;
        results.success = false;
      }
    }

    if (!results.success) {
      results.error = `Failed to publish to some platforms: ${Object.keys(results.errors).join(', ')}`;
    }

    return results;
  }

  /**
   * Publish content to specific platform
   */
  async publishToPlatform(platform, accessToken, content) {
    const config = this.platforms[platform];
    const headers = {
      'Authorization': `Bearer ${accessToken}`,
      'Content-Type': 'application/json'
    };

    switch (platform) {
      case 'facebook':
        const fbResponse = await axios.post(`${config.apiUrl}/me/feed`, {
          message: content.content
        }, { headers });
        return fbResponse.data.id;

      case 'twitter':
        const twitterResponse = await axios.post(`${config.apiUrl}/tweets`, {
          text: content.content
        }, { headers });
        return twitterResponse.data.data.id;

      case 'linkedin':
        const linkedinResponse = await axios.post(`${config.apiUrl}/ugcPosts`, {
          author: `urn:li:person:${await this.getLinkedInPersonId(accessToken)}`,
          lifecycleState: 'PUBLISHED',
          specificContent: {
            'com.linkedin.ugc.ShareContent': {
              shareCommentary: {
                text: content.content
              },
              shareMediaCategory: 'NONE'
            }
          },
          visibility: {
            'com.linkedin.ugc.MemberNetworkVisibility': 'PUBLIC'
          }
        }, { headers });
        return linkedinResponse.data.id;

      default:
        throw new Error(`Publishing not implemented for ${platform}`);
    }
  }

  /**
   * Get LinkedIn person ID for posting
   */
  async getLinkedInPersonId(accessToken) {
    const response = await axios.get('https://api.linkedin.com/v2/people/~', {
      headers: { 'Authorization': `Bearer ${accessToken}` }
    });
    return response.data.id;
  }
}

module.exports = new SocialMediaService();
