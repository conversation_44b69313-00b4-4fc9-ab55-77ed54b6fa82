# APIMatic Core Libary for JavaScript

> This library is currently in preview.

Provides core http logic, file wrapper, api response and validation error classes.

This library is used by JavaScript SDKs generated by the [APIMatic Code Generator](http://www.apimatic.io).

## Builds

The following environments are supported:

1. Node.js v14+ and v16+
1. Bundlers like Rollup or Webpack
1. Web browsers

To support multiple environments, we export various builds:

| Environment | Usage |
| --- | --- |
| Common.js | Import like this: `require('@apimatic/core')`. |
| ES Module | Import like this: `import { /* your imports */ } from '@apimatic/core'`. |
| Browsers | *Use script: `https://unpkg.com/@apimatic/core@VERSION/umd/core.js` |
| Modern Browsers (supports ESM and uses modern JS) | *Use script: `https://unpkg.com/@apimatic/core@VERSION/umd/core.esm.js` |

_* Don't forget to replace VERSION with the version number._

**Note**: We discourage importing files or modules directly from the package. These are likely to change in the future and should not be considered stable.