const { body, param, query } = require('express-validator');

// Validation for coupon ID parameter
const validateCouponId = [
  param('couponId')
    .isInt({ min: 1 })
    .withMessage('Coupon ID must be a positive integer')
];

// Validation for coupon code parameter
const validateCouponCode = [
  param('code')
    .trim()
    .isLength({ min: 3, max: 50 })
    .withMessage('Coupon code must be between 3 and 50 characters')
    .matches(/^[A-Z0-9]+$/)
    .withMessage('Coupon code can only contain uppercase letters and numbers')
];

// Validation for creating a new coupon
const validateCreateCoupon = [
  body('code')
    .trim()
    .isLength({ min: 3, max: 50 })
    .withMessage('Coupon code must be between 3 and 50 characters')
    .matches(/^[A-Z0-9]+$/i)
    .withMessage('Coupon code can only contain letters and numbers')
    .customSanitizer(value => value.toUpperCase()),
    
  body('name')
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('Coupon name must be between 1 and 100 characters')
    .notEmpty()
    .withMessage('Coupon name is required'),
    
  body('description')
    .optional()
    .trim()
    .isLength({ max: 500 })
    .withMessage('Description cannot exceed 500 characters'),
    
  body('type')
    .isIn(['percentage', 'fixed_amount', 'free_shipping'])
    .withMessage('Type must be percentage, fixed_amount, or free_shipping'),
    
  body('value')
    .isFloat({ min: 0 })
    .withMessage('Value must be a positive number')
    .custom((value, { req }) => {
      if (req.body.type === 'percentage' && value > 100) {
        throw new Error('Percentage value cannot exceed 100');
      }
      return true;
    }),
    
  body('minimumAmount')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Minimum amount must be a positive number'),
    
  body('maximumAmount')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Maximum amount must be a positive number')
    .custom((value, { req }) => {
      if (value && req.body.minimumAmount && parseFloat(value) <= parseFloat(req.body.minimumAmount)) {
        throw new Error('Maximum amount must be greater than minimum amount');
      }
      return true;
    }),
    
  body('usageLimit')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Usage limit must be a positive integer'),
    
  body('userUsageLimit')
    .optional()
    .isInt({ min: 1 })
    .withMessage('User usage limit must be a positive integer'),
    
  body('isActive')
    .optional()
    .isBoolean()
    .withMessage('isActive must be a boolean value'),
    
  body('startsAt')
    .optional()
    .isISO8601()
    .withMessage('Start date must be a valid ISO 8601 date')
    .custom((value, { req }) => {
      if (value && req.body.expiresAt) {
        const startDate = new Date(value);
        const endDate = new Date(req.body.expiresAt);
        if (startDate >= endDate) {
          throw new Error('Start date must be before expiration date');
        }
      }
      return true;
    }),
    
  body('expiresAt')
    .optional()
    .isISO8601()
    .withMessage('Expiration date must be a valid ISO 8601 date')
    .custom((value) => {
      if (value) {
        const expirationDate = new Date(value);
        const now = new Date();
        if (expirationDate <= now) {
          throw new Error('Expiration date must be in the future');
        }
      }
      return true;
    }),
    
  body('applicableProducts')
    .optional()
    .isArray()
    .withMessage('Applicable products must be an array')
    .custom((products) => {
      if (products && !products.every(id => Number.isInteger(id) && id > 0)) {
        throw new Error('All product IDs must be positive integers');
      }
      return true;
    }),
    
  body('applicableCategories')
    .optional()
    .isArray()
    .withMessage('Applicable categories must be an array')
    .custom((categories) => {
      if (categories && !categories.every(id => Number.isInteger(id) && id > 0)) {
        throw new Error('All category IDs must be positive integers');
      }
      return true;
    })
];

// Validation for updating a coupon
const validateUpdateCoupon = [
  param('couponId')
    .isInt({ min: 1 })
    .withMessage('Coupon ID must be a positive integer'),
    
  body('code')
    .optional()
    .trim()
    .isLength({ min: 3, max: 50 })
    .withMessage('Coupon code must be between 3 and 50 characters')
    .matches(/^[A-Z0-9]+$/i)
    .withMessage('Coupon code can only contain letters and numbers')
    .customSanitizer(value => value ? value.toUpperCase() : value),
    
  body('name')
    .optional()
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('Coupon name must be between 1 and 100 characters'),
    
  body('description')
    .optional()
    .trim()
    .isLength({ max: 500 })
    .withMessage('Description cannot exceed 500 characters'),
    
  body('type')
    .optional()
    .isIn(['percentage', 'fixed_amount', 'free_shipping'])
    .withMessage('Type must be percentage, fixed_amount, or free_shipping'),
    
  body('value')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Value must be a positive number')
    .custom((value, { req }) => {
      if (value && req.body.type === 'percentage' && value > 100) {
        throw new Error('Percentage value cannot exceed 100');
      }
      return true;
    }),
    
  body('minimumAmount')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Minimum amount must be a positive number'),
    
  body('maximumAmount')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Maximum amount must be a positive number'),
    
  body('usageLimit')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Usage limit must be a positive integer'),
    
  body('userUsageLimit')
    .optional()
    .isInt({ min: 1 })
    .withMessage('User usage limit must be a positive integer'),
    
  body('isActive')
    .optional()
    .isBoolean()
    .withMessage('isActive must be a boolean value'),
    
  body('startsAt')
    .optional()
    .isISO8601()
    .withMessage('Start date must be a valid ISO 8601 date'),
    
  body('expiresAt')
    .optional()
    .isISO8601()
    .withMessage('Expiration date must be a valid ISO 8601 date'),
    
  body('applicableProducts')
    .optional()
    .isArray()
    .withMessage('Applicable products must be an array'),
    
  body('applicableCategories')
    .optional()
    .isArray()
    .withMessage('Applicable categories must be an array')
];

// Validation for bulk operations
const validateBulkCouponOperation = [
  body('couponIds')
    .isArray({ min: 1 })
    .withMessage('Coupon IDs must be a non-empty array')
    .custom((couponIds) => {
      if (!couponIds.every(id => Number.isInteger(id) && id > 0)) {
        throw new Error('All coupon IDs must be positive integers');
      }
      return true;
    }),
    
  body('action')
    .isIn(['activate', 'deactivate', 'delete'])
    .withMessage('Action must be activate, deactivate, or delete')
];

// Validation for coupon listing query parameters
const validateCouponQuery = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
    
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),
    
  query('search')
    .optional()
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('Search term must be between 1 and 100 characters'),
    
  query('type')
    .optional()
    .isIn(['all', 'percentage', 'fixed_amount', 'free_shipping'])
    .withMessage('Type filter must be all, percentage, fixed_amount, or free_shipping'),
    
  query('status')
    .optional()
    .isIn(['all', 'active', 'inactive', 'expired'])
    .withMessage('Status filter must be all, active, inactive, or expired'),
    
  query('sortBy')
    .optional()
    .isIn(['createdAt', 'code', 'name', 'type', 'value', 'usageCount', 'expiresAt'])
    .withMessage('Sort field must be createdAt, code, name, type, value, usageCount, or expiresAt'),
    
  query('sortOrder')
    .optional()
    .isIn(['ASC', 'DESC'])
    .withMessage('Sort order must be ASC or DESC'),
    
  query('dateFrom')
    .optional()
    .isISO8601()
    .withMessage('Date from must be a valid ISO 8601 date'),
    
  query('dateTo')
    .optional()
    .isISO8601()
    .withMessage('Date to must be a valid ISO 8601 date'),
    
  query('includeCreator')
    .optional()
    .isBoolean()
    .withMessage('Include creator must be a boolean value')
];

// Validation for coupon code validation endpoint
const validateCouponCodeValidation = [
  param('code')
    .trim()
    .isLength({ min: 3, max: 50 })
    .withMessage('Coupon code must be between 3 and 50 characters'),
    
  query('subtotal')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Subtotal must be a positive number'),
    
  query('userId')
    .optional()
    .isInt({ min: 1 })
    .withMessage('User ID must be a positive integer')
];

// Role-based access control for coupon management
const requireCouponManagementAccess = (req, res, next) => {
  if (!['admin', 'manager', 'super_admin'].includes(req.user.role)) {
    return res.status(403).json({
      success: false,
      message: 'Coupon management access required'
    });
  }
  next();
};

// Middleware to check if coupon exists
const checkCouponExists = async (req, res, next) => {
  try {
    const { couponId } = req.params;
    const { Coupon } = require('../models');
    
    const coupon = await Coupon.findByPk(couponId);
    if (!coupon) {
      return res.status(404).json({
        success: false,
        message: 'Coupon not found'
      });
    }
    
    req.coupon = coupon;
    next();
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error checking coupon existence',
      error: error.message
    });
  }
};

module.exports = {
  validateCouponId,
  validateCouponCode,
  validateCreateCoupon,
  validateUpdateCoupon,
  validateBulkCouponOperation,
  validateCouponQuery,
  validateCouponCodeValidation,
  requireCouponManagementAccess,
  checkCouponExists
};
