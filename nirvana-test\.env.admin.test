# Nirvana Organics - Admin Server Test Configuration
# Admin Panel Test Environment

# Environment
NODE_ENV=test

# Server Configuration
PORT=3001
FRONTEND_URL=https://test.shopnirvanaorganics.com/admin
BACKEND_URL=https://test.shopnirvanaorganics.com

# Database Configuration (Test)
DB_HOST=srv1921.hstgr.io
DB_PORT=3306
DB_NAME=u106832845_nirvana
DB_USER=u106832845_root
DB_PASSWORD=Hrishikesh@0912
DB_SSL=true
DB_POOL_MAX=6
DB_POOL_MIN=2

# CORS Configuration (Admin test specific - path-based)
CORS_ORIGIN=https://test.shopnirvanaorganics.com

# Security Configuration (Test admin)
JWT_SECRET=4d18f78eb794845484ea3fa05759c0e4412b0987c054ce0e2009050f3ef4fefc
JWT_REFRESH_SECRET=zvBFWhW0eP3uNAp0t0KLIRq0owdq1QX82OmLLXyMpSY=
JWT_EXPIRES_IN=30m
JWT_REFRESH_EXPIRES_IN=2h
BCRYPT_ROUNDS=12

# Admin Rate Limiting (Test)
RATE_LIMIT_WINDOW_MS=300000
ADMIN_RATE_LIMIT_MAX_REQUESTS=200

# Admin Security (Test)
ADMIN_SECURITY_MODE=true
ADMIN_IP_WHITELIST=

# Email Configuration (Test admin)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
SMTP_SECURE=false
EMAIL_USER=<EMAIL>
EMAIL_PASS=aowu yxxq xeyj qbpe
EMAIL_FROM=<EMAIL>
EMAIL_FROM_NAME=Shop Nirvana Organics

# Email Addresses (Test Admin Environment)
EMAIL_ORDERS=<EMAIL>
EMAIL_ORDERS_USER=<EMAIL>
EMAIL_ORDERS_PASS=wiba ieco dxth xdyn
EMAIL_ORDERS_HOST=smtp.gmail.com
EMAIL_ORDERS_PORT=587

EMAIL_SUPPORT=<EMAIL>
EMAIL_SUPPORT_USER=<EMAIL>
EMAIL_SUPPORT_PASS=gqfh cyls ryuv yind
EMAIL_SUPPORT_HOST=smtp.gmail.com
EMAIL_SUPPORT_PORT=587

EMAIL_CUSTOMER_SERVICE=<EMAIL>
EMAIL_NO_REPLY=<EMAIL>
EMAIL_ADMIN=<EMAIL>

# VAPID Configuration (Test Admin)
VAPID_PUBLIC_KEY=BGSeufHC3qXpetuNrZmPuQAp34DAHFUm3l6ZWoqGYKLY252IwUpPiZe9PjcVlsueX3JTJ8sBhZiJ49rnZuI73kQ
VAPID_PRIVATE_KEY=FZ4eME5YyL0IIF4SZsSW0_PZ8ISMM5mtfGFKPF7a3AQ
VAPID_EMAIL=mailto:<EMAIL>

# Payment Configuration (Test/Sandbox)
SQUARE_ENVIRONMENT=sandbox
SQUARE_APPLICATION_ID=*************************************
SQUARE_ACCESS_TOKEN=****************************************************************
SQUARE_WEBHOOK_SIGNATURE_KEY=ss2HK0tSkM5sm_e3qR42Dg
SQUARE_LOCATION_ID=LWBM41BAWMRQS

# Square OAuth Configuration (Test/Sandbox - Admin)
SQUARE_OAUTH_CLIENT_ID=*************************************
SQUARE_OAUTH_CLIENT_SECRET=sandbox-sq0csb-wPrc4mMkN8ZA2nz5md75DlDNdy2BVUG7_9l9G2zjnzc
SQUARE_OAUTH_CALLBACK_URL=https://test.shopnirvanaorganics.com/admin/api/auth/square/callback

# Google OAuth (Test admin)
GOOGLE_CLIENT_ID=53561266132-fn7bptsn5hr9jpfim2hp77845fjtl6cn.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=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
GOOGLE_OAUTH_CALLBACK_URL=https://test.shopnirvanaorganics.com/admin/api/auth/google/callback

# File Upload Configuration (Test admin)
UPLOAD_DIR=uploads
MAX_FILE_SIZE=50485760
ALLOWED_FILE_TYPES=jpg,jpeg,png,gif,pdf,doc,docx,xlsx,csv

# Logging Configuration (Test admin)
LOG_LEVEL=debug
LOG_FILE=logs/admin-test.log
ERROR_LOG_FILE=logs/admin-test-error.log
AUDIT_LOG_FILE=logs/admin-test-audit.log

# Cache Configuration
REDIS_URL=redis://localhost:6379
CACHE_TTL=900

# Analytics Configuration (Test)
GOOGLE_ANALYTICS_ID=your-test-google-analytics-id

# Social Media Configuration (Test admin)
FACEBOOK_APP_ID=your-test-facebook-app-id
FACEBOOK_APP_SECRET=your-test-facebook-app-secret
INSTAGRAM_ACCESS_TOKEN=your-test-instagram-access-token
TWITTER_API_KEY=your-test-twitter-api-key
TWITTER_API_SECRET=your-test-twitter-api-secret

# Shipping Configuration
USPS_USER_ID=your-test-usps-user-id
USPS_API_URL=https://secure.shippingapis.com/ShippingAPI.dll

# Session Configuration (Test admin)
SESSION_SECRET=your-test-admin-session-secret
SESSION_COOKIE_SECURE=true
SESSION_COOKIE_HTTPONLY=true
SESSION_COOKIE_SAMESITE=strict
SESSION_TIMEOUT=3600000

# API Keys
ENCRYPTION_KEY=744c05e48632e7a4aaa86ca5f85e733fff59dfcfc37bf5d43a72e8462df0f89d
API_KEY_SECRET=slji4ImLCC4EhyyEYZPT49T2EZAGmg83gBJae0Rkbeg=

# Monitoring
HEALTH_CHECK_INTERVAL=30000
PERFORMANCE_MONITORING=true

# Debug Configuration
DEBUG_MODE=true
ENABLE_CORS_DEBUG=true
SQL_LOGGING=true

# Admin Features (Test)
ENABLE_DATA_EXPORT=true
ENABLE_BULK_OPERATIONS=true
ENABLE_SYSTEM_MAINTENANCE=true

# Backup Configuration
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 4 * * *
BACKUP_RETENTION_DAYS=14

# Two-Factor Authentication (Disabled for test)
ENABLE_2FA=false
TOTP_ISSUER=Nirvana Organics Test Admin
TOTP_WINDOW=2
