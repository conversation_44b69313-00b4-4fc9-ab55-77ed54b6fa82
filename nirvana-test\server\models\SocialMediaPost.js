const { DataTypes } = require('sequelize');
const { sequelize } = require('./database');

/**
 * Social Media Post Model
 * Stores posts created and scheduled for social media platforms
 */
const SocialMediaPost = sequelize.define('SocialMediaPost', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  
  // Post content
  content: {
    type: DataTypes.TEXT,
    allowNull: false,
    field: 'content'
  },
  
  // Media attachments
  mediaUrls: {
    type: DataTypes.JSON,
    allowNull: true,
    field: 'media_urls',
    comment: 'Array of media URLs (images, videos)'
  },
  
  mediaType: {
    type: DataTypes.ENUM('none', 'image', 'video', 'carousel', 'story'),
    defaultValue: 'none',
    field: 'media_type'
  },
  
  // Platform targeting
  platforms: {
    type: DataTypes.JSON,
    allowNull: false,
    field: 'platforms',
    comment: 'Array of platforms to post to'
  },
  
  // Scheduling
  scheduledAt: {
    type: DataTypes.DATE,
    allowNull: true,
    field: 'scheduled_at'
  },
  
  publishedAt: {
    type: DataTypes.DATE,
    allowNull: true,
    field: 'published_at'
  },
  
  // Post status
  status: {
    type: DataTypes.ENUM('draft', 'scheduled', 'publishing', 'published', 'failed', 'cancelled'),
    defaultValue: 'draft',
    field: 'status'
  },
  
  // Platform-specific post IDs
  platformPostIds: {
    type: DataTypes.JSON,
    allowNull: true,
    field: 'platform_post_ids',
    comment: 'Object mapping platform to post ID'
  },
  
  // Engagement metrics
  totalLikes: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    field: 'total_likes'
  },
  
  totalComments: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    field: 'total_comments'
  },
  
  totalShares: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    field: 'total_shares'
  },
  
  totalReach: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    field: 'total_reach'
  },
  
  totalImpressions: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    field: 'total_impressions'
  },
  
  // Platform-specific metrics
  platformMetrics: {
    type: DataTypes.JSON,
    allowNull: true,
    field: 'platform_metrics',
    comment: 'Detailed metrics per platform'
  },
  
  // Hashtags and mentions
  hashtags: {
    type: DataTypes.JSON,
    allowNull: true,
    field: 'hashtags',
    comment: 'Array of hashtags used'
  },
  
  mentions: {
    type: DataTypes.JSON,
    allowNull: true,
    field: 'mentions',
    comment: 'Array of user mentions'
  },
  
  // Campaign and categorization
  campaignId: {
    type: DataTypes.STRING,
    allowNull: true,
    field: 'campaign_id'
  },
  
  category: {
    type: DataTypes.STRING,
    allowNull: true,
    field: 'category'
  },
  
  tags: {
    type: DataTypes.JSON,
    allowNull: true,
    field: 'tags',
    comment: 'Internal tags for organization'
  },
  
  // Error tracking
  lastError: {
    type: DataTypes.TEXT,
    allowNull: true,
    field: 'last_error'
  },
  
  retryCount: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    field: 'retry_count'
  },
  
  maxRetries: {
    type: DataTypes.INTEGER,
    defaultValue: 3,
    field: 'max_retries'
  },
  
  // Approval workflow
  requiresApproval: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    field: 'requires_approval'
  },
  
  approvedBy: {
    type: DataTypes.UUID,
    allowNull: true,
    field: 'approved_by',
    references: {
      model: 'users',
      key: 'id'
    }
  },
  
  approvedAt: {
    type: DataTypes.DATE,
    allowNull: true,
    field: 'approved_at'
  },
  
  // Creator information
  createdBy: {
    type: DataTypes.UUID,
    allowNull: false,
    field: 'created_by',
    references: {
      model: 'users',
      key: 'id'
    }
  },
  
  // Analytics tracking
  lastMetricsUpdate: {
    type: DataTypes.DATE,
    allowNull: true,
    field: 'last_metrics_update'
  },
  
  // Post settings
  settings: {
    type: DataTypes.JSON,
    allowNull: true,
    field: 'settings',
    comment: 'Platform-specific settings and options'
  }
}, {
  tableName: 'social_media_posts',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      fields: ['status']
    },
    {
      fields: ['scheduled_at']
    },
    {
      fields: ['published_at']
    },
    {
      fields: ['created_by']
    },
    {
      fields: ['campaign_id']
    },
    {
      fields: ['category']
    },
    {
      fields: ['created_at']
    }
  ]
});

// Instance methods
SocialMediaPost.prototype.isScheduled = function() {
  return this.status === 'scheduled' && this.scheduledAt && new Date() >= this.scheduledAt;
};

SocialMediaPost.prototype.canRetry = function() {
  return this.status === 'failed' && this.retryCount < this.maxRetries;
};

SocialMediaPost.prototype.updateMetrics = function(platformMetrics) {
  // Aggregate metrics from all platforms
  let totalLikes = 0;
  let totalComments = 0;
  let totalShares = 0;
  let totalReach = 0;
  let totalImpressions = 0;

  Object.values(platformMetrics).forEach(metrics => {
    totalLikes += metrics.likes || 0;
    totalComments += metrics.comments || 0;
    totalShares += metrics.shares || 0;
    totalReach += metrics.reach || 0;
    totalImpressions += metrics.impressions || 0;
  });

  this.totalLikes = totalLikes;
  this.totalComments = totalComments;
  this.totalShares = totalShares;
  this.totalReach = totalReach;
  this.totalImpressions = totalImpressions;
  this.platformMetrics = platformMetrics;
  this.lastMetricsUpdate = new Date();

  return this.save();
};

SocialMediaPost.prototype.markAsPublished = function(platformPostIds) {
  this.status = 'published';
  this.publishedAt = new Date();
  this.platformPostIds = { ...this.platformPostIds, ...platformPostIds };
  return this.save();
};

SocialMediaPost.prototype.markAsFailed = function(error) {
  this.status = 'failed';
  this.lastError = error.message || error;
  this.retryCount += 1;
  return this.save();
};

SocialMediaPost.prototype.approve = function(userId) {
  this.approvedBy = userId;
  this.approvedAt = new Date();
  if (this.status === 'draft') {
    this.status = this.scheduledAt ? 'scheduled' : 'publishing';
  }
  return this.save();
};

// Class methods
SocialMediaPost.findScheduledPosts = function() {
  return this.findAll({
    where: {
      status: 'scheduled',
      scheduledAt: {
        [sequelize.Sequelize.Op.lte]: new Date()
      }
    },
    order: [['scheduled_at', 'ASC']]
  });
};

SocialMediaPost.findByStatus = function(status) {
  return this.findAll({
    where: { status },
    order: [['created_at', 'DESC']]
  });
};

SocialMediaPost.findByCampaign = function(campaignId) {
  return this.findAll({
    where: { campaignId },
    order: [['created_at', 'DESC']]
  });
};

SocialMediaPost.findRecentPosts = function(limit = 10) {
  return this.findAll({
    where: {
      status: 'published'
    },
    order: [['published_at', 'DESC']],
    limit
  });
};

SocialMediaPost.getAnalytics = function(startDate, endDate) {
  return this.findAll({
    where: {
      status: 'published',
      publishedAt: {
        [sequelize.Sequelize.Op.between]: [startDate, endDate]
      }
    },
    attributes: [
      'id',
      'content',
      'platforms',
      'publishedAt',
      'totalLikes',
      'totalComments',
      'totalShares',
      'totalReach',
      'totalImpressions',
      'platformMetrics'
    ],
    order: [['published_at', 'DESC']]
  });
};

module.exports = SocialMediaPost;
