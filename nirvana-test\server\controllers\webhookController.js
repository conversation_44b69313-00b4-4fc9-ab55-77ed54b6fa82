const SquareService = require('../services/squareService');
const crypto = require('crypto');

// Handle Square webhooks
const handleSquareWebhook = async (req, res) => {
  try {
    const signature = req.headers['x-square-signature'];
    const body = JSON.stringify(req.body);
    
    // Verify webhook signature
    if (!verifySquareSignature(signature, body)) {
      console.warn('Invalid Square webhook signature');
      return res.status(401).json({
        success: false,
        message: 'Invalid signature'
      });
    }

    const { type, data } = req.body;
    
    console.log(`Processing Square webhook: ${type}`);
    
    // Process the webhook event
    const result = await SquareService.processWebhook(type, data);
    
    if (result.success) {
      res.json({
        success: true,
        message: result.message || 'Webhook processed successfully'
      });
    } else {
      console.error('Webhook processing failed:', result.error);
      res.status(500).json({
        success: false,
        message: 'Webhook processing failed',
        error: result.error
      });
    }

  } catch (error) {
    console.error('Square webhook error:', error);
    res.status(500).json({
      success: false,
      message: 'Webhook processing failed',
      error: error.message
    });
  }
};

// Verify Square webhook signature
const verifySquareSignature = (signature, body) => {
  try {
    const webhookSignatureKey = process.env.SQUARE_WEBHOOK_SIGNATURE_KEY;
    
    if (!webhookSignatureKey) {
      console.warn('Square webhook signature key not configured');
      return true; // Allow in development
    }

    // Square uses HMAC-SHA256 for webhook signatures
    const expectedSignature = crypto
      .createHmac('sha256', webhookSignatureKey)
      .update(body)
      .digest('base64');

    return signature === expectedSignature;
  } catch (error) {
    console.error('Signature verification error:', error);
    return false;
  }
};

// Handle payment status updates (for manual testing)
const updatePaymentStatus = async (req, res) => {
  try {
    const { orderId, status, paymentId } = req.body;

    if (!orderId || !status) {
      return res.status(400).json({
        success: false,
        message: 'Order ID and status are required'
      });
    }

    const { Order } = require('../models');
    const order = await Order.findOne({ where: { orderNumber: orderId } });

    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'Order not found'
      });
    }

    let paymentStatus = 'pending';
    let orderStatus = order.status;

    switch (status.toLowerCase()) {
      case 'completed':
      case 'paid':
        paymentStatus = 'paid';
        if (order.status === 'pending') {
          orderStatus = 'confirmed';
        }
        break;
      case 'failed':
      case 'declined':
        paymentStatus = 'failed';
        orderStatus = 'cancelled';
        break;
      case 'refunded':
        paymentStatus = 'refunded';
        orderStatus = 'refunded';
        break;
      default:
        paymentStatus = 'pending';
    }

    // Update status history
    const statusHistory = order.statusHistory || [];
    statusHistory.push({
      status: orderStatus,
      paymentStatus,
      timestamp: new Date(),
      note: `Payment status manually updated to ${status}${paymentId ? ` - Payment ID: ${paymentId}` : ''}`
    });

    await order.update({
      status: orderStatus,
      paymentStatus,
      squarePaymentId: paymentId || order.squarePaymentId,
      statusHistory
    });

    res.json({
      success: true,
      message: 'Payment status updated successfully',
      data: {
        orderId: order.orderNumber,
        status: orderStatus,
        paymentStatus
      }
    });

  } catch (error) {
    console.error('Update payment status error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update payment status',
      error: error.message
    });
  }
};

// Get payment status for an order
const getPaymentStatus = async (req, res) => {
  try {
    const { orderId } = req.params;

    const { Order } = require('../models');
    const order = await Order.findOne({ 
      where: { orderNumber: orderId },
      attributes: ['id', 'orderNumber', 'status', 'paymentStatus', 'squarePaymentId', 'total', 'statusHistory']
    });

    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'Order not found'
      });
    }

    res.json({
      success: true,
      data: {
        orderId: order.orderNumber,
        status: order.status,
        paymentStatus: order.paymentStatus,
        squarePaymentId: order.squarePaymentId,
        total: order.total,
        statusHistory: order.statusHistory
      }
    });

  } catch (error) {
    console.error('Get payment status error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get payment status',
      error: error.message
    });
  }
};

// Test webhook endpoint (for development)
const testWebhook = async (req, res) => {
  try {
    const { type, orderId, status } = req.body;

    if (!type || !orderId || !status) {
      return res.status(400).json({
        success: false,
        message: 'Type, orderId, and status are required'
      });
    }

    // Simulate webhook data
    const webhookData = {
      type,
      data: {
        object: {
          payment: {
            id: `test_payment_${Date.now()}`,
            status: status.toUpperCase(),
            referenceId: orderId,
            amountMoney: {
              amount: 1000, // $10.00 in cents
              currency: 'USD'
            }
          }
        }
      }
    };

    const result = await SquareService.processWebhook(webhookData.type, webhookData.data);

    res.json({
      success: true,
      message: 'Test webhook processed',
      result
    });

  } catch (error) {
    console.error('Test webhook error:', error);
    res.status(500).json({
      success: false,
      message: 'Test webhook failed',
      error: error.message
    });
  }
};

module.exports = {
  handleSquareWebhook,
  updatePaymentStatus,
  getPaymentStatus,
  testWebhook
};
