/**
 * Social Media Controller
 * Handles public social media content and statistics
 */
class SocialMediaController {
  /**
   * Get all social media posts
   * @route GET /api/social-media/posts
   * @access Public
   */
  static async getPosts(req, res) {
    try {
      const { platform, limit = 20, offset = 0 } = req.query;
      
      // Mock social media posts data
      // In a real implementation, this would fetch from a database or external APIs
      const mockPosts = [
        {
          id: 'fb_001',
          platform: 'facebook',
          title: 'New Premium Cannabis Products Available',
          content: 'Discover our latest collection of premium cannabis products, carefully curated for quality and potency. Visit our store today!',
          imageUrl: '/images/social/facebook-post-1.jpg',
          url: 'https://facebook.com/nirvanaorganics/posts/123',
          publishedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
          engagement: {
            likes: 245,
            comments: 18,
            shares: 32
          },
          author: 'Nirvana Organics'
        },
        {
          id: 'ig_001',
          platform: 'instagram',
          title: 'Behind the Scenes: Quality Testing',
          content: 'Take a look behind the scenes at our quality testing process. Every product goes through rigorous testing to ensure safety and potency. #QualityFirst #Cannabis #Testing',
          imageUrl: '/images/social/instagram-post-1.jpg',
          url: 'https://instagram.com/p/nirvanaorganics123',
          publishedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
          engagement: {
            likes: 189,
            comments: 24,
            shares: 15
          },
          author: 'Nirvana Organics'
        },
        {
          id: 'yt_001',
          platform: 'youtube',
          title: 'Cannabis Education: Understanding Terpenes',
          content: 'Learn about terpenes and how they affect your cannabis experience. Our expert explains the science behind these aromatic compounds.',
          imageUrl: '/images/social/youtube-thumbnail-1.jpg',
          videoUrl: 'https://youtube.com/watch?v=example123',
          url: 'https://youtube.com/watch?v=example123',
          publishedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
          engagement: {
            likes: 156,
            comments: 42,
            shares: 28,
            views: 2340
          },
          author: 'Nirvana Organics'
        },
        {
          id: 'tt_001',
          platform: 'tiktok',
          title: 'Quick Cannabis Facts',
          content: 'Did you know? Cannabis has over 100 different cannabinoids! Learn more about the science of cannabis with us. #CannabisFacts #Education',
          videoUrl: 'https://tiktok.com/@nirvanaorganics/video/123',
          url: 'https://tiktok.com/@nirvanaorganics/video/123',
          publishedAt: new Date(Date.now() - 12 * 60 * 60 * 1000).toISOString(),
          engagement: {
            likes: 892,
            comments: 67,
            shares: 134,
            views: 12450
          },
          author: 'Nirvana Organics'
        },
        {
          id: 'md_001',
          platform: 'medium',
          title: 'The Future of Cannabis Wellness',
          content: 'Exploring the evolving landscape of cannabis wellness and its potential impact on healthcare. A comprehensive look at current research and future possibilities.',
          url: 'https://medium.com/@nirvanaorganics/future-cannabis-wellness-123',
          publishedAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
          engagement: {
            likes: 78,
            comments: 12,
            shares: 23
          },
          author: 'Dr. Cannabis Expert'
        }
      ];
      
      // Filter by platform if specified
      let filteredPosts = platform 
        ? mockPosts.filter(post => post.platform === platform)
        : mockPosts;
      
      // Apply pagination
      const startIndex = parseInt(offset);
      const endIndex = startIndex + parseInt(limit);
      const paginatedPosts = filteredPosts.slice(startIndex, endIndex);
      
      res.json({
        success: true,
        data: paginatedPosts,
        pagination: {
          total: filteredPosts.length,
          limit: parseInt(limit),
          offset: parseInt(offset),
          hasMore: endIndex < filteredPosts.length
        }
      });
    } catch (error) {
      console.error('Get social media posts error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch social media posts',
        error: error.message
      });
    }
  }

  /**
   * Get social media platform statistics
   * @route GET /api/social-media/stats
   * @access Public
   */
  static async getStats(req, res) {
    try {
      // Mock social media statistics
      // In a real implementation, this would fetch from external APIs or cached data
      const mockStats = [
        {
          platform: 'facebook',
          followers: 12500,
          posts: 156,
          engagement: 4.2,
          growth: 8.5,
          url: 'https://facebook.com/nirvanaorganics',
          icon: '📘',
          color: 'bg-blue-600'
        },
        {
          platform: 'instagram',
          followers: 8900,
          posts: 234,
          engagement: 6.8,
          growth: 12.3,
          url: 'https://instagram.com/nirvanaorganics',
          icon: '📷',
          color: 'bg-gradient-to-r from-purple-500 to-pink-500'
        },
        {
          platform: 'youtube',
          followers: 3400,
          posts: 45,
          engagement: 8.1,
          growth: 15.7,
          url: 'https://youtube.com/@nirvanaorganics',
          icon: '📺',
          color: 'bg-red-600'
        },
        {
          platform: 'tiktok',
          followers: 15600,
          posts: 89,
          engagement: 12.4,
          growth: 25.8,
          url: 'https://tiktok.com/@nirvanaorganics',
          icon: '🎵',
          color: 'bg-black'
        },
        {
          platform: 'medium',
          followers: 1200,
          posts: 23,
          engagement: 3.6,
          growth: 5.2,
          url: 'https://medium.com/@nirvanaorganics',
          icon: '📝',
          color: 'bg-gray-800'
        }
      ];
      
      res.json({
        success: true,
        data: mockStats
      });
    } catch (error) {
      console.error('Get social media stats error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch social media statistics',
        error: error.message
      });
    }
  }

  /**
   * Get posts from a specific platform
   * @route GET /api/social-media/platforms/:platform/posts
   * @access Public
   */
  static async getPlatformPosts(req, res) {
    try {
      const { platform } = req.params;
      const { limit = 20, offset = 0 } = req.query;
      
      // Validate platform
      const validPlatforms = ['facebook', 'instagram', 'youtube', 'tiktok', 'medium'];
      if (!validPlatforms.includes(platform)) {
        return res.status(400).json({
          success: false,
          message: 'Invalid platform. Must be one of: ' + validPlatforms.join(', ')
        });
      }
      
      // Use the same mock data but filter by platform
      req.query.platform = platform;
      return this.getPosts(req, res);
    } catch (error) {
      console.error('Get platform posts error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch platform posts',
        error: error.message
      });
    }
  }

  /**
   * Get platform-specific statistics
   * @route GET /api/social-media/platforms/:platform/stats
   * @access Public
   */
  static async getPlatformStats(req, res) {
    try {
      const { platform } = req.params;
      
      // Get all stats first
      const statsResponse = await this.getStats({ query: {} }, {
        json: (data) => data
      });
      
      const platformStats = statsResponse.data.find(stat => stat.platform === platform);
      
      if (!platformStats) {
        return res.status(404).json({
          success: false,
          message: 'Platform not found'
        });
      }
      
      res.json({
        success: true,
        data: platformStats
      });
    } catch (error) {
      console.error('Get platform stats error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch platform statistics',
        error: error.message
      });
    }
  }

  /**
   * Get engagement metrics for a specific post
   * @route GET /api/social-media/posts/:postId/engagement
   * @access Public
   */
  static async getPostEngagement(req, res) {
    try {
      const { postId } = req.params;
      
      // Mock engagement data
      const mockEngagement = {
        postId,
        totalEngagement: 245,
        likes: 189,
        comments: 32,
        shares: 24,
        views: 1250,
        engagementRate: 4.2,
        reachEstimate: 5800,
        demographics: {
          ageGroups: {
            '18-24': 15,
            '25-34': 35,
            '35-44': 28,
            '45-54': 15,
            '55+': 7
          },
          gender: {
            male: 52,
            female: 45,
            other: 3
          }
        },
        timeline: [
          { hour: 0, engagement: 12 },
          { hour: 1, engagement: 8 },
          { hour: 2, engagement: 15 },
          { hour: 6, engagement: 45 },
          { hour: 12, engagement: 89 },
          { hour: 18, engagement: 76 }
        ]
      };
      
      res.json({
        success: true,
        data: mockEngagement
      });
    } catch (error) {
      console.error('Get post engagement error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch post engagement',
        error: error.message
      });
    }
  }

  /**
   * Search social media posts
   * @route GET /api/social-media/posts/search
   * @access Public
   */
  static async searchPosts(req, res) {
    try {
      const { keyword, platform, dateFrom, dateTo, limit = 20 } = req.query;
      
      // Get all posts first
      const allPostsResponse = await this.getPosts({ query: { limit: 1000 } }, {
        json: (data) => data
      });
      
      let filteredPosts = allPostsResponse.data;
      
      // Filter by keyword
      if (keyword) {
        const searchTerm = keyword.toLowerCase();
        filteredPosts = filteredPosts.filter(post => 
          post.title.toLowerCase().includes(searchTerm) ||
          post.content.toLowerCase().includes(searchTerm)
        );
      }
      
      // Filter by platform
      if (platform) {
        filteredPosts = filteredPosts.filter(post => post.platform === platform);
      }
      
      // Filter by date range
      if (dateFrom || dateTo) {
        filteredPosts = filteredPosts.filter(post => {
          const postDate = new Date(post.publishedAt);
          if (dateFrom && postDate < new Date(dateFrom)) return false;
          if (dateTo && postDate > new Date(dateTo)) return false;
          return true;
        });
      }
      
      // Apply limit
      const limitedPosts = filteredPosts.slice(0, parseInt(limit));
      
      res.json({
        success: true,
        data: limitedPosts,
        searchParams: { keyword, platform, dateFrom, dateTo },
        totalResults: filteredPosts.length
      });
    } catch (error) {
      console.error('Search posts error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to search posts',
        error: error.message
      });
    }
  }

  /**
   * Get trending hashtags and topics
   * @route GET /api/social-media/trending
   * @access Public
   */
  static async getTrendingTopics(req, res) {
    try {
      const { platform } = req.query;
      
      // Mock trending data
      const mockTrending = {
        hashtags: [
          { tag: '#Cannabis', count: 1250, growth: 15.2 },
          { tag: '#CBD', count: 890, growth: 8.7 },
          { tag: '#Wellness', count: 567, growth: 12.4 },
          { tag: '#Organic', count: 445, growth: 6.8 },
          { tag: '#Hemp', count: 334, growth: 9.1 }
        ],
        topics: [
          { topic: 'Cannabis Education', mentions: 234, sentiment: 'positive' },
          { topic: 'Product Reviews', mentions: 189, sentiment: 'positive' },
          { topic: 'Health Benefits', mentions: 156, sentiment: 'positive' },
          { topic: 'Legal Updates', mentions: 123, sentiment: 'neutral' },
          { topic: 'Growing Tips', mentions: 98, sentiment: 'positive' }
        ],
        platform: platform || 'all',
        lastUpdated: new Date().toISOString()
      };
      
      res.json({
        success: true,
        data: mockTrending
      });
    } catch (error) {
      console.error('Get trending topics error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch trending topics',
        error: error.message
      });
    }
  }
}

module.exports = SocialMediaController;
