#!/bin/bash
# Make script executable: chmod +x test-all-fixes.sh

# Nirvana Organics - Test All Fixes Script
# Comprehensive testing of all critical issue fixes

set -e

echo "🧪 Nirvana Organics - Test All Fixes"
echo "===================================="

# Color codes
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}✅ $1${NC}"
}

warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

error() {
    echo -e "${RED}❌ $1${NC}"
}

# Test counters
TESTS_PASSED=0
TESTS_FAILED=0
TOTAL_TESTS=0

run_test() {
    local test_name="$1"
    local test_command="$2"
    local expected_result="$3"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    
    echo ""
    log "Testing: $test_name"
    
    if eval "$test_command"; then
        if [ "$expected_result" = "success" ]; then
            success "$test_name - PASSED"
            TESTS_PASSED=$((TESTS_PASSED + 1))
        else
            error "$test_name - FAILED (expected failure but got success)"
            TESTS_FAILED=$((TESTS_FAILED + 1))
        fi
    else
        if [ "$expected_result" = "failure" ]; then
            success "$test_name - PASSED (expected failure)"
            TESTS_PASSED=$((TESTS_PASSED + 1))
        else
            error "$test_name - FAILED"
            TESTS_FAILED=$((TESTS_FAILED + 1))
        fi
    fi
}

# Test 1: PM2 Process Status
log "Test Suite 1: PM2 Process Status"
run_test "Main server online" "pm2 list | grep -q 'nirvana-backend-main-test.*online'" "success"
run_test "Admin server online" "pm2 list | grep -q 'nirvana-backend-admin-test.*online'" "success"

# Test 2: Database Connection and Tables
log "Test Suite 2: Database Connection and Tables"
run_test "Database connection test" "[ -f 'database-manager.js' ] && node database-manager.js test" "success"
run_test "Database tables check" "[ -f 'database-manager.js' ] && node database-manager.js check" "success"

# Test 3: Frontend Path Configuration
log "Test Suite 3: Frontend Path Configuration"
run_test "Main frontend directory exists" "[ -d '/var/www/nirvana-frontend-test/main' ]" "success"
run_test "Admin frontend directory exists" "[ -d '/var/www/nirvana-frontend-test/admin' ]" "success"
run_test "Admin HTML file exists" "[ -f '/var/www/nirvana-frontend-test/admin/admin.html' ]" "success"

# Test 4: Admin Assets
log "Test Suite 4: Admin Assets"
run_test "Admin JavaScript asset exists" "[ -f '/var/www/nirvana-frontend-test/admin/assets/admin-DitisM-I.js' ]" "success"
run_test "Admin CSS asset exists" "[ -f '/var/www/nirvana-frontend-test/admin/assets/admin-TY7ZtfqV.css' ]" "success"

# Test 5: HTTP Endpoints
log "Test Suite 5: HTTP Endpoints"
run_test "Main health endpoint" "curl -s -f https://test.shopnirvanaorganics.com/api/health > /dev/null" "success"
run_test "Admin health endpoint" "curl -s -f https://test.shopnirvanaorganics.com/admin/api/health > /dev/null" "success"
run_test "Admin frontend accessible" "curl -s -o /dev/null -w '%{http_code}' https://test.shopnirvanaorganics.com/admin/ | grep -q '200'" "success"

# Test 6: Admin Assets HTTP
log "Test Suite 6: Admin Assets HTTP"
run_test "Admin JavaScript HTTP 200" "curl -s -o /dev/null -w '%{http_code}' https://test.shopnirvanaorganics.com/admin/assets/admin-DitisM-I.js | grep -q '200'" "success"
run_test "Admin CSS HTTP 200" "curl -s -o /dev/null -w '%{http_code}' https://test.shopnirvanaorganics.com/admin/assets/admin-TY7ZtfqV.css | grep -q '200'" "success"

# Test 7: Environment Variables
log "Test Suite 7: Environment Variables"
run_test "NODE_ENV set to test" "[ '$NODE_ENV' = 'test' ] || grep -q 'NODE_ENV=test' .env.test" "success"
run_test "DB_HOST not localhost" "grep -q 'DB_HOST=srv1921.hstgr.io' .env.test" "success"

# Test 8: File Permissions
log "Test Suite 8: File Permissions"
run_test "Admin assets readable" "[ -r '/var/www/nirvana-frontend-test/admin/assets/admin-DitisM-I.js' ]" "success"
run_test "Admin HTML readable" "[ -r '/var/www/nirvana-frontend-test/admin/admin.html' ]" "success"

# Test 9: Server Logs (No Critical Errors)
log "Test Suite 9: Server Logs"
run_test "No ENOENT errors in admin logs" "! pm2 logs nirvana-backend-admin-test --lines 20 --nostream | grep -q 'ENOENT'" "success"
run_test "No IPv6 localhost errors" "! pm2 logs nirvana-backend-main-test --lines 20 --nostream | grep -q '::1:3306'" "success"

# Test Results Summary
echo ""
echo "📊 TEST RESULTS SUMMARY"
echo "======================="
echo "Total Tests: $TOTAL_TESTS"
echo "Passed: $TESTS_PASSED"
echo "Failed: $TESTS_FAILED"

if [ $TESTS_FAILED -eq 0 ]; then
    success "🎉 ALL TESTS PASSED!"
    echo ""
    echo "✅ Critical Issues Status:"
    echo "  - ENOENT frontend path errors: RESOLVED"
    echo "  - IPv6 localhost database errors: RESOLVED"
    echo "  - Admin 404 asset errors: RESOLVED"
    echo "  - Database table missing errors: RESOLVED"
    echo "  - Products table functionality: WORKING"
    echo ""
    echo "🚀 Your Nirvana Organics test environment is ready!"
    echo ""
    echo "🔗 Access URLs:"
    echo "  Main Site: https://test.shopnirvanaorganics.com/"
    echo "  Admin Panel: https://test.shopnirvanaorganics.com/admin/"
    echo ""
    echo "📋 Next Steps:"
    echo "  1. Test all admin functionality in browser"
    echo "  2. Verify customer-facing features work correctly"
    echo "  3. Monitor logs for any new issues"
    echo "  4. Proceed with production deployment when ready"
    
    exit 0
else
    error "❌ $TESTS_FAILED TESTS FAILED"
    echo ""
    echo "🔧 Troubleshooting Steps:"
    echo "  1. Review failed tests above"
    echo "  2. Check PM2 logs: pm2 logs"
    echo "  3. Run individual fix scripts as needed"
    echo "  4. Consult CRITICAL_ISSUES_RESOLUTION.md"
    echo ""
    echo "🛠️  Available Fix Scripts:"
    echo "  - node database-manager.js fix (comprehensive database fix)"
    echo "  - node database-manager.js debug (environment debugging)"
    echo "  - ./fix-admin-404-assets.sh (admin assets)"
    echo "  - ./deploy-and-fix.sh (comprehensive deployment fix)"
    echo "  - ./restart-services.sh (service restart)"
    
    exit 1
fi
