const express = require('express');
const router = express.Router();
const { logger, performanceLogger } = require('../utils/logger');
const { sequelize } = require('../config/database');

// Database health check
const checkDatabase = async () => {
  try {
    const startTime = Date.now();
    await sequelize.authenticate();
    const duration = Date.now() - startTime;
    
    return { 
      status: 'healthy', 
      message: 'Database connection successful',
      responseTime: `${duration}ms`
    };
  } catch (error) {
    return { 
      status: 'unhealthy', 
      message: error.message,
      responseTime: 'timeout'
    };
  }
};

// Email service health check
const checkEmailService = async () => {
  try {
    const nodemailer = require('nodemailer');

    const transporter = nodemailer.createTransport({
      service: 'gmail',
      host: 'smtp.gmail.com',
      port: 587,
      secure: false,
      auth: {
        user: process.env.EMAIL_USER,
        pass: process.env.EMAIL_PASS
      },
      tls: {
        rejectUnauthorized: true,
        minVersion: 'TLSv1.2'
      }
    });

    const startTime = Date.now();
    await transporter.verify();
    const duration = Date.now() - startTime;

    return {
      status: 'healthy',
      message: 'Gmail transporter verified',
      responseTime: `${duration}ms`
    };
  } catch (error) {
    return {
      status: 'unhealthy',
      message: error.message,
      responseTime: 'timeout'
    };
  }
};


// File storage health check
const checkFileStorage = async () => {
  try {
    const fs = require('fs').promises;
    const uploadPath = process.env.UPLOAD_PATH || './uploads';
    
    const startTime = Date.now();
    await fs.access(uploadPath);
    const stats = await fs.stat(uploadPath);
    const duration = Date.now() - startTime;
    
    return { 
      status: 'healthy', 
      message: 'File storage accessible',
      writable: stats.isDirectory(),
      responseTime: `${duration}ms`
    };
  } catch (error) {
    return { 
      status: 'unhealthy', 
      message: error.message,
      responseTime: 'timeout'
    };
  }
};

// Memory usage check
const checkMemoryUsage = () => {
  const usage = process.memoryUsage();
  const totalMB = Math.round(usage.rss / 1024 / 1024);
  const heapUsedMB = Math.round(usage.heapUsed / 1024 / 1024);
  const heapTotalMB = Math.round(usage.heapTotal / 1024 / 1024);
  
  return {
    status: totalMB < 1024 ? 'healthy' : 'warning', // Warning if over 1GB
    totalMB,
    heapUsedMB,
    heapTotalMB,
    uptime: Math.round(process.uptime()),
    loadAverage: process.platform !== 'win32' ? require('os').loadavg() : [0, 0, 0]
  };
};

// Disk space check
const checkDiskSpace = async () => {
  try {
    const fs = require('fs').promises;
    const stats = await fs.statfs(process.cwd());
    const totalGB = Math.round(stats.bavail * stats.bsize / 1024 / 1024 / 1024);
    const freeGB = Math.round(stats.bfree * stats.bsize / 1024 / 1024 / 1024);
    const usedPercent = Math.round(((stats.blocks - stats.bfree) / stats.blocks) * 100);
    
    return {
      status: usedPercent < 90 ? 'healthy' : 'warning',
      totalGB,
      freeGB,
      usedPercent
    };
  } catch (error) {
    return {
      status: 'unknown',
      message: 'Unable to check disk space',
      error: error.message
    };
  }
};

// Main health check endpoint
router.get('/', async (req, res) => {
  // Skip health checks if disabled
  if (process.env.HEALTH_CHECK_ENABLED === 'false') {
    return res.status(404).json({
      error: 'Health checks disabled',
      timestamp: new Date().toISOString()
    });
  }

  const startTime = Date.now();

  try {
    const [database, email, storage] = await Promise.all([
      checkDatabase(),
      checkEmailService(),
      checkFileStorage()
    ]);

    const memory = checkMemoryUsage();
    const disk = await checkDiskSpace();
    const responseTime = Date.now() - startTime;
    
    const health = {
      status: 'ok',
      timestamp: new Date().toISOString(),
      responseTime: `${responseTime}ms`,
      version: process.env.npm_package_version || '1.0.0',
      environment: process.env.NODE_ENV,
      uptime: process.uptime(),
      services: {
        database,
        email,
        storage,
        memory,
        disk
      }
    };
    
    // Determine overall health status
    const unhealthyServices = Object.values(health.services)
      .filter(service => service.status === 'unhealthy');
    
    const warningServices = Object.values(health.services)
      .filter(service => service.status === 'warning');
    
    if (unhealthyServices.length > 0) {
      health.status = 'degraded';
      res.status(503);
    } else if (warningServices.length > 0) {
      health.status = 'warning';
    }
    
    // Log health check
    logger.info('Health check performed', { 
      status: health.status, 
      responseTime: health.responseTime,
      unhealthyServices: unhealthyServices.length,
      warningServices: warningServices.length
    });
    
    // Log performance metrics
    performanceLogger.apiResponse('/api/health', 'GET', responseTime, res.statusCode);
    
    res.json(health);
  } catch (error) {
    logger.error('Health check failed', error);
    res.status(500).json({
      status: 'error',
      timestamp: new Date().toISOString(),
      error: 'Health check failed',
      message: process.env.NODE_ENV === 'production' ? 'Internal server error' : error.message
    });
  }
});

// Readiness probe (for load balancers)
router.get('/ready', async (req, res) => {
  try {
    const database = await checkDatabase();
    
    if (database.status === 'healthy') {
      res.status(200).json({ 
        status: 'ready',
        timestamp: new Date().toISOString(),
        checks: {
          database: database.status
        }
      });
    } else {
      res.status(503).json({ 
        status: 'not ready', 
        reason: database.message,
        timestamp: new Date().toISOString()
      });
    }
  } catch (error) {
    res.status(503).json({ 
      status: 'not ready', 
      reason: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// Liveness probe (for container orchestration)
router.get('/live', (req, res) => {
  res.status(200).json({ 
    status: 'alive', 
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    pid: process.pid
  });
});

// Database-specific health check
router.get('/database', async (req, res) => {
  try {
    const database = await checkDatabase();
    
    if (database.status === 'healthy') {
      res.status(200).json(database);
    } else {
      res.status(503).json(database);
    }
  } catch (error) {
    res.status(500).json({
      status: 'error',
      message: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// Memory and performance metrics
router.get('/metrics', (req, res) => {
  // Skip metrics if disabled
  if (process.env.METRICS_ENABLED === 'false') {
    return res.status(404).json({
      error: 'Metrics disabled',
      timestamp: new Date().toISOString()
    });
  }

  const memory = checkMemoryUsage();

  res.json({
    timestamp: new Date().toISOString(),
    memory,
    process: {
      pid: process.pid,
      uptime: process.uptime(),
      version: process.version,
      platform: process.platform,
      arch: process.arch
    },
    system: {
      loadAverage: process.platform !== 'win32' ? require('os').loadavg() : [0, 0, 0],
      cpus: require('os').cpus().length,
      totalMemory: Math.round(require('os').totalmem() / 1024 / 1024 / 1024) + 'GB',
      freeMemory: Math.round(require('os').freemem() / 1024 / 1024 / 1024) + 'GB'
    }
  });
});

module.exports = router;
