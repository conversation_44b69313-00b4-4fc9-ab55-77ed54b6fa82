#!/bin/bash
# Quick Admin Assets Test - Run this to immediately test the current state

echo "🧪 Quick Admin Assets Test"
echo "=========================="

# Test URLs
ADMIN_URL="https://test.shopnirvanaorganics.com/admin/"
JS_URL="https://test.shopnirvanaorganics.com/admin/assets/admin-DitisM-I.js"
CSS_URL="https://test.shopnirvanaorganics.com/admin/assets/admin-TY7ZtfqV.css"

echo ""
echo "🌐 Testing HTTP responses..."

# Test admin.html
echo "1. Testing admin.html:"
ADMIN_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "$ADMIN_URL" 2>/dev/null || echo "000")
echo "   Status: $ADMIN_STATUS"
if [ "$ADMIN_STATUS" = "200" ]; then
    echo "   ✅ PASS"
else
    echo "   ❌ FAIL"
fi

# Test JavaScript
echo ""
echo "2. Testing JavaScript asset:"
JS_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "$JS_URL" 2>/dev/null || echo "000")
echo "   Status: $JS_STATUS"
if [ "$JS_STATUS" = "200" ]; then
    echo "   ✅ PASS"
else
    echo "   ❌ FAIL"
fi

# Test CSS
echo ""
echo "3. Testing CSS asset:"
CSS_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "$CSS_URL" 2>/dev/null || echo "000")
echo "   Status: $CSS_STATUS"
if [ "$CSS_STATUS" = "200" ]; then
    echo "   ✅ PASS"
else
    echo "   ❌ FAIL"
fi

echo ""
echo "📊 Summary:"
if [ "$JS_STATUS" = "200" ] && [ "$CSS_STATUS" = "200" ]; then
    echo "✅ All assets are accessible - 404 issue is RESOLVED!"
else
    echo "❌ Assets still returning 404 - fix needed"
    echo ""
    echo "🔧 To fix this issue:"
    echo "1. Run: sudo ./nirvana-test/fix-nginx-admin-assets.sh"
    echo "2. Or follow the steps in ADMIN_ASSETS_404_FIX_SOLUTION.md"
fi

echo ""
echo "🔍 Manual verification:"
echo "Open in browser: $ADMIN_URL"
echo "Check for 404 errors in browser console (F12 -> Network tab)"
