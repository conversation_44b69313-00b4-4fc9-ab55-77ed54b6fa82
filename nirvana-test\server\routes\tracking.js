const express = require('express');
const router = express.Router();
const trackingController = require('../controllers/trackingController');
const { authenticate, requireAdmin, requireManagerOrAdmin, auditAdminAction } = require('../middleware/auth');
const { validateId } = require('../middleware/validation');
const { body } = require('express-validator');

// Validation middleware
const validateTrackingNumber = [
  body('trackingNumber')
    .notEmpty()
    .withMessage('Tracking number is required')
    .isLength({ min: 10, max: 50 })
    .withMessage('Tracking number must be between 10 and 50 characters'),
  body('carrier')
    .optional()
    .isIn(['USPS', 'UPS', 'FedEx'])
    .withMessage('Invalid carrier'),
  body('notes')
    .optional()
    .isLength({ max: 500 })
    .withMessage('Notes must be less than 500 characters')
];

// @route   POST /api/admin/tracking/:orderId/add
// @desc    Add tracking number to order
// @access  Private (Admin/Manager)
router.post('/:orderId/add',
  authenticate,
  requireManagerOrAdmin,
  validateId('orderId'),
  validateTrackingNumber,
  auditAdminAction('ADD_TRACKING_NUMBER', 'ORDER'),
  trackingController.addTrackingNumber
);

// @route   PUT /api/admin/tracking/:orderId/update
// @desc    Update tracking status for order
// @access  Private (Admin/Manager)
router.put('/:orderId/update',
  authenticate,
  requireManagerOrAdmin,
  validateId('orderId'),
  auditAdminAction('UPDATE_TRACKING_STATUS', 'ORDER'),
  trackingController.updateTrackingStatus
);

// @route   GET /api/admin/tracking/:orderId
// @desc    Get tracking information for order
// @access  Private (Admin/Manager)
router.get('/:orderId',
  authenticate,
  requireManagerOrAdmin,
  validateId('orderId'),
  trackingController.getTrackingInfo
);

// @route   POST /api/admin/tracking/batch-update
// @desc    Batch update tracking for multiple orders
// @access  Private (Admin only)
router.post('/batch-update',
  authenticate,
  requireAdmin,
  auditAdminAction('BATCH_UPDATE_TRACKING', 'ORDER'),
  trackingController.batchUpdateTracking
);

// Public tracking routes (for customers)

// @route   GET /api/tracking/:trackingNumber
// @desc    Get tracking information by tracking number (public)
// @access  Public
router.get('/public/:trackingNumber', async (req, res) => {
  try {
    const { trackingNumber } = req.params;
    const uspsService = require('../services/uspsService');

    // Validate tracking number format
    if (!uspsService.validateTrackingNumber(trackingNumber)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid tracking number format'
      });
    }

    // Get tracking information
    const trackingInfo = await uspsService.trackPackage(trackingNumber);

    if (trackingInfo.success) {
      res.json({
        success: true,
        data: {
          trackingNumber: trackingNumber,
          status: trackingInfo.status,
          statusHistory: trackingInfo.statusHistory,
          estimatedDelivery: trackingInfo.estimatedDelivery,
          lastUpdate: trackingInfo.lastUpdate,
          carrier: trackingInfo.carrier
        }
      });
    } else {
      res.status(404).json({
        success: false,
        message: 'Tracking information not found',
        error: trackingInfo.error
      });
    }

  } catch (error) {
    console.error('Public tracking error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get tracking information',
      error: error.message
    });
  }
});

// @route   GET /api/tracking/order/:orderNumber
// @desc    Get tracking information by order number (requires email verification)
// @access  Public
router.post('/order/:orderNumber', 
  [
    body('email')
      .isEmail()
      .normalizeEmail()
      .withMessage('Valid email is required for order tracking')
  ],
  async (req, res) => {
    try {
      const { orderNumber } = req.params;
      const { email } = req.body;
      const { Order, User } = require('../models');

      // Find order by order number and email
      const order = await Order.findOne({
        where: { orderNumber: orderNumber },
        include: [
          {
            model: User,
            as: 'user',
            attributes: ['email']
          }
        ]
      });

      if (!order) {
        return res.status(404).json({
          success: false,
          message: 'Order not found'
        });
      }

      // Verify email matches
      const orderEmail = order.user?.email || order.guestEmail;
      if (orderEmail !== email) {
        return res.status(403).json({
          success: false,
          message: 'Email does not match order records'
        });
      }

      // Get tracking information if available
      let trackingInfo = null;
      if (order.trackingNumber && order.shippingCarrier === 'USPS') {
        const uspsService = require('../services/uspsService');
        trackingInfo = await uspsService.trackPackage(order.trackingNumber);
      }

      res.json({
        success: true,
        data: {
          orderNumber: order.orderNumber,
          status: order.status,
          trackingNumber: order.trackingNumber,
          carrier: order.shippingCarrier,
          shippedAt: order.shippedAt,
          deliveredAt: order.deliveredAt,
          estimatedDelivery: order.estimatedDelivery,
          trackingInfo: trackingInfo
        }
      });

    } catch (error) {
      console.error('Order tracking error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to get order tracking information',
        error: error.message
      });
    }
  }
);

module.exports = router;
