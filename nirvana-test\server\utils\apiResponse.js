/**
 * Standardized API Response Utility
 * Provides consistent response format across all API endpoints
 */

class ApiResponse {
  constructor(success, message, data = null, errors = null, meta = null) {
    this.success = success;
    this.message = message;
    
    if (data !== null) {
      this.data = data;
    }
    
    if (errors !== null) {
      this.errors = errors;
    }
    
    if (meta !== null) {
      this.meta = meta;
    }
    
    this.timestamp = new Date().toISOString();
  }

  static success(message, data = null, meta = null) {
    return new ApiResponse(true, message, data, null, meta);
  }

  static error(message, errors = null, meta = null) {
    return new ApiResponse(false, message, null, errors, meta);
  }

  static created(message, data = null) {
    return new ApiResponse(true, message, data, null, { statusCode: 201 });
  }

  static updated(message, data = null) {
    return new ApiResponse(true, message, data, null, { statusCode: 200 });
  }

  static deleted(message) {
    return new ApiResponse(true, message, null, null, { statusCode: 200 });
  }

  static notFound(message = 'Resource not found') {
    return new ApiResponse(false, message, null, null, { statusCode: 404 });
  }

  static unauthorized(message = 'Unauthorized access') {
    return new ApiResponse(false, message, null, null, { statusCode: 401 });
  }

  static forbidden(message = 'Access forbidden') {
    return new ApiResponse(false, message, null, null, { statusCode: 403 });
  }

  static validationError(message, errors) {
    return new ApiResponse(false, message, null, errors, { statusCode: 400 });
  }

  static serverError(message = 'Internal server error') {
    return new ApiResponse(false, message, null, null, { statusCode: 500 });
  }

  static paginated(message, data, pagination) {
    const meta = {
      pagination: {
        currentPage: pagination.currentPage,
        totalPages: pagination.totalPages,
        totalItems: pagination.totalItems,
        itemsPerPage: pagination.itemsPerPage,
        hasNextPage: pagination.hasNextPage,
        hasPrevPage: pagination.hasPrevPage
      }
    };
    
    return new ApiResponse(true, message, data, null, meta);
  }

  send(res, statusCode = null) {
    const code = statusCode || this.meta?.statusCode || (this.success ? 200 : 500);
    return res.status(code).json(this);
  }
}

// Helper functions for common response patterns
const sendSuccess = (res, message, data = null, statusCode = 200) => {
  return ApiResponse.success(message, data).send(res, statusCode);
};

const sendError = (res, message, statusCode = 500, errors = null) => {
  return ApiResponse.error(message, errors).send(res, statusCode);
};

const sendCreated = (res, message, data = null) => {
  return ApiResponse.created(message, data).send(res, 201);
};

const sendUpdated = (res, message, data = null) => {
  return ApiResponse.updated(message, data).send(res, 200);
};

const sendDeleted = (res, message) => {
  return ApiResponse.deleted(message).send(res, 200);
};

const sendNotFound = (res, message = 'Resource not found') => {
  return ApiResponse.notFound(message).send(res, 404);
};

const sendUnauthorized = (res, message = 'Unauthorized access') => {
  return ApiResponse.unauthorized(message).send(res, 401);
};

const sendForbidden = (res, message = 'Access forbidden') => {
  return ApiResponse.forbidden(message).send(res, 403);
};

const sendValidationError = (res, message, errors) => {
  return ApiResponse.validationError(message, errors).send(res, 400);
};

const sendServerError = (res, message = 'Internal server error') => {
  return ApiResponse.serverError(message).send(res, 500);
};

const sendPaginated = (res, message, data, pagination) => {
  return ApiResponse.paginated(message, data, pagination).send(res, 200);
};

// Pagination helper
const createPagination = (page, limit, totalItems) => {
  const currentPage = parseInt(page) || 1;
  const itemsPerPage = parseInt(limit) || 10;
  const totalPages = Math.ceil(totalItems / itemsPerPage);
  
  return {
    currentPage,
    totalPages,
    totalItems,
    itemsPerPage,
    hasNextPage: currentPage < totalPages,
    hasPrevPage: currentPage > 1,
    nextPage: currentPage < totalPages ? currentPage + 1 : null,
    prevPage: currentPage > 1 ? currentPage - 1 : null
  };
};

// Response middleware to add helper methods to res object
const responseMiddleware = (req, res, next) => {
  res.sendSuccess = (message, data, statusCode) => sendSuccess(res, message, data, statusCode);
  res.sendError = (message, statusCode, errors) => sendError(res, message, statusCode, errors);
  res.sendCreated = (message, data) => sendCreated(res, message, data);
  res.sendUpdated = (message, data) => sendUpdated(res, message, data);
  res.sendDeleted = (message) => sendDeleted(res, message);
  res.sendNotFound = (message) => sendNotFound(res, message);
  res.sendUnauthorized = (message) => sendUnauthorized(res, message);
  res.sendForbidden = (message) => sendForbidden(res, message);
  res.sendValidationError = (message, errors) => sendValidationError(res, message, errors);
  res.sendServerError = (message) => sendServerError(res, message);
  res.sendPaginated = (message, data, pagination) => sendPaginated(res, message, data, pagination);
  
  next();
};

// API documentation helper
const generateApiDocs = (endpoints) => {
  return {
    version: '1.0.0',
    title: 'Nirvana Organics API',
    description: 'E-commerce API for cannabis products',
    baseUrl: process.env.API_BASE_URL || 'http://localhost:5000/api',
    endpoints,
    responseFormat: {
      success: {
        success: true,
        message: 'Operation successful',
        data: '{}',
        timestamp: '2024-01-01T00:00:00.000Z'
      },
      error: {
        success: false,
        message: 'Operation failed',
        errors: '[]',
        timestamp: '2024-01-01T00:00:00.000Z'
      },
      paginated: {
        success: true,
        message: 'Data retrieved successfully',
        data: '[]',
        meta: {
          pagination: {
            currentPage: 1,
            totalPages: 10,
            totalItems: 100,
            itemsPerPage: 10,
            hasNextPage: true,
            hasPrevPage: false,
            nextPage: 2,
            prevPage: null
          }
        },
        timestamp: '2024-01-01T00:00:00.000Z'
      }
    }
  };
};

module.exports = {
  ApiResponse,
  sendSuccess,
  sendError,
  sendCreated,
  sendUpdated,
  sendDeleted,
  sendNotFound,
  sendUnauthorized,
  sendForbidden,
  sendValidationError,
  sendServerError,
  sendPaginated,
  createPagination,
  responseMiddleware,
  generateApiDocs
};
