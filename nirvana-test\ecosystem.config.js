module.exports = {
  apps: [
    {
      name: 'nirvana-backend-main-test',
      script: './server/index.js',
      cwd: '/var/www/nirvana-backend-test',
      env: {
        NODE_ENV: 'test'
      },
      instances: 1,
      exec_mode: 'fork',
      watch: false,
      max_memory_restart: '1G',
      error_file: '/home/<USER>/.pm2/logs/nirvana-backend-main-test-error.log',
      out_file: '/home/<USER>/.pm2/logs/nirvana-backend-main-test-out.log',
      log_file: '/home/<USER>/.pm2/logs/nirvana-backend-main-test.log',
      time: true,
      restart_delay: 1000,
      max_restarts: 10,
      min_uptime: '10s'
    },
    {
      name: 'nirvana-backend-admin-test',
      script: './server/admin-server.js',
      cwd: '/var/www/nirvana-backend-test',
      env: {
        NODE_ENV: 'test'
      },
      instances: 1,
      exec_mode: 'fork',
      watch: false,
      max_memory_restart: '1G',
      error_file: '/home/<USER>/.pm2/logs/nirvana-backend-admin-test-error.log',
      out_file: '/home/<USER>/.pm2/logs/nirvana-backend-admin-test-out.log',
      log_file: '/home/<USER>/.pm2/logs/nirvana-backend-admin-test.log',
      time: true,
      restart_delay: 1000,
      max_restarts: 10,
      min_uptime: '10s'
    }
  ]
};
