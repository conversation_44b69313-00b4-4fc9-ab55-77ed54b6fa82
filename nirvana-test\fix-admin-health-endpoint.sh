#!/bin/bash

# Fix Admin Server Health Endpoint Issues
# Diagnoses and fixes why admin server health endpoint is not responding

echo "🏥 Fixing Admin Server Health Endpoint Issues"
echo "============================================="

DEPLOY_DIR="/var/www/nirvana-backend-test"
DEPLOY_USER="Nirvana"

# Step 1: Check if admin server is actually running
echo "🔍 Checking admin server status..."
echo "PM2 processes:"
sudo -u $DEPLOY_USER pm2 list | grep admin

echo ""
echo "Admin server process details:"
sudo -u $DEPLOY_USER pm2 show nirvana-backend-admin-test 2>/dev/null || echo "❌ Admin process not found"

echo ""

# Step 2: Check admin server logs for startup issues
echo "📋 Recent admin server logs:"
sudo -u $DEPLOY_USER pm2 logs nirvana-backend-admin-test --lines 20 2>/dev/null || echo "❌ Cannot access admin logs"

echo ""

# Step 3: Check if admin server is listening on correct port
echo "🔌 Checking port 3001 (admin server port):"
netstat -tlnp | grep :3001 || echo "❌ Nothing listening on port 3001"

echo ""

# Step 4: Test admin server health endpoint directly
echo "🧪 Testing admin server health endpoint:"
echo "Local test (port 3001):"
curl -v http://localhost:3001/api/health 2>&1 | head -10 || echo "❌ Local health check failed"

echo ""
echo "External test (via domain):"
curl -v https://test.shopnirvanaorganics.com/admin/api/health 2>&1 | head -10 || echo "❌ External health check failed"

echo ""

# Step 5: Check Nginx configuration for admin routing
echo "🌐 Checking Nginx configuration for admin routing..."
if [ -f "/etc/nginx/sites-available/test.shopnirvanaorganics.com" ]; then
    echo "Nginx config exists. Checking admin routing:"
    grep -A 10 -B 2 "/admin" /etc/nginx/sites-available/test.shopnirvanaorganics.com || echo "❌ No admin routing found in Nginx"
else
    echo "❌ Nginx config not found at expected location"
fi

echo ""

# Step 6: Check if admin server file exists and has health endpoint
echo "📁 Checking admin server file..."
if [ -f "$DEPLOY_DIR/server/admin-server.js" ]; then
    echo "✅ Admin server file exists"
    echo "Checking for health endpoint:"
    grep -n -A 5 -B 2 "health\|/api" "$DEPLOY_DIR/server/admin-server.js" | head -20 || echo "❌ No health endpoint found"
else
    echo "❌ Admin server file not found"
fi

echo ""

# Step 7: Provide specific fixes based on common issues
echo "🔧 Common Admin Server Issues and Fixes:"
echo "========================================"

echo "1. If admin server is not starting:"
echo "   - Check logs: sudo -u $DEPLOY_USER pm2 logs nirvana-backend-admin-test"
echo "   - Restart: sudo -u $DEPLOY_USER pm2 restart nirvana-backend-admin-test"

echo ""
echo "2. If admin server starts but health endpoint fails:"
echo "   - Verify health route exists in admin-server.js"
echo "   - Check if server is listening on port 3001"
echo "   - Test local connection: curl http://localhost:3001/api/health"

echo ""
echo "3. If local works but external fails:"
echo "   - Check Nginx configuration for /admin routing"
echo "   - Verify proxy_pass points to localhost:3001"
echo "   - Check SSL certificate for admin subdomain"

echo ""
echo "4. If environment variables are not loading:"
echo "   - Run: ./fix-pm2-env-loading.sh"
echo "   - Verify .env.admin.test has correct PORT=3001"

echo ""

# Step 8: Quick fix attempt
echo "🚀 Attempting quick fix..."

# Restart admin server
echo "Restarting admin server..."
sudo -u $DEPLOY_USER pm2 restart nirvana-backend-admin-test 2>/dev/null || echo "❌ Failed to restart admin server"

# Wait a moment
sleep 3

# Test again
echo "Testing after restart:"
curl -s http://localhost:3001/api/health && echo "✅ Local health check passed" || echo "❌ Local health check still failing"

echo ""
echo "📞 Next Steps:"
echo "============="
echo "1. Run the main fix: sudo ./fix-pm2-env-loading.sh"
echo "2. Check admin server logs: sudo -u $DEPLOY_USER pm2 logs nirvana-backend-admin-test"
echo "3. Verify Nginx config includes admin routing"
echo "4. Test health endpoints after fixes"

# Step 9: Create a simple health endpoint test
echo ""
echo "🧪 Creating health endpoint test script..."
cat > "$DEPLOY_DIR/test-health.sh" << 'EOF'
#!/bin/bash
echo "Testing Nirvana Organics Health Endpoints"
echo "========================================"

echo "Main server (port 5000):"
curl -s http://localhost:5000/api/health && echo " ✅ Main server OK" || echo " ❌ Main server FAIL"

echo "Admin server (port 3001):"
curl -s http://localhost:3001/api/health && echo " ✅ Admin server OK" || echo " ❌ Admin server FAIL"

echo ""
echo "External endpoints:"
curl -s https://test.shopnirvanaorganics.com/api/health && echo " ✅ Main external OK" || echo " ❌ Main external FAIL"
curl -s https://test.shopnirvanaorganics.com/admin/api/health && echo " ✅ Admin external OK" || echo " ❌ Admin external FAIL"
EOF

chmod +x "$DEPLOY_DIR/test-health.sh"
chown $DEPLOY_USER:$DEPLOY_USER "$DEPLOY_DIR/test-health.sh"

echo "✅ Health endpoint diagnostic complete!"
echo "Run ./test-health.sh to test all endpoints"
