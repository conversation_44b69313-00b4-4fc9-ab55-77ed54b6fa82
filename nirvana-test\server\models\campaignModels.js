const { DataTypes } = require('sequelize');

/**
 * Campaign Models
 * Defines database models for email campaigns, push notifications, and marketing automation
 */
function defineCampaignModels(sequelize, models) {
  // Campaign Model
  models.Campaign = sequelize.define('Campaign', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        len: [3, 100]
      }
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    type: {
      type: DataTypes.ENUM('email', 'push', 'sms', 'in_app'),
      allowNull: false
    },
    status: {
      type: DataTypes.ENUM('draft', 'scheduled', 'active', 'paused', 'completed', 'cancelled'),
      defaultValue: 'draft'
    },
    // Campaign content
    subject: {
      type: DataTypes.STRING,
      allowNull: true,
      validate: {
        len: [1, 200]
      }
    },
    content: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    htmlContent: {
      type: DataTypes.TEXT,
      allowNull: true,
      field: 'html_content'
    },
    // Targeting
    targetAudience: {
      type: DataTypes.ENUM('all', 'customers', 'subscribers', 'segment', 'custom'),
      defaultValue: 'all',
      field: 'target_audience'
    },
    segmentCriteria: {
      type: DataTypes.JSON,
      defaultValue: {},
      field: 'segment_criteria'
    },
    // Scheduling
    scheduledAt: {
      type: DataTypes.DATE,
      allowNull: true,
      field: 'scheduled_at'
    },
    startDate: {
      type: DataTypes.DATE,
      allowNull: true,
      field: 'start_date'
    },
    endDate: {
      type: DataTypes.DATE,
      allowNull: true,
      field: 'end_date'
    },
    // Analytics
    totalRecipients: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      field: 'total_recipients'
    },
    sentCount: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      field: 'sent_count'
    },
    deliveredCount: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      field: 'delivered_count'
    },
    openedCount: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      field: 'opened_count'
    },
    clickedCount: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      field: 'clicked_count'
    },
    unsubscribedCount: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      field: 'unsubscribed_count'
    },
    // Settings
    settings: {
      type: DataTypes.JSON,
      defaultValue: {}
    },
    createdBy: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: 'created_by'
    }
  });

  // Campaign Recipient Model
  models.CampaignRecipient = sequelize.define('CampaignRecipient', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    campaignId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: 'campaign_id'
    },
    userId: {
      type: DataTypes.INTEGER,
      allowNull: true,
      field: 'user_id'
    },
    email: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        isEmail: true
      }
    },
    status: {
      type: DataTypes.ENUM('pending', 'sent', 'delivered', 'opened', 'clicked', 'bounced', 'unsubscribed'),
      defaultValue: 'pending'
    },
    sentAt: {
      type: DataTypes.DATE,
      allowNull: true,
      field: 'sent_at'
    },
    deliveredAt: {
      type: DataTypes.DATE,
      allowNull: true,
      field: 'delivered_at'
    },
    openedAt: {
      type: DataTypes.DATE,
      allowNull: true,
      field: 'opened_at'
    },
    clickedAt: {
      type: DataTypes.DATE,
      allowNull: true,
      field: 'clicked_at'
    },
    unsubscribedAt: {
      type: DataTypes.DATE,
      allowNull: true,
      field: 'unsubscribed_at'
    },
    errorMessage: {
      type: DataTypes.TEXT,
      allowNull: true,
      field: 'error_message'
    }
  });

  // Email Template Model
  models.EmailTemplate = sequelize.define('EmailTemplate', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        len: [3, 100]
      }
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    category: {
      type: DataTypes.ENUM('marketing', 'transactional', 'notification', 'welcome', 'abandoned_cart', 'order_confirmation'),
      allowNull: false
    },
    subject: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        len: [1, 200]
      }
    },
    htmlContent: {
      type: DataTypes.TEXT,
      allowNull: false,
      field: 'html_content'
    },
    textContent: {
      type: DataTypes.TEXT,
      allowNull: true,
      field: 'text_content'
    },
    variables: {
      type: DataTypes.JSON,
      defaultValue: [],
      comment: 'Available template variables like {{firstName}}, {{orderTotal}}, etc.'
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
      field: 'is_active'
    },
    createdBy: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: 'created_by'
    }
  });

  // Newsletter Subscription Model
  models.NewsletterSubscription = sequelize.define('NewsletterSubscription', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    email: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true,
      validate: {
        isEmail: true
      }
    },
    userId: {
      type: DataTypes.INTEGER,
      allowNull: true,
      field: 'user_id'
    },
    firstName: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'first_name'
    },
    lastName: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'last_name'
    },
    status: {
      type: DataTypes.ENUM('active', 'unsubscribed', 'bounced'),
      defaultValue: 'active'
    },
    preferences: {
      type: DataTypes.JSON,
      defaultValue: {
        marketing: true,
        productUpdates: true,
        promotions: true,
        newsletter: true
      }
    },
    source: {
      type: DataTypes.STRING,
      allowNull: true,
      comment: 'Where the subscription came from (website, popup, checkout, etc.)'
    },
    confirmedAt: {
      type: DataTypes.DATE,
      allowNull: true,
      field: 'confirmed_at'
    },
    unsubscribedAt: {
      type: DataTypes.DATE,
      allowNull: true,
      field: 'unsubscribed_at'
    },
    unsubscribeToken: {
      type: DataTypes.STRING,
      allowNull: true,
      unique: true,
      field: 'unsubscribe_token'
    }
  });

  // Push Notification Model
  models.PushNotification = sequelize.define('PushNotification', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    title: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        len: [1, 100]
      }
    },
    body: {
      type: DataTypes.TEXT,
      allowNull: false,
      validate: {
        len: [1, 500]
      }
    },
    icon: {
      type: DataTypes.STRING,
      allowNull: true
    },
    image: {
      type: DataTypes.STRING,
      allowNull: true
    },
    url: {
      type: DataTypes.STRING,
      allowNull: true,
      validate: {
        isUrl: true
      }
    },
    data: {
      type: DataTypes.JSON,
      defaultValue: {}
    },
    targetAudience: {
      type: DataTypes.ENUM('all', 'customers', 'segment', 'custom'),
      defaultValue: 'all',
      field: 'target_audience'
    },
    segmentCriteria: {
      type: DataTypes.JSON,
      defaultValue: {},
      field: 'segment_criteria'
    },
    status: {
      type: DataTypes.ENUM('draft', 'scheduled', 'sent', 'cancelled'),
      defaultValue: 'draft'
    },
    scheduledAt: {
      type: DataTypes.DATE,
      allowNull: true,
      field: 'scheduled_at'
    },
    sentAt: {
      type: DataTypes.DATE,
      allowNull: true,
      field: 'sent_at'
    },
    totalRecipients: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      field: 'total_recipients'
    },
    deliveredCount: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      field: 'delivered_count'
    },
    clickedCount: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      field: 'clicked_count'
    },
    createdBy: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: 'created_by'
    }
  });

  // Define associations
  models.Campaign.belongsTo(models.User, { foreignKey: 'createdBy', as: 'creator' });
  models.Campaign.hasMany(models.CampaignRecipient, { foreignKey: 'campaignId', as: 'recipients' });
  
  models.CampaignRecipient.belongsTo(models.Campaign, { foreignKey: 'campaignId', as: 'campaign' });
  models.CampaignRecipient.belongsTo(models.User, { foreignKey: 'userId', as: 'user' });
  
  models.EmailTemplate.belongsTo(models.User, { foreignKey: 'createdBy', as: 'creator' });
  
  models.NewsletterSubscription.belongsTo(models.User, { foreignKey: 'userId', as: 'user' });
  models.User.hasOne(models.NewsletterSubscription, { foreignKey: 'userId', as: 'newsletterSubscription' });
  
  models.PushNotification.belongsTo(models.User, { foreignKey: 'createdBy', as: 'creator' });

  return models;
}

module.exports = defineCampaignModels;
