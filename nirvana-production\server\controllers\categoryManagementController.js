const { Category, Product, User, AuditLog } = require('../models');
const { Op, sequelize } = require('sequelize');
const { validationResult } = require('express-validator');

// Get all categories with filtering and pagination
const getCategories = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 50,
      search,
      parentId,
      isActive,
      sortBy = 'sortOrder',
      sortOrder = 'ASC',
      includeProducts = false,
      tree = false
    } = req.query;

    // If tree structure is requested
    if (tree === 'true') {
      const categories = await Category.findAll({
        where: { isActive: true },
        order: [['sortOrder', 'ASC'], ['name', 'ASC']],
        include: [
          {
            model: Category,
            as: 'children',
            include: [
              {
                model: Category,
                as: 'children'
              }
            ]
          }
        ]
      });

      // Build tree structure
      const rootCategories = categories.filter(cat => !cat.parentId);
      
      return res.json({
        success: true,
        data: { categories: rootCategories }
      });
    }

    // Build where clause
    const where = {};
    
    if (search) {
      where[Op.or] = [
        { name: { [Op.iLike]: `%${search}%` } },
        { description: { [Op.iLike]: `%${search}%` } },
        { seoTitle: { [Op.iLike]: `%${search}%` } }
      ];
    }
    
    if (parentId !== undefined) {
      where.parentId = parentId === 'null' ? null : parseInt(parentId);
    }
    
    if (isActive !== undefined) {
      where.isActive = isActive === 'true';
    }

    // Build include array
    const include = [
      {
        model: Category,
        as: 'parent',
        attributes: ['id', 'name', 'slug']
      },
      {
        model: Category,
        as: 'children',
        attributes: ['id', 'name', 'slug', 'isActive']
      }
    ];

    if (includeProducts === 'true') {
      include.push({
        model: Product,
        as: 'products',
        attributes: ['id', 'name', 'slug', 'price', 'isActive'],
        limit: 5
      });
    }

    const { count, rows: categories } = await Category.findAndCountAll({
      where,
      include,
      order: [[sortBy, sortOrder.toUpperCase()]],
      offset: (parseInt(page) - 1) * parseInt(limit),
      limit: parseInt(limit),
      distinct: true
    });

    // Add computed fields
    const categoriesWithStats = await Promise.all(
      categories.map(async (category) => {
        const categoryData = category.toJSON();
        
        // Get product count
        categoryData.productCount = await Product.count({
          where: { categoryId: category.id }
        });
        
        // Get subcategory count
        categoryData.subcategoryCount = await Category.count({
          where: { parentId: category.id }
        });
        
        return categoryData;
      })
    );

    const totalPages = Math.ceil(count / parseInt(limit));

    // Log admin action
    await AuditLog.logUserAction(req.user.id, 'VIEW_CATEGORIES', 'CATEGORY', null, {
      ipAddress: req.ip,
      userAgent: req.get('User-Agent'),
      metadata: { filters: req.query }
    });

    res.json({
      success: true,
      data: {
        categories: categoriesWithStats,
        pagination: {
          currentPage: parseInt(page),
          totalPages,
          totalCategories: count,
          hasNextPage: parseInt(page) < totalPages,
          hasPrevPage: parseInt(page) > 1,
          limit: parseInt(limit)
        }
      }
    });

  } catch (error) {
    console.error('Get categories error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch categories',
      error: error.message
    });
  }
};

// Get single category by ID
const getCategoryById = async (req, res) => {
  try {
    const { categoryId } = req.params;

    const category = await Category.findByPk(categoryId, {
      include: [
        {
          model: Category,
          as: 'parent',
          attributes: ['id', 'name', 'slug']
        },
        {
          model: Category,
          as: 'children',
          attributes: ['id', 'name', 'slug', 'isActive', 'sortOrder']
        },
        {
          model: Product,
          as: 'products',
          attributes: ['id', 'name', 'slug', 'price', 'isActive'],
          limit: 10,
          order: [['createdAt', 'DESC']]
        }
      ]
    });

    if (!category) {
      return res.status(404).json({
        success: false,
        message: 'Category not found'
      });
    }

    // Add computed fields
    const categoryData = category.toJSON();
    categoryData.productCount = await Product.count({
      where: { categoryId: category.id }
    });
    categoryData.subcategoryCount = await Category.count({
      where: { parentId: category.id }
    });

    // Log admin action
    await AuditLog.logUserAction(req.user.id, 'VIEW_CATEGORY', 'CATEGORY', categoryId, {
      ipAddress: req.ip,
      userAgent: req.get('User-Agent')
    });

    res.json({
      success: true,
      data: { category: categoryData }
    });

  } catch (error) {
    console.error('Get category by ID error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch category',
      error: error.message
    });
  }
};

// Create new category
const createCategory = async (req, res) => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const {
      name,
      slug,
      description,
      image,
      parentId,
      isActive = true,
      sortOrder = 0,
      seoTitle,
      seoDescription,
      seoKeywords
    } = req.body;

    // Check if slug already exists
    const existingCategory = await Category.findOne({ where: { slug } });
    if (existingCategory) {
      return res.status(400).json({
        success: false,
        message: 'Category slug already exists'
      });
    }

    // Validate parent category if provided
    if (parentId) {
      const parentCategory = await Category.findByPk(parentId);
      if (!parentCategory) {
        return res.status(400).json({
          success: false,
          message: 'Parent category not found'
        });
      }
    }

    // Create category
    const category = await Category.create({
      name,
      slug,
      description,
      image,
      parentId: parentId || null,
      isActive,
      sortOrder,
      seoTitle,
      seoDescription,
      seoKeywords
    });

    // Get created category with relations
    const createdCategory = await Category.findByPk(category.id, {
      include: [
        {
          model: Category,
          as: 'parent',
          attributes: ['id', 'name', 'slug']
        }
      ]
    });

    // Log admin action
    await AuditLog.logCreate(req.user.id, 'CATEGORY', category.id, createdCategory.toJSON(), {
      ipAddress: req.ip,
      userAgent: req.get('User-Agent'),
      severity: 'medium'
    });

    res.status(201).json({
      success: true,
      message: 'Category created successfully',
      data: { category: createdCategory }
    });

  } catch (error) {
    console.error('Create category error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create category',
      error: error.message
    });
  }
};

// Update category
const updateCategory = async (req, res) => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { categoryId } = req.params;
    const updateData = req.body;

    // Find category
    const category = await Category.findByPk(categoryId);
    if (!category) {
      return res.status(404).json({
        success: false,
        message: 'Category not found'
      });
    }

    // Store old values for audit log
    const oldValues = category.toJSON();

    // Check if slug is being changed and if it already exists
    if (updateData.slug && updateData.slug !== category.slug) {
      const existingCategory = await Category.findOne({ 
        where: { 
          slug: updateData.slug,
          id: { [Op.ne]: categoryId }
        } 
      });
      
      if (existingCategory) {
        return res.status(400).json({
          success: false,
          message: 'Category slug already exists'
        });
      }
    }

    // Validate parent category if being changed
    if (updateData.parentId && updateData.parentId !== category.parentId) {
      // Prevent circular reference
      if (parseInt(updateData.parentId) === parseInt(categoryId)) {
        return res.status(400).json({
          success: false,
          message: 'Category cannot be its own parent'
        });
      }

      const parentCategory = await Category.findByPk(updateData.parentId);
      if (!parentCategory) {
        return res.status(400).json({
          success: false,
          message: 'Parent category not found'
        });
      }
    }

    // Update category
    await category.update(updateData);

    // Get updated category data
    const updatedCategory = await Category.findByPk(categoryId, {
      include: [
        {
          model: Category,
          as: 'parent',
          attributes: ['id', 'name', 'slug']
        },
        {
          model: Category,
          as: 'children',
          attributes: ['id', 'name', 'slug']
        }
      ]
    });

    // Log admin action
    await AuditLog.logUpdate(req.user.id, 'CATEGORY', categoryId, oldValues, updatedCategory.toJSON(), {
      ipAddress: req.ip,
      userAgent: req.get('User-Agent'),
      severity: 'medium'
    });

    res.json({
      success: true,
      message: 'Category updated successfully',
      data: { category: updatedCategory }
    });

  } catch (error) {
    console.error('Update category error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update category',
      error: error.message
    });
  }
};

// Delete category
const deleteCategory = async (req, res) => {
  try {
    const { categoryId } = req.params;

    // Find category
    const category = await Category.findByPk(categoryId);
    if (!category) {
      return res.status(404).json({
        success: false,
        message: 'Category not found'
      });
    }

    // Check if category has products
    const productCount = await Product.count({
      where: { categoryId }
    });

    if (productCount > 0) {
      return res.status(400).json({
        success: false,
        message: `Cannot delete category with ${productCount} products. Please move or delete products first.`
      });
    }

    // Check if category has subcategories
    const subcategoryCount = await Category.count({
      where: { parentId: categoryId }
    });

    if (subcategoryCount > 0) {
      return res.status(400).json({
        success: false,
        message: `Cannot delete category with ${subcategoryCount} subcategories. Please move or delete subcategories first.`
      });
    }

    // Store old values for audit log
    const oldValues = category.toJSON();

    // Delete category
    await category.destroy();

    // Log admin action
    await AuditLog.logDelete(req.user.id, 'CATEGORY', categoryId, oldValues, {
      ipAddress: req.ip,
      userAgent: req.get('User-Agent'),
      severity: 'high'
    });

    res.json({
      success: true,
      message: 'Category deleted successfully'
    });

  } catch (error) {
    console.error('Delete category error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete category',
      error: error.message
    });
  }
};

// Bulk operations on categories
const bulkUpdateCategories = async (req, res) => {
  try {
    const { categoryIds, action, data } = req.body;

    if (!categoryIds || !Array.isArray(categoryIds) || categoryIds.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Category IDs array is required'
      });
    }

    let updateData = {};
    let actionName = '';

    switch (action) {
      case 'activate':
        updateData = { isActive: true };
        actionName = 'BULK_ACTIVATE';
        break;
      case 'deactivate':
        updateData = { isActive: false };
        actionName = 'BULK_DEACTIVATE';
        break;
      case 'move':
        if (data.parentId === undefined) {
          return res.status(400).json({
            success: false,
            message: 'Parent ID is required for move action'
          });
        }
        updateData = { parentId: data.parentId };
        actionName = 'BULK_MOVE';
        break;
      case 'delete':
        actionName = 'BULK_DELETE';
        break;
      default:
        return res.status(400).json({
          success: false,
          message: 'Invalid action'
        });
    }

    let affectedCount = 0;

    if (action === 'delete') {
      // Check if any categories have products or subcategories
      const categoriesWithProducts = await Category.findAll({
        where: { id: { [Op.in]: categoryIds } },
        include: [
          {
            model: Product,
            as: 'products',
            attributes: ['id']
          },
          {
            model: Category,
            as: 'children',
            attributes: ['id']
          }
        ]
      });

      const categoriesWithDependencies = categoriesWithProducts.filter(
        cat => cat.products.length > 0 || cat.children.length > 0
      );

      if (categoriesWithDependencies.length > 0) {
        return res.status(400).json({
          success: false,
          message: 'Cannot delete categories that have products or subcategories'
        });
      }

      // Delete categories
      affectedCount = await Category.destroy({
        where: { id: { [Op.in]: categoryIds } }
      });
    } else {
      // Update categories
      const [updatedCount] = await Category.update(updateData, {
        where: { id: { [Op.in]: categoryIds } }
      });
      affectedCount = updatedCount;
    }

    // Log admin action
    await AuditLog.logBulkAction(req.user.id, actionName, 'CATEGORY', categoryIds, {
      ipAddress: req.ip,
      userAgent: req.get('User-Agent'),
      metadata: { updateData },
      severity: 'high'
    });

    res.json({
      success: true,
      message: `Successfully ${action}d ${affectedCount} categories`,
      data: { affectedCount }
    });

  } catch (error) {
    console.error('Bulk update categories error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update categories',
      error: error.message
    });
  }
};

// Reorder categories
const reorderCategories = async (req, res) => {
  try {
    const { categoryOrders } = req.body;

    if (!categoryOrders || !Array.isArray(categoryOrders)) {
      return res.status(400).json({
        success: false,
        message: 'Category orders array is required'
      });
    }

    // Update sort orders
    const updatePromises = categoryOrders.map(({ id, sortOrder }) =>
      Category.update({ sortOrder }, { where: { id } })
    );

    await Promise.all(updatePromises);

    // Log admin action
    await AuditLog.logUserAction(req.user.id, 'REORDER_CATEGORIES', 'CATEGORY', null, {
      ipAddress: req.ip,
      userAgent: req.get('User-Agent'),
      metadata: { categoryOrders },
      severity: 'medium'
    });

    res.json({
      success: true,
      message: 'Categories reordered successfully'
    });

  } catch (error) {
    console.error('Reorder categories error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to reorder categories',
      error: error.message
    });
  }
};

// Get category statistics
const getCategoryStatistics = async (req, res) => {
  try {
    const stats = await Promise.all([
      // Total categories
      Category.count(),

      // Active categories
      Category.count({ where: { isActive: true } }),

      // Root categories (no parent)
      Category.count({ where: { parentId: null } }),

      // Categories with products
      Category.count({
        include: [{
          model: Product,
          as: 'products',
          required: true
        }]
      }),

      // Categories created this month
      Category.count({
        where: {
          createdAt: {
            [Op.gte]: new Date(new Date().getFullYear(), new Date().getMonth(), 1)
          }
        }
      }),

      // Categories by level
      sequelize.query(`
        WITH RECURSIVE category_hierarchy AS (
          SELECT id, name, parent_id, 0 as level
          FROM categories
          WHERE parent_id IS NULL

          UNION ALL

          SELECT c.id, c.name, c.parent_id, ch.level + 1
          FROM categories c
          INNER JOIN category_hierarchy ch ON c.parent_id = ch.id
        )
        SELECT level, COUNT(*) as count
        FROM category_hierarchy
        GROUP BY level
        ORDER BY level
      `, { type: sequelize.QueryTypes.SELECT }),

      // Top categories by product count
      Category.findAll({
        attributes: [
          'id', 'name', 'slug',
          [sequelize.fn('COUNT', sequelize.col('products.id')), 'productCount']
        ],
        include: [{
          model: Product,
          as: 'products',
          attributes: []
        }],
        group: ['Category.id'],
        order: [[sequelize.fn('COUNT', sequelize.col('products.id')), 'DESC']],
        limit: 10
      })
    ]);

    const [
      totalCategories,
      activeCategories,
      rootCategories,
      categoriesWithProducts,
      categoriesThisMonth,
      categoriesByLevel,
      topCategoriesByProducts
    ] = stats;

    res.json({
      success: true,
      data: {
        totalCategories,
        activeCategories,
        inactiveCategories: totalCategories - activeCategories,
        rootCategories,
        categoriesWithProducts,
        categoriesWithoutProducts: totalCategories - categoriesWithProducts,
        categoriesThisMonth,
        categoriesByLevel,
        topCategoriesByProducts
      }
    });

  } catch (error) {
    console.error('Get category statistics error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch category statistics',
      error: error.message
    });
  }
};

module.exports = {
  getCategories,
  getCategoryById,
  createCategory,
  updateCategory,
  deleteCategory,
  bulkUpdateCategories,
  reorderCategories,
  getCategoryStatistics
};
