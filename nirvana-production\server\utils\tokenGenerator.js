const crypto = require('crypto');

/**
 * Token Generator Utility for Nirvana Organics
 * Provides secure token generation for password resets and email verification
 */

/**
 * Generate a cryptographically secure random token
 * @param {number} length - Length of the token in bytes (default: 32)
 * @returns {string} - Hex encoded token
 */
const generateSecureToken = (length = 32) => {
  return crypto.randomBytes(length).toString('hex');
};

/**
 * Generate a password reset token with expiration
 * @param {number} expirationHours - Hours until token expires (default: 1)
 * @returns {Object} - Object containing token and expiration date
 */
const generatePasswordResetToken = (expirationHours = 1) => {
  const token = generateSecureToken(32); // 64 character hex string
  const expiresAt = new Date(Date.now() + (expirationHours * 60 * 60 * 1000));
  
  return {
    token,
    expiresAt,
    isValid: true
  };
};

/**
 * Generate an email verification token
 * @param {number} expirationHours - Hours until token expires (default: 24)
 * @returns {Object} - Object containing token and expiration date
 */
const generateEmailVerificationToken = (expirationHours = 24) => {
  const token = generateSecureToken(32);
  const expiresAt = new Date(Date.now() + (expirationHours * 60 * 60 * 1000));
  
  return {
    token,
    expiresAt,
    isValid: true
  };
};

/**
 * Validate if a token is still valid (not expired)
 * @param {Date} expirationDate - Token expiration date
 * @returns {boolean} - True if token is still valid
 */
const isTokenValid = (expirationDate) => {
  if (!expirationDate) return false;
  return new Date() < new Date(expirationDate);
};

/**
 * Generate a secure session token
 * @param {number} length - Length of the token in bytes (default: 48)
 * @returns {string} - Base64 encoded token
 */
const generateSessionToken = (length = 48) => {
  return crypto.randomBytes(length).toString('base64url');
};

/**
 * Generate a CSRF token
 * @param {number} length - Length of the token in bytes (default: 24)
 * @returns {string} - Base64 encoded token
 */
const generateCSRFToken = (length = 24) => {
  return crypto.randomBytes(length).toString('base64url');
};

/**
 * Hash a token for secure storage (one-way hash)
 * @param {string} token - Token to hash
 * @returns {string} - SHA-256 hash of the token
 */
const hashToken = (token) => {
  return crypto.createHash('sha256').update(token).digest('hex');
};

/**
 * Compare a plain token with its hash
 * @param {string} plainToken - Plain text token
 * @param {string} hashedToken - Hashed token
 * @returns {boolean} - True if tokens match
 */
const compareTokens = (plainToken, hashedToken) => {
  const hashedPlainToken = hashToken(plainToken);
  return crypto.timingSafeEqual(
    Buffer.from(hashedPlainToken, 'hex'),
    Buffer.from(hashedToken, 'hex')
  );
};

/**
 * Generate a time-based one-time password (TOTP) style token
 * @param {string} secret - Secret key for generation
 * @param {number} timeStep - Time step in seconds (default: 30)
 * @returns {string} - 6-digit numeric token
 */
const generateTOTPToken = (secret, timeStep = 30) => {
  const time = Math.floor(Date.now() / 1000 / timeStep);
  const hmac = crypto.createHmac('sha1', secret);
  hmac.update(Buffer.from(time.toString(16).padStart(16, '0'), 'hex'));
  const hash = hmac.digest();
  
  const offset = hash[hash.length - 1] & 0xf;
  const code = ((hash[offset] & 0x7f) << 24) |
               ((hash[offset + 1] & 0xff) << 16) |
               ((hash[offset + 2] & 0xff) << 8) |
               (hash[offset + 3] & 0xff);
  
  return (code % 1000000).toString().padStart(6, '0');
};

/**
 * Generate a secure API key
 * @param {string} prefix - Optional prefix for the API key
 * @param {number} length - Length of the random part in bytes (default: 32)
 * @returns {string} - API key with optional prefix
 */
const generateAPIKey = (prefix = '', length = 32) => {
  const randomPart = crypto.randomBytes(length).toString('hex');
  return prefix ? `${prefix}_${randomPart}` : randomPart;
};

/**
 * Generate a secure password reset token with additional metadata
 * @param {Object} options - Configuration options
 * @param {number} options.expirationHours - Hours until expiration (default: 1)
 * @param {string} options.userId - User ID for additional security
 * @param {string} options.email - User email for additional security
 * @returns {Object} - Token object with metadata
 */
const generateSecurePasswordResetToken = (options = {}) => {
  const {
    expirationHours = 1,
    userId = null,
    email = null
  } = options;
  
  const token = generateSecureToken(32);
  const expiresAt = new Date(Date.now() + (expirationHours * 60 * 60 * 1000));
  
  // Create additional security metadata
  const metadata = {
    createdAt: new Date(),
    expiresAt,
    userId,
    email: email ? email.toLowerCase() : null,
    tokenType: 'password_reset',
    version: '1.0'
  };
  
  return {
    token,
    expiresAt,
    metadata,
    isValid: true
  };
};

/**
 * Validate token format and structure
 * @param {string} token - Token to validate
 * @param {string} expectedType - Expected token type ('hex', 'base64', 'base64url')
 * @returns {boolean} - True if token format is valid
 */
const validateTokenFormat = (token, expectedType = 'hex') => {
  if (!token || typeof token !== 'string') return false;
  
  switch (expectedType) {
    case 'hex':
      return /^[a-f0-9]+$/i.test(token) && token.length % 2 === 0;
    case 'base64':
      return /^[A-Za-z0-9+/]+=*$/.test(token);
    case 'base64url':
      return /^[A-Za-z0-9_-]+$/.test(token);
    default:
      return false;
  }
};

module.exports = {
  generateSecureToken,
  generatePasswordResetToken,
  generateEmailVerificationToken,
  generateSecurePasswordResetToken,
  generateSessionToken,
  generateCSRFToken,
  generateTOTPToken,
  generateAPIKey,
  isTokenValid,
  hashToken,
  compareTokens,
  validateTokenFormat
};
