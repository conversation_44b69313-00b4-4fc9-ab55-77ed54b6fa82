#!/bin/bash
# Make script executable: chmod +x fix-admin-assets-comprehensive.sh

# Nirvana Organics - Comprehensive Admin Assets Fix
# Fixes 404 errors for admin frontend assets by addressing deployment and Nginx configuration

set -e

echo "🔧 Nirvana Organics - Comprehensive Admin Assets Fix"
echo "===================================================="

# Color codes
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}✅ $1${NC}"
}

warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if running as correct user
if [ "$USER" != "Nirvana" ]; then
    error "This script must be run as the 'Nirvana' user"
    echo "Please run: sudo -u Nirvana $0"
    exit 1
fi

# Step 1: Diagnose current state
log "Step 1: Diagnosing current admin assets state..."

TARGET_ADMIN="/var/www/nirvana-frontend-test/admin"
MISSING_JS="admin-DitisM-I.js"
MISSING_CSS="admin-TY7ZtfqV.css"

echo "🔍 Current State Analysis:"
echo "=========================="

# Check source files
if [ -f "dist-admin/assets/$MISSING_JS" ]; then
    success "Source JavaScript exists: dist-admin/assets/$MISSING_JS"
    SOURCE_JS_SIZE=$(stat -c%s "dist-admin/assets/$MISSING_JS")
    echo "  Size: ${SOURCE_JS_SIZE} bytes"
else
    error "Source JavaScript missing: dist-admin/assets/$MISSING_JS"
    echo "  Need to rebuild admin frontend"
fi

if [ -f "dist-admin/assets/$MISSING_CSS" ]; then
    success "Source CSS exists: dist-admin/assets/$MISSING_CSS"
    SOURCE_CSS_SIZE=$(stat -c%s "dist-admin/assets/$MISSING_CSS")
    echo "  Size: ${SOURCE_CSS_SIZE} bytes"
else
    error "Source CSS missing: dist-admin/assets/$MISSING_CSS"
    echo "  Need to rebuild admin frontend"
fi

# Check deployment directory
if [ -d "$TARGET_ADMIN" ]; then
    success "Admin deployment directory exists: $TARGET_ADMIN"
else
    warning "Admin deployment directory missing: $TARGET_ADMIN"
fi

# Check deployed files
if [ -f "$TARGET_ADMIN/assets/$MISSING_JS" ]; then
    success "Deployed JavaScript exists: $TARGET_ADMIN/assets/$MISSING_JS"
    DEPLOYED_JS_SIZE=$(stat -c%s "$TARGET_ADMIN/assets/$MISSING_JS")
    echo "  Size: ${DEPLOYED_JS_SIZE} bytes"
else
    error "Deployed JavaScript missing: $TARGET_ADMIN/assets/$MISSING_JS"
fi

if [ -f "$TARGET_ADMIN/assets/$MISSING_CSS" ]; then
    success "Deployed CSS exists: $TARGET_ADMIN/assets/$MISSING_CSS"
    DEPLOYED_CSS_SIZE=$(stat -c%s "$TARGET_ADMIN/assets/$MISSING_CSS")
    echo "  Size: ${DEPLOYED_CSS_SIZE} bytes"
else
    error "Deployed CSS missing: $TARGET_ADMIN/assets/$MISSING_CSS"
fi

echo ""

# Step 2: Rebuild admin frontend if needed
if [ ! -f "dist-admin/assets/$MISSING_JS" ] || [ ! -f "dist-admin/assets/$MISSING_CSS" ]; then
    log "Step 2: Rebuilding admin frontend (source files missing)..."
    
    if [ -f "package.json" ]; then
        echo "📦 Running admin frontend build..."
        npm run build:admin:test
        
        # Verify build completed
        if [ -f "dist-admin/assets/$MISSING_JS" ] && [ -f "dist-admin/assets/$MISSING_CSS" ]; then
            success "Admin frontend build completed successfully"
        else
            error "Admin frontend build failed - assets still missing"
            echo "Available assets:"
            ls -la dist-admin/assets/ 2>/dev/null || echo "No assets directory found"
            exit 1
        fi
    else
        error "package.json not found - cannot rebuild frontend"
        exit 1
    fi
else
    log "Step 2: Source files exist, skipping rebuild"
fi

# Step 3: Deploy admin frontend assets
log "Step 3: Deploying admin frontend assets..."

# Create target directories with proper permissions
sudo mkdir -p "$TARGET_ADMIN/assets"
sudo chown -R Nirvana:Nirvana /var/www/nirvana-frontend-test/

# Copy all admin frontend files
cp -r dist-admin/* "$TARGET_ADMIN/"

# Verify critical files were copied
VERIFICATION_FAILED=false

if [ -f "$TARGET_ADMIN/admin.html" ]; then
    success "admin.html deployed"
else
    error "admin.html deployment failed"
    VERIFICATION_FAILED=true
fi

if [ -f "$TARGET_ADMIN/assets/$MISSING_JS" ]; then
    success "JavaScript assets deployed"
    DEPLOYED_JS_SIZE=$(stat -c%s "$TARGET_ADMIN/assets/$MISSING_JS")
    echo "  Size: ${DEPLOYED_JS_SIZE} bytes"
else
    error "JavaScript assets deployment failed"
    VERIFICATION_FAILED=true
fi

if [ -f "$TARGET_ADMIN/assets/$MISSING_CSS" ]; then
    success "CSS assets deployed"
    DEPLOYED_CSS_SIZE=$(stat -c%s "$TARGET_ADMIN/assets/$MISSING_CSS")
    echo "  Size: ${DEPLOYED_CSS_SIZE} bytes"
else
    error "CSS assets deployment failed"
    VERIFICATION_FAILED=true
fi

if [ "$VERIFICATION_FAILED" = true ]; then
    error "Asset deployment verification failed"
    echo "Deployed files:"
    ls -la "$TARGET_ADMIN/" 2>/dev/null || echo "Directory not accessible"
    echo "Deployed assets:"
    ls -la "$TARGET_ADMIN/assets/" 2>/dev/null || echo "Assets directory not accessible"
    exit 1
fi

# Step 4: Set proper permissions
log "Step 4: Setting proper file permissions..."

# Set ownership
sudo chown -R Nirvana:Nirvana /var/www/nirvana-frontend-test/

# Set directory permissions (755)
find /var/www/nirvana-frontend-test/ -type d -exec chmod 755 {} \;

# Set file permissions (644)
find /var/www/nirvana-frontend-test/ -type f -exec chmod 644 {} \;

success "File permissions set correctly"

# Step 5: Fix Nginx configuration for admin assets
log "Step 5: Checking Nginx configuration for admin assets..."

NGINX_CONFIG="nginx-site.conf"
if [ -f "$NGINX_CONFIG" ]; then
    # Check if admin assets location block exists
    if grep -q "location /admin/assets" "$NGINX_CONFIG"; then
        success "Admin assets location block already exists in Nginx config"
    else
        warning "Admin assets location block missing in Nginx config"
        echo "The current Nginx configuration may not properly serve admin assets."
        echo "Consider adding a specific location block for /admin/assets/"
    fi
else
    warning "Nginx configuration file not found: $NGINX_CONFIG"
fi

# Step 6: Test asset accessibility
log "Step 6: Testing asset accessibility..."

echo "🌐 HTTP Accessibility Tests:"
echo "============================"

# Test admin.html
echo "Testing admin.html:"
ADMIN_HTML_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "https://test.shopnirvanaorganics.com/admin/" 2>/dev/null || echo "000")
if [ "$ADMIN_HTML_STATUS" = "200" ]; then
    success "admin.html accessible (HTTP $ADMIN_HTML_STATUS)"
else
    error "admin.html not accessible (HTTP $ADMIN_HTML_STATUS)"
fi

# Test JavaScript asset
echo "Testing JavaScript asset:"
JS_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "https://test.shopnirvanaorganics.com/admin/assets/$MISSING_JS" 2>/dev/null || echo "000")
if [ "$JS_STATUS" = "200" ]; then
    success "JavaScript asset accessible (HTTP $JS_STATUS)"
else
    error "JavaScript asset not accessible (HTTP $JS_STATUS)"
    echo "  URL: https://test.shopnirvanaorganics.com/admin/assets/$MISSING_JS"
fi

# Test CSS asset
echo "Testing CSS asset:"
CSS_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "https://test.shopnirvanaorganics.com/admin/assets/$MISSING_CSS" 2>/dev/null || echo "000")
if [ "$CSS_STATUS" = "200" ]; then
    success "CSS asset accessible (HTTP $CSS_STATUS)"
else
    error "CSS asset not accessible (HTTP $CSS_STATUS)"
    echo "  URL: https://test.shopnirvanaorganics.com/admin/assets/$MISSING_CSS"
fi

# Step 7: Provide detailed troubleshooting if assets still not accessible
if [ "$JS_STATUS" != "200" ] || [ "$CSS_STATUS" != "200" ]; then
    echo ""
    log "Step 7: Assets still not accessible - providing detailed troubleshooting..."
    
    echo "🔧 DETAILED TROUBLESHOOTING"
    echo "=========================="
    
    echo "1. File System Check:"
    echo "   Admin directory: $(ls -ld "$TARGET_ADMIN" 2>/dev/null || echo "NOT FOUND")"
    echo "   Assets directory: $(ls -ld "$TARGET_ADMIN/assets" 2>/dev/null || echo "NOT FOUND")"
    echo "   JavaScript file: $(ls -l "$TARGET_ADMIN/assets/$MISSING_JS" 2>/dev/null || echo "NOT FOUND")"
    echo "   CSS file: $(ls -l "$TARGET_ADMIN/assets/$MISSING_CSS" 2>/dev/null || echo "NOT FOUND")"
    
    echo ""
    echo "2. Nginx Configuration Issues:"
    echo "   The static assets location block in nginx-site.conf may not properly"
    echo "   handle admin assets. The current configuration uses:"
    echo "   'try_files \$uri =404' which looks in the main root directory."
    echo ""
    echo "3. Recommended Nginx Fix:"
    echo "   Add a specific location block for admin assets before the general"
    echo "   static assets block:"
    echo ""
    echo "   # Admin assets - specific handling"
    echo "   location /admin/assets/ {"
    echo "       alias /var/www/nirvana-frontend-test/admin/assets/;"
    echo "       expires 1h;"
    echo "       add_header Cache-Control \"public\";"
    echo "       add_header Vary \"Accept-Encoding\";"
    echo "   }"
    echo ""
    echo "4. Manual Verification Commands:"
    echo "   sudo nginx -t  # Test Nginx configuration"
    echo "   sudo systemctl reload nginx  # Reload Nginx"
    echo "   curl -I https://test.shopnirvanaorganics.com/admin/assets/$MISSING_JS"
    echo "   curl -I https://test.shopnirvanaorganics.com/admin/assets/$MISSING_CSS"
    
else
    log "Step 7: All assets are accessible - fix completed successfully!"
fi

# Step 8: Final summary
echo ""
log "Step 8: Fix completed!"

echo ""
echo "📋 COMPREHENSIVE FIX SUMMARY"
echo "============================"

if [ "$JS_STATUS" = "200" ] && [ "$CSS_STATUS" = "200" ]; then
    success "✅ All admin assets are now accessible"
    success "✅ 404 errors should be resolved"
    success "✅ Admin panel should load completely"
else
    warning "⚠️  Some assets may still have accessibility issues"
    echo "Additional Nginx configuration may be required"
fi

echo ""
echo "📊 ASSET STATUS"
echo "==============="
echo "JavaScript: HTTP $JS_STATUS - https://test.shopnirvanaorganics.com/admin/assets/$MISSING_JS"
echo "CSS: HTTP $CSS_STATUS - https://test.shopnirvanaorganics.com/admin/assets/$MISSING_CSS"

echo ""
echo "🔍 VERIFICATION STEPS"
echo "===================="
echo "1. Open browser: https://test.shopnirvanaorganics.com/admin/"
echo "2. Check browser console for 404 errors (should be gone)"
echo "3. Verify admin panel loads completely with styling"
echo "4. Test admin functionality"

echo ""
success "🎉 Comprehensive admin assets fix completed!"
