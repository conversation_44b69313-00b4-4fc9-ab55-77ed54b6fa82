const { Order, Product } = require('../models');
const { validationResult } = require('express-validator');
const SquareService = require('../services/squareService');
const CustomerAnalyticsService = require('../services/customerAnalyticsService');
const crypto = require('crypto');

// Create guest order
const createGuestOrder = async (req, res) => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const {
      items,
      billingAddress,
      shippingAddress,
      paymentMethod,
      sourceId,
      guestInfo,
      customerGender,
      shippingMethod,
      shippingCost
    } = req.body;

    // Determine traffic source from request
    const trafficData = CustomerAnalyticsService.determineTrafficSource(req);

    // Validate guest info
    if (!guestInfo || !guestInfo.email || !guestInfo.firstName || !guestInfo.lastName) {
      return res.status(400).json({
        success: false,
        message: 'Guest information (email, firstName, lastName) is required'
      });
    }

    // Age verification for guest checkout (21+ required)
    if (!guestInfo.ageVerified) {
      return res.status(400).json({
        success: false,
        message: 'Age verification is required. You must be 21 or older.'
      });
    }

    // Validate and calculate order totals
    let subtotal = 0;
    const validatedItems = [];

    for (const item of items) {
      const product = await Product.findByPk(item.productId);
      if (!product || product.status !== 'active') {
        return res.status(400).json({
          success: false,
          message: `Product ${item.productId} is not available`
        });
      }

      // Check stock
      if (product.trackQuantity && product.quantity < item.quantity) {
        return res.status(400).json({
          success: false,
          message: `Insufficient stock for ${product.name}`
        });
      }

      const itemPrice = item.variant?.price || product.price;
      const itemTotal = itemPrice * item.quantity;
      subtotal += itemTotal;

      validatedItems.push({
        productId: product.id,
        name: product.name,
        slug: product.slug,
        price: itemPrice,
        quantity: item.quantity,
        variant: item.variant || null,
        total: itemTotal
      });
    }

    // Calculate tax and shipping
    const shippingState = shippingAddress.state || 'CO';
    const taxRates = {
      'AL': 9.22, 'AK': 1.76, 'AZ': 8.37, 'AR': 9.43, 'CA': 10.58, 'CO': 7.63,
      'CT': 6.35, 'DE': 0.00, 'FL': 7.05, 'GA': 7.29, 'HI': 4.58, 'ID': 6.03,
      'IL': 8.74, 'IN': 7.00, 'IA': 6.82, 'KS': 8.67, 'KY': 6.00, 'LA': 9.45,
      'ME': 5.50, 'MD': 6.00, 'MA': 6.25, 'MI': 6.00, 'MN': 7.43, 'MS': 7.07,
      'MO': 8.13, 'MT': 0.00, 'NE': 6.85, 'NV': 8.14, 'NH': 0.00, 'NJ': 6.60,
      'NM': 7.82, 'NY': 8.49, 'NC': 6.97, 'ND': 6.85, 'OH': 7.17, 'OK': 8.92,
      'OR': 0.00, 'PA': 6.34, 'RI': 7.00, 'SC': 7.43, 'SD': 6.10, 'TN': 9.47,
      'TX': 8.19, 'UT': 7.09, 'VT': 6.18, 'VA': 5.30, 'WA': 9.60, 'WV': 6.59,
      'WI': 5.44, 'WY': 5.36, 'DC': 6.00
    };

    const taxRate = taxRates[shippingState] || 7.63;
    const tax = subtotal * (taxRate / 100);

    // Calculate shipping using enhanced shipping service
    let shipping = shippingCost;
    if (shipping === undefined || shipping === null) {
      // Fallback to legacy calculation if not provided
      const restrictedStates = ['ID', 'IA', 'SD'];
      const alaskaHawaii = ['AK', 'HI'];
      const territories = ['PR', 'VI', 'GU', 'AS', 'MP'];

      if (restrictedStates.includes(shippingState)) {
        shipping = 0; // No shipping to restricted states
      } else if (subtotal >= 100) {
        shipping = 0; // Free shipping over $100
      } else if (alaskaHawaii.includes(shippingState)) {
        shipping = 12.99; // Higher rate for AK/HI
      } else if (territories.includes(shippingState)) {
        shipping = 19.99; // Highest rate for territories
      } else {
        shipping = shippingMethod === 'express' ? 19.99 : 9.99; // Enhanced shipping rates
      }
    }

    const total = subtotal + tax + shipping;

    // Generate order number
    const orderNumber = `NO-${Date.now()}-${Math.random().toString(36).substr(2, 9).toUpperCase()}`;

    // Generate guest tracking token
    const guestTrackingToken = crypto.randomBytes(32).toString('hex');

    // Create order
    const orderData = {
      orderNumber,
      userId: null, // Guest order
      guestInfo,
      guestTrackingToken,
      items: validatedItems,
      billingAddress,
      shippingAddress,
      subtotal,
      tax,
      shipping,
      total,
      status: 'pending',
      paymentStatus: 'pending',
      paymentMethod,
      refundAmount: 0,
      // Enhanced customer data
      customerGender: customerGender || null,
      customerMembershipType: 'first-time', // All guest orders are first-time
      trafficSource: trafficData.source,
      referralSource: trafficData.details,
      shippingMethod: shippingMethod || 'regular',
      shippingCost: shipping,
      isFirstOrder: true,
      customerLifetimeValue: total, // For guest orders, LTV is just this order
      orderSource: 'web',
      statusHistory: [{
        status: 'pending',
        timestamp: new Date(),
        note: 'Order created'
      }]
    };

    const order = await Order.create(orderData);

    // Process payment if Square payment
    if (paymentMethod === 'square' && sourceId) {
      const squarePaymentResult = await SquareService.createPayment({
        sourceId,
        amountMoney: {
          amount: Math.round(total * 100), // Convert to cents
          currency: 'USD'
        },
        orderId: orderNumber,
        buyerEmailAddress: guestInfo.email,
        billingAddress,
        shippingAddress
      });

      if (squarePaymentResult.success) {
        await order.update({
          squarePaymentId: squarePaymentResult.payment.id,
          paymentId: squarePaymentResult.payment.id,
          paymentStatus: squarePaymentResult.payment.status === 'COMPLETED' ? 'paid' : 'pending'
        });
      } else {
        return res.status(400).json({
          success: false,
          message: 'Payment processing failed',
          error: squarePaymentResult.error
        });
      }
    }

    // Update product quantities
    for (const item of validatedItems) {
      const product = await Product.findByPk(item.productId);
      if (product && product.trackQuantity) {
        await product.update({
          quantity: product.quantity - item.quantity,
          salesCount: product.salesCount + item.quantity
        });
      }
    }

    res.status(201).json({
      success: true,
      message: 'Guest order created successfully',
      data: {
        order: {
          id: order.id,
          orderNumber: order.orderNumber,
          total: order.total,
          status: order.status,
          paymentStatus: order.paymentStatus,
          guestTrackingToken: order.guestTrackingToken
        }
      }
    });

  } catch (error) {
    console.error('Create guest order error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create guest order',
      error: error.message
    });
  }
};

// Track guest order
const trackGuestOrder = async (req, res) => {
  try {
    const { orderNumber, email, trackingToken } = req.query;

    if (!orderNumber || (!email && !trackingToken)) {
      return res.status(400).json({
        success: false,
        message: 'Order number and either email or tracking token are required'
      });
    }

    let whereClause = { orderNumber };

    if (trackingToken) {
      whereClause.guestTrackingToken = trackingToken;
    } else if (email) {
      whereClause['guestInfo.email'] = email.toLowerCase();
    }

    const order = await Order.findOne({ where: whereClause });

    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'Order not found'
      });
    }

    res.json({
      success: true,
      data: {
        order: {
          id: order.id,
          orderNumber: order.orderNumber,
          status: order.status,
          paymentStatus: order.paymentStatus,
          total: order.total,
          items: order.items,
          shippingAddress: order.shippingAddress,
          statusHistory: order.statusHistory,
          createdAt: order.createdAt,
          estimatedDelivery: order.estimatedDelivery
        }
      }
    });

  } catch (error) {
    console.error('Track guest order error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to track guest order',
      error: error.message
    });
  }
};

module.exports = {
  createGuestOrder,
  trackGuestOrder
};
