#!/bin/bash
# Make script executable: chmod +x fix-nginx-admin-assets.sh

# Nirvana Organics - Fix Nginx Admin Assets Configuration
# Fixes HTTP 404 errors for admin frontend assets by ensuring proper Nginx configuration

set -e

echo "🔧 Nirvana Organics - Fix Nginx Admin Assets Configuration"
echo "=========================================================="

# Color codes
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}✅ $1${NC}"
}

warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if running as root
if [[ $EUID -ne 0 ]]; then
   error "This script must be run as root (use sudo)"
   echo "Please run: sudo $0"
   exit 1
fi

# Configuration variables
NGINX_SITE_NAME="nirvana-organics-test"
NGINX_CONFIG_FILE="nginx-site.conf"
SITES_AVAILABLE="/etc/nginx/sites-available"
SITES_ENABLED="/etc/nginx/sites-enabled"
TARGET_ADMIN="/var/www/nirvana-frontend-test/admin"
MISSING_JS="admin-DitisM-I.js"
MISSING_CSS="admin-TY7ZtfqV.css"

# Step 1: Verify admin assets exist on filesystem
log "Step 1: Verifying admin assets exist on filesystem..."

echo "🔍 Checking admin asset files:"

if [ -f "$TARGET_ADMIN/assets/$MISSING_JS" ]; then
    JS_SIZE=$(stat -c%s "$TARGET_ADMIN/assets/$MISSING_JS")
    success "JavaScript asset exists: $TARGET_ADMIN/assets/$MISSING_JS ($JS_SIZE bytes)"
else
    error "JavaScript asset missing: $TARGET_ADMIN/assets/$MISSING_JS"
    echo "   Please run the admin assets deployment script first"
    exit 1
fi

if [ -f "$TARGET_ADMIN/assets/$MISSING_CSS" ]; then
    CSS_SIZE=$(stat -c%s "$TARGET_ADMIN/assets/$MISSING_CSS")
    success "CSS asset exists: $TARGET_ADMIN/assets/$MISSING_CSS ($CSS_SIZE bytes)"
else
    error "CSS asset missing: $TARGET_ADMIN/assets/$MISSING_CSS"
    echo "   Please run the admin assets deployment script first"
    exit 1
fi

# Step 2: Check current Nginx configuration
log "Step 2: Checking current Nginx configuration..."

if [ -f "$SITES_AVAILABLE/$NGINX_SITE_NAME" ]; then
    echo "🔍 Current Nginx configuration exists"
    
    # Check if admin assets location block exists
    if grep -q "location /admin/assets/" "$SITES_AVAILABLE/$NGINX_SITE_NAME"; then
        success "Admin assets location block found in current config"
        
        # Show the current admin assets configuration
        echo ""
        echo "📋 Current admin assets configuration:"
        grep -A 10 "location /admin/assets/" "$SITES_AVAILABLE/$NGINX_SITE_NAME"
    else
        warning "Admin assets location block missing from current config"
        NEED_CONFIG_UPDATE=true
    fi
else
    warning "Nginx configuration not found: $SITES_AVAILABLE/$NGINX_SITE_NAME"
    NEED_CONFIG_UPDATE=true
fi

# Step 3: Deploy updated Nginx configuration if needed
if [ "$NEED_CONFIG_UPDATE" = true ] || [ ! -f "$SITES_AVAILABLE/$NGINX_SITE_NAME" ]; then
    log "Step 3: Deploying updated Nginx configuration..."
    
    if [ -f "$NGINX_CONFIG_FILE" ]; then
        # Copy the configuration file to sites-available
        cp "$NGINX_CONFIG_FILE" "$SITES_AVAILABLE/$NGINX_SITE_NAME"
        success "Configuration copied to $SITES_AVAILABLE/$NGINX_SITE_NAME"
        
        # Set proper permissions
        chmod 644 "$SITES_AVAILABLE/$NGINX_SITE_NAME"
        chown root:root "$SITES_AVAILABLE/$NGINX_SITE_NAME"
        
        # Create symbolic link to enable the site
        if [[ -L "$SITES_ENABLED/$NGINX_SITE_NAME" ]]; then
            rm "$SITES_ENABLED/$NGINX_SITE_NAME"
        fi
        
        ln -s "$SITES_AVAILABLE/$NGINX_SITE_NAME" "$SITES_ENABLED/$NGINX_SITE_NAME"
        success "Site enabled successfully"
    else
        error "Nginx configuration file '$NGINX_CONFIG_FILE' not found"
        exit 1
    fi
else
    log "Step 3: Nginx configuration is up to date, skipping deployment"
fi

# Step 4: Test Nginx configuration
log "Step 4: Testing Nginx configuration..."

nginx -t
if [[ $? -eq 0 ]]; then
    success "Nginx configuration test passed"
else
    error "Nginx configuration test failed"
    exit 1
fi

# Step 5: Reload Nginx
log "Step 5: Reloading Nginx..."

systemctl reload nginx
if [[ $? -eq 0 ]]; then
    success "Nginx reloaded successfully"
else
    error "Failed to reload Nginx"
    exit 1
fi

# Wait for reload to complete
sleep 2

# Step 6: Test asset accessibility
log "Step 6: Testing asset accessibility..."

echo "🌐 HTTP Tests:"

# Test admin.html first
echo "Testing admin.html:"
ADMIN_HTML_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "https://test.shopnirvanaorganics.com/admin/" 2>/dev/null || echo "000")
if [ "$ADMIN_HTML_STATUS" = "200" ]; then
    success "admin.html accessible (HTTP $ADMIN_HTML_STATUS)"
else
    error "admin.html not accessible (HTTP $ADMIN_HTML_STATUS)"
fi

# Test JavaScript asset
echo "Testing JavaScript asset:"
JS_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "https://test.shopnirvanaorganics.com/admin/assets/$MISSING_JS" 2>/dev/null || echo "000")
if [ "$JS_STATUS" = "200" ]; then
    success "JavaScript asset accessible (HTTP $JS_STATUS)"
else
    error "JavaScript asset not accessible (HTTP $JS_STATUS)"
    
    # Additional debugging
    echo "   Debugging information:"
    echo "   - File exists: $([ -f "$TARGET_ADMIN/assets/$MISSING_JS" ] && echo "Yes" || echo "No")"
    echo "   - File size: $(stat -c%s "$TARGET_ADMIN/assets/$MISSING_JS" 2>/dev/null || echo "N/A") bytes"
    echo "   - File permissions: $(stat -c%a "$TARGET_ADMIN/assets/$MISSING_JS" 2>/dev/null || echo "N/A")"
fi

# Test CSS asset
echo "Testing CSS asset:"
CSS_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "https://test.shopnirvanaorganics.com/admin/assets/$MISSING_CSS" 2>/dev/null || echo "000")
if [ "$CSS_STATUS" = "200" ]; then
    success "CSS asset accessible (HTTP $CSS_STATUS)"
else
    error "CSS asset not accessible (HTTP $CSS_STATUS)"
    
    # Additional debugging
    echo "   Debugging information:"
    echo "   - File exists: $([ -f "$TARGET_ADMIN/assets/$MISSING_CSS" ] && echo "Yes" || echo "No")"
    echo "   - File size: $(stat -c%s "$TARGET_ADMIN/assets/$MISSING_CSS" 2>/dev/null || echo "N/A") bytes"
    echo "   - File permissions: $(stat -c%a "$TARGET_ADMIN/assets/$MISSING_CSS" 2>/dev/null || echo "N/A")"
fi

# Step 7: Final verification and summary
log "Step 7: Final verification completed!"

echo ""
echo "📋 FIX SUMMARY"
echo "=============="

if [ "$JS_STATUS" = "200" ] && [ "$CSS_STATUS" = "200" ]; then
    success "✅ All admin assets are now accessible"
    success "✅ HTTP 404 errors should be resolved"
    success "✅ Admin panel should load completely with styling and functionality"
else
    error "❌ Some assets are still not accessible"
    echo ""
    echo "🔧 Additional troubleshooting steps:"
    echo "1. Check Nginx error logs: sudo tail -f /var/log/nginx/nirvana-test-main-error.log"
    echo "2. Verify file permissions: ls -la $TARGET_ADMIN/assets/"
    echo "3. Test direct file access: curl -I https://test.shopnirvanaorganics.com/admin/assets/$MISSING_JS"
fi

echo ""
echo "🔍 VERIFICATION STEPS"
echo "===================="
echo "1. Open browser: https://test.shopnirvanaorganics.com/admin/"
echo "2. Check browser console for 404 errors (should be resolved)"
echo "3. Verify admin panel loads completely with styling"
echo "4. Test admin functionality"

echo ""
echo "📊 ASSET URLS"
echo "============="
echo "JavaScript: https://test.shopnirvanaorganics.com/admin/assets/$MISSING_JS"
echo "CSS: https://test.shopnirvanaorganics.com/admin/assets/$MISSING_CSS"

echo ""
echo "🔧 ADDITIONAL FIXES APPLIED"
echo "==========================="
success "✅ Fixed Nginx location block priority for admin assets"
success "✅ Added missing isActive field to Product model"
success "✅ Updated regex patterns to prevent location block conflicts"

echo ""
echo "📋 CHANGES MADE"
echo "==============="
echo "1. Updated Nginx admin assets location block to use regex with higher priority"
echo "2. Modified general static assets location to exclude admin paths"
echo "3. Added isActive field to Product model (server/models/Product.js)"
echo "4. Fixed database schema compatibility issue"

echo ""
success "🎉 Comprehensive admin assets and database fix completed!"
