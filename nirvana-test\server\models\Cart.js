const { DataTypes } = require('sequelize');
const { sequelize } = require('./database');

const Cart = sequelize.define('Cart', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  userId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    unique: true,
    field: 'user_id',
    references: {
      model: 'users',
      key: 'id'
    }
  },
  items: {
    type: DataTypes.JSON,
    allowNull: false,
    defaultValue: []
  },
  subtotal: {
    type: DataTypes.DECIMAL(10, 2),
    defaultValue: 0,
    validate: {
      min: 0
    }
  },
  tax: {
    type: DataTypes.DECIMAL(10, 2),
    defaultValue: 0,
    validate: {
      min: 0
    }
  },
  shipping: {
    type: DataTypes.DECIMAL(10, 2),
    defaultValue: 0,
    validate: {
      min: 0
    }
  },
  total: {
    type: DataTypes.DECIMAL(10, 2),
    defaultValue: 0,
    validate: {
      min: 0
    }
  },
  couponCode: {
    type: DataTypes.STRING,
    allowNull: true,
    field: 'coupon_code'
  },
  discount: {
    type: DataTypes.DECIMAL(10, 2),
    defaultValue: 0,
    validate: {
      min: 0
    }
  },
  expiresAt: {
    type: DataTypes.DATE,
    allowNull: true,
    field: 'expires_at'
  }
}, {
  tableName: 'carts',
  hooks: {
    beforeSave: (cart) => {
      // Calculate totals
      const items = cart.items || [];
      cart.subtotal = items.reduce((sum, item) => sum + (item.price * item.quantity), 0);

      // Get shipping state (default to Colorado if not set)
      const shippingState = cart.shippingState || 'CO';

      // Calculate tax based on US state rates
      const taxRates = {
        'AL': 9.22, 'AK': 1.76, 'AZ': 8.37, 'AR': 9.43, 'CA': 10.58, 'CO': 7.63,
        'CT': 6.35, 'DE': 0.00, 'FL': 7.05, 'GA': 7.29, 'HI': 4.58, 'ID': 6.03,
        'IL': 8.74, 'IN': 7.00, 'IA': 6.82, 'KS': 8.67, 'KY': 6.00, 'LA': 9.45,
        'ME': 5.50, 'MD': 6.00, 'MA': 6.25, 'MI': 6.00, 'MN': 7.43, 'MS': 7.07,
        'MO': 8.13, 'MT': 0.00, 'NE': 6.85, 'NV': 8.14, 'NH': 0.00, 'NJ': 6.60,
        'NM': 7.82, 'NY': 8.49, 'NC': 6.97, 'ND': 6.85, 'OH': 7.17, 'OK': 8.92,
        'OR': 0.00, 'PA': 6.34, 'RI': 7.00, 'SC': 7.43, 'SD': 6.10, 'TN': 9.47,
        'TX': 8.19, 'UT': 7.09, 'VT': 6.18, 'VA': 5.30, 'WA': 9.60, 'WV': 6.59,
        'WI': 5.44, 'WY': 5.36, 'DC': 6.00
      };

      const taxRate = taxRates[shippingState] || 7.63; // Default to Colorado rate
      cart.tax = cart.subtotal * (taxRate / 100);

      // Calculate USPS shipping (free shipping over $100 for continental US)
      const restrictedStates = ['ID', 'IA', 'SD'];
      const alaskaHawaii = ['AK', 'HI'];
      const territories = ['PR', 'VI', 'GU', 'AS', 'MP'];

      if (restrictedStates.includes(shippingState)) {
        cart.shipping = 0; // No shipping to restricted states
      } else if (cart.subtotal >= 100) {
        cart.shipping = 0; // Free shipping over $100
      } else if (alaskaHawaii.includes(shippingState)) {
        cart.shipping = 12.99; // Higher rate for AK/HI
      } else if (territories.includes(shippingState)) {
        cart.shipping = 19.99; // Highest rate for territories
      } else {
        cart.shipping = 6.99; // Standard USPS Ground Advantage rate
      }

      // Apply discount
      const discountAmount = cart.discount || 0;

      cart.total = cart.subtotal + cart.tax + cart.shipping - discountAmount;

      // Update expiration (30 days from now)
      cart.expiresAt = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000);
    }
  }
});

// Instance methods
Cart.prototype.addItem = async function(productId, variant, quantity, price) {
  const items = this.items || [];
  const existingItemIndex = items.findIndex(item =>
    item.productId === productId &&
    JSON.stringify(item.variant) === JSON.stringify(variant)
  );

  if (existingItemIndex > -1) {
    items[existingItemIndex].quantity += quantity;
  } else {
    items.push({
      productId,
      variant,
      quantity,
      price
    });
  }

  this.items = items;
  return await this.save();
};

Cart.prototype.updateItemQuantity = async function(itemIndex, quantity) {
  const items = this.items || [];
  if (items[itemIndex]) {
    if (quantity <= 0) {
      items.splice(itemIndex, 1);
    } else {
      items[itemIndex].quantity = quantity;
    }
    this.items = items;
  }
  return await this.save();
};

Cart.prototype.removeItem = async function(itemIndex) {
  const items = this.items || [];
  if (items[itemIndex]) {
    items.splice(itemIndex, 1);
    this.items = items;
  }
  return await this.save();
};

Cart.prototype.clearCart = async function() {
  this.items = [];
  this.couponCode = null;
  this.discount = 0;
  return await this.save();
};

Cart.prototype.getItemCount = function() {
  const items = this.items || [];
  return items.reduce((count, item) => count + item.quantity, 0);
};

// Static methods
Cart.findOrCreateForUser = async function(userId) {
  let cart = await Cart.findOne({ where: { userId } });

  if (!cart) {
    cart = await Cart.create({ userId });
  }

  return cart;
};

module.exports = Cart;
