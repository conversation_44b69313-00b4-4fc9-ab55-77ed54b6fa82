const express = require('express');
const router = express.Router();

// Basic health check route that doesn't require external dependencies
router.get('/health', (req, res) => {
  res.json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    message: 'Server is running'
  });
});

// Basic API info route
router.get('/info', (req, res) => {
  res.json({
    name: 'Nirvana Backend API',
    version: '1.0.0',
    status: 'running',
    timestamp: new Date().toISOString()
  });
});

// TODO: Add other routes as dependencies are installed
// Uncomment and add routes as needed:
// const authRoutes = require('./auth');
// router.use('/auth', authRoutes);

module.exports = router;
