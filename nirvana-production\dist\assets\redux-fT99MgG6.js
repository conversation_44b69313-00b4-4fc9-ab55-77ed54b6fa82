var Qe=Object.defineProperty;var Je=(e,t,r)=>t in e?Qe(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r;var Z=(e,t,r)=>Je(e,typeof t!="symbol"?t+"":t,r);import{r as O}from"./router-HnPUWn0z.js";import{r as Ye}from"./vendor-DavUf6mE.js";var ee={exports:{}},te={};/**
 * @license React
 * use-sync-external-store-with-selector.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var _e;function Ze(){if(_e)return te;_e=1;var e=Ye();function t(s,f){return s===f&&(s!==0||1/s===1/f)||s!==s&&f!==f}var r=typeof Object.is=="function"?Object.is:t,n=e.useSyncExternalStore,o=e.useRef,i=e.useEffect,c=e.useMemo,u=e.useDebugValue;return te.useSyncExternalStoreWithSelector=function(s,f,a,l,h){var d=o(null);if(d.current===null){var y={hasValue:!1,value:null};d.current=y}else y=d.current;d=c(function(){function w(m){if(!_){if(_=!0,S=m,m=l(m),h!==void 0&&y.hasValue){var E=y.value;if(h(E,m))return p=E}return p=m}if(E=p,r(S,m))return E;var g=l(m);return h!==void 0&&h(E,g)?(S=m,E):(S=m,p=g)}var _=!1,S,p,b=a===void 0?null:a;return[function(){return w(f())},b===null?void 0:function(){return w(b())}]},[f,a,l,h]);var v=n(s,d[0],d[1]);return i(function(){y.hasValue=!0,y.value=v},[v]),u(v),v},te}var ge;function et(){return ge||(ge=1,ee.exports=Ze()),ee.exports}var tt=et();function rt(e){e()}function nt(){let e=null,t=null;return{clear(){e=null,t=null},notify(){rt(()=>{let r=e;for(;r;)r.callback(),r=r.next})},get(){const r=[];let n=e;for(;n;)r.push(n),n=n.next;return r},subscribe(r){let n=!0;const o=t={callback:r,next:null,prev:t};return o.prev?o.prev.next=o:e=o,function(){!n||e===null||(n=!1,o.next?o.next.prev=o.prev:t=o.prev,o.prev?o.prev.next=o.next:e=o.next)}}}}var ve={notify(){},get:()=>[]};function ot(e,t){let r,n=ve,o=0,i=!1;function c(v){a();const w=n.subscribe(v);let _=!1;return()=>{_||(_=!0,w(),l())}}function u(){n.notify()}function s(){y.onStateChange&&y.onStateChange()}function f(){return i}function a(){o++,r||(r=e.subscribe(s),n=nt())}function l(){o--,r&&o===0&&(r(),r=void 0,n.clear(),n=ve)}function h(){i||(i=!0,a())}function d(){i&&(i=!1,l())}const y={addNestedSub:c,notifyNestedSubs:u,handleChangeWrapper:s,isSubscribed:f,trySubscribe:h,tryUnsubscribe:d,getListeners:()=>n};return y}var it=()=>typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u",st=it(),ct=()=>typeof navigator<"u"&&navigator.product==="ReactNative",ut=ct(),at=()=>st||ut?O.useLayoutEffect:O.useEffect,ft=at(),re=Symbol.for("react-redux-context"),ne=typeof globalThis<"u"?globalThis:{};function lt(){if(!O.createContext)return{};const e=ne[re]??(ne[re]=new Map);let t=e.get(O.createContext);return t||(t=O.createContext(null),e.set(O.createContext,t)),t}var A=lt();function dt(e){const{children:t,context:r,serverState:n,store:o}=e,i=O.useMemo(()=>{const s=ot(o);return{store:o,subscription:s,getServerState:n?()=>n:void 0}},[o,n]),c=O.useMemo(()=>o.getState(),[o]);ft(()=>{const{subscription:s}=i;return s.onStateChange=s.notifyNestedSubs,s.trySubscribe(),c!==o.getState()&&s.notifyNestedSubs(),()=>{s.tryUnsubscribe(),s.onStateChange=void 0}},[i,c]);const u=r||A;return O.createElement(u.Provider,{value:i},t)}var vr=dt;function ye(e=A){return function(){return O.useContext(e)}}var je=ye();function Ne(e=A){const t=e===A?je:ye(e),r=()=>{const{store:n}=t();return n};return Object.assign(r,{withTypes:()=>r}),r}var pt=Ne();function yt(e=A){const t=e===A?pt:Ne(e),r=()=>t().dispatch;return Object.assign(r,{withTypes:()=>r}),r}var Sr=yt(),ht=(e,t)=>e===t;function mt(e=A){const t=e===A?je:ye(e),r=(n,o={})=>{const{equalityFn:i=ht}=typeof o=="function"?{equalityFn:o}:o,c=t(),{store:u,subscription:s,getServerState:f}=c;O.useRef(!0);const a=O.useCallback({[n.name](h){return n(h)}}[n.name],[n]),l=tt.useSyncExternalStoreWithSelector(s.addNestedSub,u.getState,f||u.getState,a,i);return O.useDebugValue(l),l};return Object.assign(r,{withTypes:()=>r}),r}var Cr=mt();function R(e){return`Minified Redux error #${e}; visit https://redux.js.org/Errors?code=${e} for the full message or use the non-minified dev environment for full errors. `}var bt=typeof Symbol=="function"&&Symbol.observable||"@@observable",Se=bt,oe=()=>Math.random().toString(36).substring(7).split("").join("."),wt={INIT:`@@redux/INIT${oe()}`,REPLACE:`@@redux/REPLACE${oe()}`,PROBE_UNKNOWN_ACTION:()=>`@@redux/PROBE_UNKNOWN_ACTION${oe()}`},q=wt;function he(e){if(typeof e!="object"||e===null)return!1;let t=e;for(;Object.getPrototypeOf(t)!==null;)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t||Object.getPrototypeOf(e)===null}function ze(e,t,r){if(typeof e!="function")throw new Error(R(2));if(typeof t=="function"&&typeof r=="function"||typeof r=="function"&&typeof arguments[3]=="function")throw new Error(R(0));if(typeof t=="function"&&typeof r>"u"&&(r=t,t=void 0),typeof r<"u"){if(typeof r!="function")throw new Error(R(1));return r(ze)(e,t)}let n=e,o=t,i=new Map,c=i,u=0,s=!1;function f(){c===i&&(c=new Map,i.forEach((w,_)=>{c.set(_,w)}))}function a(){if(s)throw new Error(R(3));return o}function l(w){if(typeof w!="function")throw new Error(R(4));if(s)throw new Error(R(5));let _=!0;f();const S=u++;return c.set(S,w),function(){if(_){if(s)throw new Error(R(6));_=!1,f(),c.delete(S),i=null}}}function h(w){if(!he(w))throw new Error(R(7));if(typeof w.type>"u")throw new Error(R(8));if(typeof w.type!="string")throw new Error(R(17));if(s)throw new Error(R(9));try{s=!0,o=n(o,w)}finally{s=!1}return(i=c).forEach(S=>{S()}),w}function d(w){if(typeof w!="function")throw new Error(R(10));n=w,h({type:q.REPLACE})}function y(){const w=l;return{subscribe(_){if(typeof _!="object"||_===null)throw new Error(R(11));function S(){const b=_;b.next&&b.next(a())}return S(),{unsubscribe:w(S)}},[Se](){return this}}}return h({type:q.INIT}),{dispatch:h,subscribe:l,getState:a,replaceReducer:d,[Se]:y}}function _t(e){Object.keys(e).forEach(t=>{const r=e[t];if(typeof r(void 0,{type:q.INIT})>"u")throw new Error(R(12));if(typeof r(void 0,{type:q.PROBE_UNKNOWN_ACTION()})>"u")throw new Error(R(13))})}function gt(e){const t=Object.keys(e),r={};for(let i=0;i<t.length;i++){const c=t[i];typeof e[c]=="function"&&(r[c]=e[c])}const n=Object.keys(r);let o;try{_t(r)}catch(i){o=i}return function(c={},u){if(o)throw o;let s=!1;const f={};for(let a=0;a<n.length;a++){const l=n[a],h=r[l],d=c[l],y=h(d,u);if(typeof y>"u")throw u&&u.type,new Error(R(14));f[l]=y,s=s||y!==d}return s=s||n.length!==Object.keys(c).length,s?f:c}}function K(...e){return e.length===0?t=>t:e.length===1?e[0]:e.reduce((t,r)=>(...n)=>t(r(...n)))}function vt(...e){return t=>(r,n)=>{const o=t(r,n);let i=()=>{throw new Error(R(15))};const c={getState:o.getState,dispatch:(s,...f)=>i(s,...f)},u=e.map(s=>s(c));return i=K(...u)(o.dispatch),{...o,dispatch:i}}}function St(e){return he(e)&&"type"in e&&typeof e.type=="string"}var Ie=Symbol.for("immer-nothing"),Ce=Symbol.for("immer-draftable"),P=Symbol.for("immer-state");function M(e,...t){throw new Error(`[Immer] minified error nr: ${e}. Full error at: https://bit.ly/3cXEKWf`)}var z=Object.getPrototypeOf;function j(e){return!!e&&!!e[P]}function k(e){return e?Fe(e)||Array.isArray(e)||!!e[Ce]||!!e.constructor?.[Ce]||Q(e)||J(e):!1}var Ct=Object.prototype.constructor.toString();function Fe(e){if(!e||typeof e!="object")return!1;const t=z(e);if(t===null)return!0;const r=Object.hasOwnProperty.call(t,"constructor")&&t.constructor;return r===Object?!0:typeof r=="function"&&Function.toString.call(r)===Ct}function V(e,t){X(e)===0?Reflect.ownKeys(e).forEach(r=>{t(r,e[r],e)}):e.forEach((r,n)=>t(n,r,e))}function X(e){const t=e[P];return t?t.type_:Array.isArray(e)?1:Q(e)?2:J(e)?3:0}function ue(e,t){return X(e)===2?e.has(t):Object.prototype.hasOwnProperty.call(e,t)}function We(e,t,r){const n=X(e);n===2?e.set(t,r):n===3?e.add(r):e[t]=r}function Et(e,t){return e===t?e!==0||1/e===1/t:e!==e&&t!==t}function Q(e){return e instanceof Map}function J(e){return e instanceof Set}function D(e){return e.copy_||e.base_}function ae(e,t){if(Q(e))return new Map(e);if(J(e))return new Set(e);if(Array.isArray(e))return Array.prototype.slice.call(e);const r=Fe(e);if(t===!0||t==="class_only"&&!r){const n=Object.getOwnPropertyDescriptors(e);delete n[P];let o=Reflect.ownKeys(n);for(let i=0;i<o.length;i++){const c=o[i],u=n[c];u.writable===!1&&(u.writable=!0,u.configurable=!0),(u.get||u.set)&&(n[c]={configurable:!0,writable:!0,enumerable:u.enumerable,value:e[c]})}return Object.create(z(e),n)}else{const n=z(e);if(n!==null&&r)return{...e};const o=Object.create(n);return Object.assign(o,e)}}function me(e,t=!1){return Y(e)||j(e)||!k(e)||(X(e)>1&&(e.set=e.add=e.clear=e.delete=Rt),Object.freeze(e),t&&Object.entries(e).forEach(([r,n])=>me(n,!0))),e}function Rt(){M(2)}function Y(e){return Object.isFrozen(e)}var Ot={};function N(e){const t=Ot[e];return t||M(0,e),t}var W;function $e(){return W}function Pt(e,t){return{drafts_:[],parent_:e,immer_:t,canAutoFreeze_:!0,unfinalizedDrafts_:0}}function Ee(e,t){t&&(N("Patches"),e.patches_=[],e.inversePatches_=[],e.patchListener_=t)}function fe(e){le(e),e.drafts_.forEach(xt),e.drafts_=null}function le(e){e===W&&(W=e.parent_)}function Re(e){return W=Pt(W,e)}function xt(e){const t=e[P];t.type_===0||t.type_===1?t.revoke_():t.revoked_=!0}function Oe(e,t){t.unfinalizedDrafts_=t.drafts_.length;const r=t.drafts_[0];return e!==void 0&&e!==r?(r[P].modified_&&(fe(t),M(4)),k(e)&&(e=H(t,e),t.parent_||G(t,e)),t.patches_&&N("Patches").generateReplacementPatches_(r[P].base_,e,t.patches_,t.inversePatches_)):e=H(t,r,[]),fe(t),t.patches_&&t.patchListener_(t.patches_,t.inversePatches_),e!==Ie?e:void 0}function H(e,t,r){if(Y(t))return t;const n=t[P];if(!n)return V(t,(o,i)=>Pe(e,n,t,o,i,r)),t;if(n.scope_!==e)return t;if(!n.modified_)return G(e,n.base_,!0),n.base_;if(!n.finalized_){n.finalized_=!0,n.scope_.unfinalizedDrafts_--;const o=n.copy_;let i=o,c=!1;n.type_===3&&(i=new Set(o),o.clear(),c=!0),V(i,(u,s)=>Pe(e,n,o,u,s,r,c)),G(e,o,!1),r&&e.patches_&&N("Patches").generatePatches_(n,r,e.patches_,e.inversePatches_)}return n.copy_}function Pe(e,t,r,n,o,i,c){if(j(o)){const u=i&&t&&t.type_!==3&&!ue(t.assigned_,n)?i.concat(n):void 0,s=H(e,o,u);if(We(r,n,s),j(s))e.canAutoFreeze_=!1;else return}else c&&r.add(o);if(k(o)&&!Y(o)){if(!e.immer_.autoFreeze_&&e.unfinalizedDrafts_<1)return;H(e,o),(!t||!t.scope_.parent_)&&typeof n!="symbol"&&Object.prototype.propertyIsEnumerable.call(r,n)&&G(e,o)}}function G(e,t,r=!1){!e.parent_&&e.immer_.autoFreeze_&&e.canAutoFreeze_&&me(t,r)}function Mt(e,t){const r=Array.isArray(e),n={type_:r?1:0,scope_:t?t.scope_:$e(),modified_:!1,finalized_:!1,assigned_:{},parent_:t,base_:e,draft_:null,copy_:null,revoke_:null,isManual_:!1};let o=n,i=be;r&&(o=[n],i=$);const{revoke:c,proxy:u}=Proxy.revocable(o,i);return n.draft_=u,n.revoke_=c,u}var be={get(e,t){if(t===P)return e;const r=D(e);if(!ue(r,t))return Tt(e,r,t);const n=r[t];return e.finalized_||!k(n)?n:n===ie(e.base_,t)?(se(e),e.copy_[t]=pe(n,e)):n},has(e,t){return t in D(e)},ownKeys(e){return Reflect.ownKeys(D(e))},set(e,t,r){const n=Be(D(e),t);if(n?.set)return n.set.call(e.draft_,r),!0;if(!e.modified_){const o=ie(D(e),t),i=o?.[P];if(i&&i.base_===r)return e.copy_[t]=r,e.assigned_[t]=!1,!0;if(Et(r,o)&&(r!==void 0||ue(e.base_,t)))return!0;se(e),de(e)}return e.copy_[t]===r&&(r!==void 0||t in e.copy_)||Number.isNaN(r)&&Number.isNaN(e.copy_[t])||(e.copy_[t]=r,e.assigned_[t]=!0),!0},deleteProperty(e,t){return ie(e.base_,t)!==void 0||t in e.base_?(e.assigned_[t]=!1,se(e),de(e)):delete e.assigned_[t],e.copy_&&delete e.copy_[t],!0},getOwnPropertyDescriptor(e,t){const r=D(e),n=Reflect.getOwnPropertyDescriptor(r,t);return n&&{writable:!0,configurable:e.type_!==1||t!=="length",enumerable:n.enumerable,value:r[t]}},defineProperty(){M(11)},getPrototypeOf(e){return z(e.base_)},setPrototypeOf(){M(12)}},$={};V(be,(e,t)=>{$[e]=function(){return arguments[0]=arguments[0][0],t.apply(this,arguments)}});$.deleteProperty=function(e,t){return $.set.call(this,e,t,void 0)};$.set=function(e,t,r){return be.set.call(this,e[0],t,r,e[0])};function ie(e,t){const r=e[P];return(r?D(r):e)[t]}function Tt(e,t,r){const n=Be(t,r);return n?"value"in n?n.value:n.get?.call(e.draft_):void 0}function Be(e,t){if(!(t in e))return;let r=z(e);for(;r;){const n=Object.getOwnPropertyDescriptor(r,t);if(n)return n;r=z(r)}}function de(e){e.modified_||(e.modified_=!0,e.parent_&&de(e.parent_))}function se(e){e.copy_||(e.copy_=ae(e.base_,e.scope_.immer_.useStrictShallowCopy_))}var kt=class{constructor(e){this.autoFreeze_=!0,this.useStrictShallowCopy_=!1,this.produce=(t,r,n)=>{if(typeof t=="function"&&typeof r!="function"){const i=r;r=t;const c=this;return function(s=i,...f){return c.produce(s,a=>r.call(this,a,...f))}}typeof r!="function"&&M(6),n!==void 0&&typeof n!="function"&&M(7);let o;if(k(t)){const i=Re(this),c=pe(t,void 0);let u=!0;try{o=r(c),u=!1}finally{u?fe(i):le(i)}return Ee(i,n),Oe(o,i)}else if(!t||typeof t!="object"){if(o=r(t),o===void 0&&(o=t),o===Ie&&(o=void 0),this.autoFreeze_&&me(o,!0),n){const i=[],c=[];N("Patches").generateReplacementPatches_(t,o,i,c),n(i,c)}return o}else M(1,t)},this.produceWithPatches=(t,r)=>{if(typeof t=="function")return(c,...u)=>this.produceWithPatches(c,s=>t(s,...u));let n,o;return[this.produce(t,r,(c,u)=>{n=c,o=u}),n,o]},typeof e?.autoFreeze=="boolean"&&this.setAutoFreeze(e.autoFreeze),typeof e?.useStrictShallowCopy=="boolean"&&this.setUseStrictShallowCopy(e.useStrictShallowCopy)}createDraft(e){k(e)||M(8),j(e)&&(e=At(e));const t=Re(this),r=pe(e,void 0);return r[P].isManual_=!0,le(t),r}finishDraft(e,t){const r=e&&e[P];(!r||!r.isManual_)&&M(9);const{scope_:n}=r;return Ee(n,t),Oe(void 0,n)}setAutoFreeze(e){this.autoFreeze_=e}setUseStrictShallowCopy(e){this.useStrictShallowCopy_=e}applyPatches(e,t){let r;for(r=t.length-1;r>=0;r--){const o=t[r];if(o.path.length===0&&o.op==="replace"){e=o.value;break}}r>-1&&(t=t.slice(r+1));const n=N("Patches").applyPatches_;return j(e)?n(e,t):this.produce(e,o=>n(o,t))}};function pe(e,t){const r=Q(e)?N("MapSet").proxyMap_(e,t):J(e)?N("MapSet").proxySet_(e,t):Mt(e,t);return(t?t.scope_:$e()).drafts_.push(r),r}function At(e){return j(e)||M(10,e),Le(e)}function Le(e){if(!k(e)||Y(e))return e;const t=e[P];let r;if(t){if(!t.modified_)return t.base_;t.finalized_=!0,r=ae(e,t.scope_.immer_.useStrictShallowCopy_)}else r=ae(e,!0);return V(r,(n,o)=>{We(r,n,Le(o))}),t&&(t.finalized_=!1),r}var x=new kt,Ue=x.produce;x.produceWithPatches.bind(x);x.setAutoFreeze.bind(x);x.setUseStrictShallowCopy.bind(x);x.applyPatches.bind(x);x.createDraft.bind(x);x.finishDraft.bind(x);function Dt(e,t=`expected a function, instead received ${typeof e}`){if(typeof e!="function")throw new TypeError(t)}function jt(e,t=`expected an object, instead received ${typeof e}`){if(typeof e!="object")throw new TypeError(t)}function Nt(e,t="expected all items to be functions, instead received the following types: "){if(!e.every(r=>typeof r=="function")){const r=e.map(n=>typeof n=="function"?`function ${n.name||"unnamed"}()`:typeof n).join(", ");throw new TypeError(`${t}[${r}]`)}}var xe=e=>Array.isArray(e)?e:[e];function zt(e){const t=Array.isArray(e[0])?e[0]:e;return Nt(t,"createSelector expects all input-selectors to be functions, but received the following types: "),t}function It(e,t){const r=[],{length:n}=e;for(let o=0;o<n;o++)r.push(e[o].apply(null,t));return r}var Ft=class{constructor(e){this.value=e}deref(){return this.value}},Wt=typeof WeakRef<"u"?WeakRef:Ft,$t=0,Me=1;function B(){return{s:$t,v:void 0,o:null,p:null}}function qe(e,t={}){let r=B();const{resultEqualityCheck:n}=t;let o,i=0;function c(){let u=r;const{length:s}=arguments;for(let l=0,h=s;l<h;l++){const d=arguments[l];if(typeof d=="function"||typeof d=="object"&&d!==null){let y=u.o;y===null&&(u.o=y=new WeakMap);const v=y.get(d);v===void 0?(u=B(),y.set(d,u)):u=v}else{let y=u.p;y===null&&(u.p=y=new Map);const v=y.get(d);v===void 0?(u=B(),y.set(d,u)):u=v}}const f=u;let a;if(u.s===Me)a=u.v;else if(a=e.apply(null,arguments),i++,n){const l=o?.deref?.()??o;l!=null&&n(l,a)&&(a=l,i!==0&&i--),o=typeof a=="object"&&a!==null||typeof a=="function"?new Wt(a):a}return f.s=Me,f.v=a,a}return c.clearCache=()=>{r=B(),c.resetResultsCount()},c.resultsCount=()=>i,c.resetResultsCount=()=>{i=0},c}function Bt(e,...t){const r=typeof e=="function"?{memoize:e,memoizeOptions:t}:e,n=(...o)=>{let i=0,c=0,u,s={},f=o.pop();typeof f=="object"&&(s=f,f=o.pop()),Dt(f,`createSelector expects an output function after the inputs, but received: [${typeof f}]`);const a={...r,...s},{memoize:l,memoizeOptions:h=[],argsMemoize:d=qe,argsMemoizeOptions:y=[]}=a,v=xe(h),w=xe(y),_=zt(o),S=l(function(){return i++,f.apply(null,arguments)},...v),p=d(function(){c++;const m=It(_,arguments);return u=S.apply(null,m),u},...w);return Object.assign(p,{resultFunc:f,memoizedResultFunc:S,dependencies:_,dependencyRecomputations:()=>c,resetDependencyRecomputations:()=>{c=0},lastResult:()=>u,recomputations:()=>i,resetRecomputations:()=>{i=0},memoize:l,argsMemoize:d})};return Object.assign(n,{withTypes:()=>n}),n}var Lt=Bt(qe),Ut=Object.assign((e,t=Lt)=>{jt(e,`createStructuredSelector expects first argument to be an object where each property is a selector, instead received a ${typeof e}`);const r=Object.keys(e),n=r.map(i=>e[i]);return t(n,(...i)=>i.reduce((c,u,s)=>(c[r[s]]=u,c),{}))},{withTypes:()=>Ut});function Ke(e){return({dispatch:r,getState:n})=>o=>i=>typeof i=="function"?i(r,n,e):o(i)}var qt=Ke(),Kt=Ke,Vt=typeof window<"u"&&window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__?window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__:function(){if(arguments.length!==0)return typeof arguments[0]=="object"?K:K.apply(null,arguments)},Ht=e=>e&&typeof e.match=="function";function F(e,t){function r(...n){if(t){let o=t(...n);if(!o)throw new Error(T(0));return{type:e,payload:o.payload,..."meta"in o&&{meta:o.meta},..."error"in o&&{error:o.error}}}return{type:e,payload:n[0]}}return r.toString=()=>`${e}`,r.type=e,r.match=n=>St(n)&&n.type===e,r}var Ve=class I extends Array{constructor(...t){super(...t),Object.setPrototypeOf(this,I.prototype)}static get[Symbol.species](){return I}concat(...t){return super.concat.apply(this,t)}prepend(...t){return t.length===1&&Array.isArray(t[0])?new I(...t[0].concat(this)):new I(...t.concat(this))}};function Te(e){return k(e)?Ue(e,()=>{}):e}function L(e,t,r){return e.has(t)?e.get(t):e.set(t,r(t)).get(t)}function Gt(e){return typeof e=="boolean"}var Xt=()=>function(t){const{thunk:r=!0,immutableCheck:n=!0,serializableCheck:o=!0,actionCreatorCheck:i=!0}=t??{};let c=new Ve;return r&&(Gt(r)?c.push(qt):c.push(Kt(r.extraArgument))),c},Qt="RTK_autoBatch",ke=e=>t=>{setTimeout(t,e)},Jt=(e={type:"raf"})=>t=>(...r)=>{const n=t(...r);let o=!0,i=!1,c=!1;const u=new Set,s=e.type==="tick"?queueMicrotask:e.type==="raf"?typeof window<"u"&&window.requestAnimationFrame?window.requestAnimationFrame:ke(10):e.type==="callback"?e.queueNotification:ke(e.timeout),f=()=>{c=!1,i&&(i=!1,u.forEach(a=>a()))};return Object.assign({},n,{subscribe(a){const l=()=>o&&a(),h=n.subscribe(l);return u.add(a),()=>{h(),u.delete(a)}},dispatch(a){try{return o=!a?.meta?.[Qt],i=!o,i&&(c||(c=!0,s(f))),n.dispatch(a)}finally{o=!0}}})},Yt=e=>function(r){const{autoBatch:n=!0}=r??{};let o=new Ve(e);return n&&o.push(Jt(typeof n=="object"?n:void 0)),o};function Er(e){const t=Xt(),{reducer:r=void 0,middleware:n,devTools:o=!0,preloadedState:i=void 0,enhancers:c=void 0}=e||{};let u;if(typeof r=="function")u=r;else if(he(r))u=gt(r);else throw new Error(T(1));let s;typeof n=="function"?s=n(t):s=t();let f=K;o&&(f=Vt({trace:!1,...typeof o=="object"&&o}));const a=vt(...s),l=Yt(a);let h=typeof c=="function"?c(l):l();const d=f(...h);return ze(u,i,d)}function He(e){const t={},r=[];let n;const o={addCase(i,c){const u=typeof i=="string"?i:i.type;if(!u)throw new Error(T(28));if(u in t)throw new Error(T(29));return t[u]=c,o},addMatcher(i,c){return r.push({matcher:i,reducer:c}),o},addDefaultCase(i){return n=i,o}};return e(o),[t,r,n]}function Zt(e){return typeof e=="function"}function er(e,t){let[r,n,o]=He(t),i;if(Zt(e))i=()=>Te(e());else{const u=Te(e);i=()=>u}function c(u=i(),s){let f=[r[s.type],...n.filter(({matcher:a})=>a(s)).map(({reducer:a})=>a)];return f.filter(a=>!!a).length===0&&(f=[o]),f.reduce((a,l)=>{if(l)if(j(a)){const d=l(a,s);return d===void 0?a:d}else{if(k(a))return Ue(a,h=>l(h,s));{const h=l(a,s);if(h===void 0){if(a===null)return a;throw Error("A case reducer on a non-draftable value must not return undefined")}return h}}return a},u)}return c.getInitialState=i,c}var tr=(e,t)=>Ht(e)?e.match(t):e(t);function rr(...e){return t=>e.some(r=>tr(r,t))}var nr="ModuleSymbhasOwnPr-0123456789ABCDEFGHNRVfgctiUvz_KqYTJkLxpZXIjQW",or=(e=21)=>{let t="",r=e;for(;r--;)t+=nr[Math.random()*64|0];return t},ir=["name","message","stack","code"],ce=class{constructor(e,t){Z(this,"_type");this.payload=e,this.meta=t}},Ae=class{constructor(e,t){Z(this,"_type");this.payload=e,this.meta=t}},sr=e=>{if(typeof e=="object"&&e!==null){const t={};for(const r of ir)typeof e[r]=="string"&&(t[r]=e[r]);return t}return{message:String(e)}},De="External signal was aborted",Rr=(()=>{function e(t,r,n){const o=F(t+"/fulfilled",(s,f,a,l)=>({payload:s,meta:{...l||{},arg:a,requestId:f,requestStatus:"fulfilled"}})),i=F(t+"/pending",(s,f,a)=>({payload:void 0,meta:{...a||{},arg:f,requestId:s,requestStatus:"pending"}})),c=F(t+"/rejected",(s,f,a,l,h)=>({payload:l,error:(n&&n.serializeError||sr)(s||"Rejected"),meta:{...h||{},arg:a,requestId:f,rejectedWithValue:!!l,requestStatus:"rejected",aborted:s?.name==="AbortError",condition:s?.name==="ConditionError"}}));function u(s,{signal:f}={}){return(a,l,h)=>{const d=n?.idGenerator?n.idGenerator(s):or(),y=new AbortController;let v,w;function _(p){w=p,y.abort()}f&&(f.aborted?_(De):f.addEventListener("abort",()=>_(De),{once:!0}));const S=async function(){let p;try{let m=n?.condition?.(s,{getState:l,extra:h});if(ur(m)&&(m=await m),m===!1||y.signal.aborted)throw{name:"ConditionError",message:"Aborted due to condition callback returning false."};const E=new Promise((g,C)=>{v=()=>{C({name:"AbortError",message:w||"Aborted"})},y.signal.addEventListener("abort",v)});a(i(d,s,n?.getPendingMeta?.({requestId:d,arg:s},{getState:l,extra:h}))),p=await Promise.race([E,Promise.resolve(r(s,{dispatch:a,getState:l,extra:h,requestId:d,signal:y.signal,abort:_,rejectWithValue:(g,C)=>new ce(g,C),fulfillWithValue:(g,C)=>new Ae(g,C)})).then(g=>{if(g instanceof ce)throw g;return g instanceof Ae?o(g.payload,d,s,g.meta):o(g,d,s)})])}catch(m){p=m instanceof ce?c(null,d,s,m.payload,m.meta):c(m,d,s)}finally{v&&y.signal.removeEventListener("abort",v)}return n&&!n.dispatchConditionRejection&&c.match(p)&&p.meta.condition||a(p),p}();return Object.assign(S,{abort:_,requestId:d,arg:s,unwrap(){return S.then(cr)}})}}return Object.assign(u,{pending:i,rejected:c,fulfilled:o,settled:rr(c,o),typePrefix:t})}return e.withTypes=()=>e,e})();function cr(e){if(e.meta&&e.meta.rejectedWithValue)throw e.payload;if(e.error)throw e.error;return e.payload}function ur(e){return e!==null&&typeof e=="object"&&typeof e.then=="function"}var ar=Symbol.for("rtk-slice-createasyncthunk");function fr(e,t){return`${e}/${t}`}function lr({creators:e}={}){const t=e?.asyncThunk?.[ar];return function(n){const{name:o,reducerPath:i=o}=n;if(!o)throw new Error(T(11));const c=(typeof n.reducers=="function"?n.reducers(pr()):n.reducers)||{},u=Object.keys(c),s={sliceCaseReducersByName:{},sliceCaseReducersByType:{},actionCreators:{},sliceMatchers:[]},f={addCase(p,b){const m=typeof p=="string"?p:p.type;if(!m)throw new Error(T(12));if(m in s.sliceCaseReducersByType)throw new Error(T(13));return s.sliceCaseReducersByType[m]=b,f},addMatcher(p,b){return s.sliceMatchers.push({matcher:p,reducer:b}),f},exposeAction(p,b){return s.actionCreators[p]=b,f},exposeCaseReducer(p,b){return s.sliceCaseReducersByName[p]=b,f}};u.forEach(p=>{const b=c[p],m={reducerName:p,type:fr(o,p),createNotation:typeof n.reducers=="function"};hr(b)?br(m,b,f,t):yr(m,b,f)});function a(){const[p={},b=[],m=void 0]=typeof n.extraReducers=="function"?He(n.extraReducers):[n.extraReducers],E={...p,...s.sliceCaseReducersByType};return er(n.initialState,g=>{for(let C in E)g.addCase(C,E[C]);for(let C of s.sliceMatchers)g.addMatcher(C.matcher,C.reducer);for(let C of b)g.addMatcher(C.matcher,C.reducer);m&&g.addDefaultCase(m)})}const l=p=>p,h=new Map,d=new WeakMap;let y;function v(p,b){return y||(y=a()),y(p,b)}function w(){return y||(y=a()),y.getInitialState()}function _(p,b=!1){function m(g){let C=g[p];return typeof C>"u"&&b&&(C=L(d,m,w)),C}function E(g=l){const C=L(h,b,()=>new WeakMap);return L(C,g,()=>{const we={};for(const[Ge,Xe]of Object.entries(n.selectors??{}))we[Ge]=dr(Xe,g,()=>L(d,g,w),b);return we})}return{reducerPath:p,getSelectors:E,get selectors(){return E(m)},selectSlice:m}}const S={name:o,reducer:v,actions:s.actionCreators,caseReducers:s.sliceCaseReducersByName,getInitialState:w,..._(i),injectInto(p,{reducerPath:b,...m}={}){const E=b??i;return p.inject({reducerPath:E,reducer:v},m),{...S,..._(E,!0)}}};return S}}function dr(e,t,r,n){function o(i,...c){let u=t(i);return typeof u>"u"&&n&&(u=r()),e(u,...c)}return o.unwrapped=e,o}var Or=lr();function pr(){function e(t,r){return{_reducerDefinitionType:"asyncThunk",payloadCreator:t,...r}}return e.withTypes=()=>e,{reducer(t){return Object.assign({[t.name](...r){return t(...r)}}[t.name],{_reducerDefinitionType:"reducer"})},preparedReducer(t,r){return{_reducerDefinitionType:"reducerWithPrepare",prepare:t,reducer:r}},asyncThunk:e}}function yr({type:e,reducerName:t,createNotation:r},n,o){let i,c;if("reducer"in n){if(r&&!mr(n))throw new Error(T(17));i=n.reducer,c=n.prepare}else i=n;o.addCase(e,i).exposeCaseReducer(t,i).exposeAction(t,c?F(e,c):F(e))}function hr(e){return e._reducerDefinitionType==="asyncThunk"}function mr(e){return e._reducerDefinitionType==="reducerWithPrepare"}function br({type:e,reducerName:t},r,n,o){if(!o)throw new Error(T(18));const{payloadCreator:i,fulfilled:c,pending:u,rejected:s,settled:f,options:a}=r,l=o(e,i,a);n.exposeAction(t,l),c&&n.addCase(l.fulfilled,c),u&&n.addCase(l.pending,u),s&&n.addCase(l.rejected,s),f&&n.addMatcher(l.settled,f),n.exposeCaseReducer(t,{fulfilled:c||U,pending:u||U,rejected:s||U,settled:f||U})}function U(){}function T(e){return`Minified Redux Toolkit error #${e}; visit https://redux-toolkit.js.org/Errors?code=${e} for the full message or use the non-minified dev environment for full errors. `}export{vr as P,Or as a,Er as b,Rr as c,Cr as d,Sr as u};
