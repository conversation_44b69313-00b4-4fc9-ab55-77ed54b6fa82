#!/usr/bin/env node

/**
 * Fix Products Table is_active Column
 * 
 * This script ensures the products table has the is_active column
 * that is referenced in queries but may be missing from the actual database.
 */

const { Sequelize, DataTypes } = require('sequelize');
const path = require('path');

// Color codes for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[0;31m',
  green: '\x1b[0;32m',
  yellow: '\x1b[1;33m',
  blue: '\x1b[0;34m',
  cyan: '\x1b[0;36m',
  white: '\x1b[1;37m'
};

function log(message, color = colors.blue) {
  console.log(`${color}[${new Date().toISOString()}]${colors.reset} ${message}`);
}

function success(message) {
  console.log(`${colors.green}✅ ${message}${colors.reset}`);
}

function error(message) {
  console.log(`${colors.red}❌ ${message}${colors.reset}`);
}

function warning(message) {
  console.log(`${colors.yellow}⚠️  ${message}${colors.reset}`);
}

async function fixProductsIsActiveColumn() {
  console.log(`${colors.cyan}🔧 Fix Products Table is_active Column${colors.reset}`);
  console.log(`${colors.cyan}=====================================\n${colors.reset}`);

  let sequelize;

  try {
    // Initialize Sequelize connection
    log('Initializing database connection...');
    
    // Try to load environment configuration
    const envPath = path.join(__dirname, '.env.test');
    require('dotenv').config({ path: envPath });

    const dbConfig = {
      host: process.env.DB_HOST || 'localhost',
      port: process.env.DB_PORT || 3306,
      database: process.env.DB_NAME || 'nirvana_organics_test',
      username: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      dialect: 'mysql',
      logging: false
    };

    sequelize = new Sequelize(dbConfig.database, dbConfig.username, dbConfig.password, {
      host: dbConfig.host,
      port: dbConfig.port,
      dialect: dbConfig.dialect,
      logging: false
    });

    // Test connection
    await sequelize.authenticate();
    success('Database connection established');

    // Check if products table exists
    log('Checking products table...');
    const [tables] = await sequelize.query("SHOW TABLES LIKE 'products'");
    
    if (tables.length === 0) {
      error('Products table does not exist');
      return;
    }

    success('Products table found');

    // Check current table structure
    log('Checking current table structure...');
    const [columns] = await sequelize.query("DESCRIBE products");
    
    console.log('\n📋 Current products table columns:');
    columns.forEach(col => {
      console.log(`  - ${col.Field} (${col.Type}) ${col.Null === 'NO' ? 'NOT NULL' : 'NULL'} ${col.Default ? `DEFAULT ${col.Default}` : ''}`);
    });

    // Check if is_active column exists
    const hasIsActiveColumn = columns.some(col => col.Field === 'is_active');

    if (hasIsActiveColumn) {
      success('is_active column already exists');
      
      // Verify the column definition
      const isActiveCol = columns.find(col => col.Field === 'is_active');
      console.log(`\n📊 is_active column details:`);
      console.log(`  Type: ${isActiveCol.Type}`);
      console.log(`  Null: ${isActiveCol.Null}`);
      console.log(`  Default: ${isActiveCol.Default}`);
      
      if (isActiveCol.Type.toLowerCase().includes('tinyint') && isActiveCol.Default === '1') {
        success('is_active column has correct definition');
      } else {
        warning('is_active column exists but may need adjustment');
      }
    } else {
      warning('is_active column is missing - adding it now...');
      
      // Add the is_active column
      await sequelize.query(`
        ALTER TABLE products 
        ADD COLUMN is_active TINYINT(1) NOT NULL DEFAULT 1
        AFTER status
      `);
      
      success('is_active column added successfully');
      
      // Update existing records to be active by default
      const [updateResult] = await sequelize.query(`
        UPDATE products 
        SET is_active = 1 
        WHERE is_active IS NULL
      `);
      
      log(`Updated ${updateResult.affectedRows || 0} existing records to be active`);
    }

    // Test the column with a sample query
    log('Testing is_active column with sample query...');
    
    try {
      const [testResults] = await sequelize.query(`
        SELECT p.id, p.name, p.is_active 
        FROM products p 
        WHERE p.is_active = 1 
        LIMIT 5
      `);
      
      success(`Sample query successful - found ${testResults.length} active products`);
      
      if (testResults.length > 0) {
        console.log('\n📊 Sample active products:');
        testResults.forEach(product => {
          console.log(`  - ID: ${product.id}, Name: ${product.name}, Active: ${product.is_active}`);
        });
      }
    } catch (queryError) {
      error(`Sample query failed: ${queryError.message}`);
      throw queryError;
    }

    // Add index for performance if it doesn't exist
    log('Checking for is_active index...');
    
    const [indexes] = await sequelize.query("SHOW INDEX FROM products WHERE Key_name = 'idx_products_is_active'");
    
    if (indexes.length === 0) {
      log('Adding index for is_active column...');
      await sequelize.query(`
        CREATE INDEX idx_products_is_active ON products (is_active)
      `);
      success('Index added for is_active column');
    } else {
      success('Index already exists for is_active column');
    }

    console.log('\n📋 FIX SUMMARY');
    console.log('==============');
    success('✅ Products table is_active column is properly configured');
    success('✅ Database queries using p.is_active should now work');
    success('✅ Performance index is in place');
    
    console.log('\n🔍 VERIFICATION');
    console.log('===============');
    console.log('The following query should now work without errors:');
    console.log('SELECT p.id, p.name FROM products p WHERE p.is_active = 1;');

  } catch (err) {
    error(`Database operation failed: ${err.message}`);
    console.error(err);
    process.exit(1);
  } finally {
    if (sequelize) {
      await sequelize.close();
      log('Database connection closed');
    }
  }

  console.log('\n🎉 Products is_active column fix completed successfully!');
}

// Run the fix if this script is executed directly
if (require.main === module) {
  fixProductsIsActiveColumn().catch(console.error);
}

module.exports = { fixProductsIsActiveColumn };
