const crypto = require('crypto');
const axios = require('axios');
require('dotenv').config();

/**
 * Square Webhook Testing Utility
 * This utility helps test Square webhook integration during development and deployment
 */
class SquareWebhookTester {
  constructor() {
    this.webhookUrl = process.env.WEBHOOK_URL || 'http://localhost:5000/api/webhooks/square';
    this.webhookSignatureKey = process.env.SQUARE_WEBHOOK_SIGNATURE_KEY;
  }

  /**
   * Generate Square webhook signature
   */
  generateSignature(body, signatureKey) {
    const hmac = crypto.createHmac('sha256', signatureKey);
    hmac.update(body);
    return hmac.digest('base64');
  }

  /**
   * Create test payment event
   */
  createTestPaymentEvent(paymentId = 'test_payment_123', status = 'COMPLETED') {
    return {
      merchant_id: 'test_merchant',
      type: 'payment.updated',
      event_id: `test_event_${Date.now()}`,
      created_at: new Date().toISOString(),
      data: {
        type: 'payment',
        id: paymentId,
        object: {
          payment: {
            id: paymentId,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
            amount_money: {
              amount: 2500, // $25.00 in cents
              currency: 'USD'
            },
            status: status,
            delay_duration: 'PT0S',
            source_type: 'CARD',
            card_details: {
              status: status,
              card: {
                card_brand: 'VISA',
                last_4: '1111',
                exp_month: 12,
                exp_year: 2025,
                fingerprint: 'sq-1-test',
                card_type: 'CREDIT',
                prepaid_type: 'NOT_PREPAID'
              },
              entry_method: 'KEYED',
              cvv_status: 'CVV_ACCEPTED',
              avs_status: 'AVS_ACCEPTED',
              statement_description: 'SQ *NIRVANA ORGANICS',
              card_payment_timeline: {
                authorized_at: new Date().toISOString(),
                captured_at: status === 'COMPLETED' ? new Date().toISOString() : null
              }
            },
            location_id: process.env.SQUARE_LOCATION_ID || 'test_location',
            order_id: 'test_order_123',
            reference_id: 'NO-12345',
            note: 'Test payment for order NO-12345',
            customer_id: 'test_customer_123',
            total_money: {
              amount: 2500,
              currency: 'USD'
            },
            approved_money: {
              amount: 2500,
              currency: 'USD'
            },
            receipt_number: 'test_receipt_123',
            receipt_url: 'https://squareup.com/receipt/test'
          }
        }
      }
    };
  }

  /**
   * Create test refund event
   */
  createTestRefundEvent(refundId = 'test_refund_123', paymentId = 'test_payment_123') {
    return {
      merchant_id: 'test_merchant',
      type: 'refund.updated',
      event_id: `test_refund_event_${Date.now()}`,
      created_at: new Date().toISOString(),
      data: {
        type: 'refund',
        id: refundId,
        object: {
          refund: {
            id: refundId,
            location_id: process.env.SQUARE_LOCATION_ID || 'test_location',
            transaction_id: 'test_transaction_123',
            tender_id: 'test_tender_123',
            created_at: new Date().toISOString(),
            reason: 'Customer requested refund',
            amount_money: {
              amount: 2500,
              currency: 'USD'
            },
            status: 'COMPLETED',
            payment_id: paymentId
          }
        }
      }
    };
  }

  /**
   * Send test webhook to your endpoint
   */
  async sendTestWebhook(event, customUrl = null) {
    const url = customUrl || this.webhookUrl;
    const body = JSON.stringify(event);
    
    if (!this.webhookSignatureKey) {
      console.warn('Warning: SQUARE_WEBHOOK_SIGNATURE_KEY not set. Webhook signature verification will fail.');
    }

    const signature = this.webhookSignatureKey ? this.generateSignature(body, this.webhookSignatureKey) : 'test_signature';

    try {
      const response = await axios.post(url, body, {
        headers: {
          'Content-Type': 'application/json',
          'X-Square-Signature': signature,
          'User-Agent': 'Square-Webhooks/1.0'
        },
        timeout: 10000
      });

      console.log('✅ Webhook sent successfully');
      console.log('Response status:', response.status);
      console.log('Response data:', response.data);
      return response;

    } catch (error) {
      console.error('❌ Webhook failed');
      if (error.response) {
        console.error('Status:', error.response.status);
        console.error('Data:', error.response.data);
      } else {
        console.error('Error:', error.message);
      }
      throw error;
    }
  }

  /**
   * Test payment completion flow
   */
  async testPaymentCompletion(paymentId = 'test_payment_123') {
    console.log('🧪 Testing payment completion webhook...');
    const event = this.createTestPaymentEvent(paymentId, 'COMPLETED');
    return this.sendTestWebhook(event);
  }

  /**
   * Test payment failure flow
   */
  async testPaymentFailure(paymentId = 'test_payment_failed') {
    console.log('🧪 Testing payment failure webhook...');
    const event = this.createTestPaymentEvent(paymentId, 'FAILED');
    return this.sendTestWebhook(event);
  }

  /**
   * Test refund flow
   */
  async testRefund(refundId = 'test_refund_123', paymentId = 'test_payment_123') {
    console.log('🧪 Testing refund webhook...');
    const event = this.createTestRefundEvent(refundId, paymentId);
    return this.sendTestWebhook(event);
  }

  /**
   * Run comprehensive webhook tests
   */
  async runAllTests() {
    console.log('🚀 Starting comprehensive Square webhook tests...\n');

    const tests = [
      {
        name: 'Payment Completion',
        test: () => this.testPaymentCompletion()
      },
      {
        name: 'Payment Failure',
        test: () => this.testPaymentFailure()
      },
      {
        name: 'Refund Processing',
        test: () => this.testRefund()
      }
    ];

    const results = [];

    for (const test of tests) {
      try {
        console.log(`\n📋 Running test: ${test.name}`);
        await test.test();
        results.push({ name: test.name, status: 'PASSED' });
        console.log(`✅ ${test.name} test passed\n`);
      } catch (error) {
        results.push({ name: test.name, status: 'FAILED', error: error.message });
        console.log(`❌ ${test.name} test failed: ${error.message}\n`);
      }

      // Wait between tests
      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    // Print summary
    console.log('📊 Test Summary:');
    console.log('================');
    results.forEach(result => {
      const status = result.status === 'PASSED' ? '✅' : '❌';
      console.log(`${status} ${result.name}: ${result.status}`);
      if (result.error) {
        console.log(`   Error: ${result.error}`);
      }
    });

    const passedTests = results.filter(r => r.status === 'PASSED').length;
    const totalTests = results.length;
    console.log(`\n🎯 Results: ${passedTests}/${totalTests} tests passed`);

    return results;
  }

  /**
   * Validate webhook configuration
   */
  validateConfiguration() {
    console.log('🔍 Validating Square webhook configuration...\n');

    const requiredEnvVars = [
      'SQUARE_WEBHOOK_SIGNATURE_KEY',
      'SQUARE_APPLICATION_ID',
      'SQUARE_ACCESS_TOKEN',
      'SQUARE_LOCATION_ID',
      'SQUARE_ENVIRONMENT'
    ];

    const missing = requiredEnvVars.filter(varName => !process.env[varName]);

    if (missing.length > 0) {
      console.error('❌ Missing required environment variables:');
      missing.forEach(varName => console.error(`   - ${varName}`));
      return false;
    }

    console.log('✅ All required environment variables are set');
    console.log(`📍 Webhook URL: ${this.webhookUrl}`);
    console.log(`🏢 Square Environment: ${process.env.SQUARE_ENVIRONMENT}`);
    console.log(`📍 Location ID: ${process.env.SQUARE_LOCATION_ID}`);

    return true;
  }
}

// CLI interface
if (require.main === module) {
  const tester = new SquareWebhookTester();
  
  const command = process.argv[2];
  
  switch (command) {
    case 'validate':
      tester.validateConfiguration();
      break;
    case 'test-payment':
      tester.testPaymentCompletion().catch(console.error);
      break;
    case 'test-refund':
      tester.testRefund().catch(console.error);
      break;
    case 'test-all':
      tester.runAllTests().catch(console.error);
      break;
    default:
      console.log('Square Webhook Tester');
      console.log('Usage:');
      console.log('  node squareWebhookTester.js validate     - Validate configuration');
      console.log('  node squareWebhookTester.js test-payment - Test payment webhook');
      console.log('  node squareWebhookTester.js test-refund  - Test refund webhook');
      console.log('  node squareWebhookTester.js test-all     - Run all tests');
  }
}

module.exports = SquareWebhookTester;
