const SquareService = require('../services/squareService');
const { Product } = require('../models');
const { Op } = require('sequelize');

/**
 * Admin Shop Finder Controller
 * Handles admin-specific shop finder operations
 */
class AdminShopFinderController {
  /**
   * Get admin settings for shop finder
   * @route GET /api/admin/shop-finder/settings
   * @access Private (Admin)
   */
  static async getAdminSettings(req, res) {
    try {
      // In a real implementation, these would be stored in a settings table
      // For now, return default settings
      const settings = {
        showLiveInventory: true,
        autoRefreshInterval: 300000, // 5 minutes
        displayMode: 'live'
      };
      
      res.json({
        success: true,
        data: settings
      });
    } catch (error) {
      console.error('Get admin settings error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch admin settings',
        error: error.message
      });
    }
  }

  /**
   * Update admin settings for shop finder
   * @route PUT /api/admin/shop-finder/settings
   * @access Private (Admin)
   */
  static async updateAdminSettings(req, res) {
    try {
      const { showLiveInventory, autoRefreshInterval, displayMode } = req.body;
      
      // Validate settings
      if (typeof showLiveInventory !== 'boolean') {
        return res.status(400).json({
          success: false,
          message: 'showLiveInventory must be a boolean'
        });
      }
      
      if (autoRefreshInterval && (typeof autoRefreshInterval !== 'number' || autoRefreshInterval < 0)) {
        return res.status(400).json({
          success: false,
          message: 'autoRefreshInterval must be a positive number'
        });
      }
      
      if (displayMode && !['live', 'manual'].includes(displayMode)) {
        return res.status(400).json({
          success: false,
          message: 'displayMode must be either "live" or "manual"'
        });
      }
      
      // In a real implementation, you would save these to a settings table
      const updatedSettings = {
        showLiveInventory,
        autoRefreshInterval: autoRefreshInterval || 300000,
        displayMode: displayMode || 'live'
      };
      
      res.json({
        success: true,
        data: updatedSettings,
        message: 'Admin settings updated successfully'
      });
    } catch (error) {
      console.error('Update admin settings error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to update admin settings',
        error: error.message
      });
    }
  }

  /**
   * Sync inventory with Square for a specific location
   * @route POST /api/admin/shop-finder/locations/:locationId/sync
   * @access Private (Admin)
   */
  static async syncLocationInventory(req, res) {
    try {
      const { locationId } = req.params;
      
      // Get all products with Square item IDs
      const products = await Product.findAll({
        where: {
          squareItemId: { [Op.not]: null },
          isActive: true
        },
        attributes: ['id', 'name', 'sku', 'quantity', 'squareItemId']
      });
      
      const syncResults = [];
      let successCount = 0;
      let errorCount = 0;
      
      for (const product of products) {
        try {
          // Get current Square inventory
          const squareQuantity = await SquareService.getInventoryCount(product.squareItemId);
          
          if (squareQuantity !== null && squareQuantity !== product.quantity) {
            // Update local inventory to match Square
            await product.update({ quantity: squareQuantity });
            
            syncResults.push({
              productId: product.id,
              name: product.name,
              sku: product.sku,
              oldQuantity: product.quantity,
              newQuantity: squareQuantity,
              status: 'updated'
            });
            successCount++;
          } else {
            syncResults.push({
              productId: product.id,
              name: product.name,
              sku: product.sku,
              quantity: product.quantity,
              status: 'in_sync'
            });
            successCount++;
          }
        } catch (error) {
          console.error(`Error syncing product ${product.id}:`, error);
          syncResults.push({
            productId: product.id,
            name: product.name,
            sku: product.sku,
            status: 'error',
            error: error.message
          });
          errorCount++;
        }
      }
      
      res.json({
        success: true,
        data: {
          locationId,
          syncResults,
          summary: {
            totalProducts: products.length,
            successCount,
            errorCount,
            syncedAt: new Date().toISOString()
          }
        },
        message: `Inventory sync completed. ${successCount} products synced successfully, ${errorCount} errors.`
      });
    } catch (error) {
      console.error('Sync location inventory error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to sync location inventory',
        error: error.message
      });
    }
  }

  /**
   * Get all reservations with filtering
   * @route GET /api/admin/shop-finder/reservations
   * @access Private (Admin)
   */
  static async getAllReservations(req, res) {
    try {
      const {
        page = 1,
        limit = 20,
        status,
        locationId,
        productId,
        dateFrom,
        dateTo
      } = req.query;
      
      // In a real implementation, you would query a reservations table
      // For now, return mock data
      const mockReservations = [
        {
          id: 'RES-001',
          locationId: 'location-1',
          locationName: 'Main Store',
          productId: 1,
          productName: 'Sample Product',
          quantity: 2,
          customerEmail: '<EMAIL>',
          customerPhone: '+1234567890',
          status: 'active',
          expiresAt: new Date(Date.now() + 30 * 60 * 1000).toISOString(),
          createdAt: new Date().toISOString()
        }
      ];
      
      res.json({
        success: true,
        data: {
          reservations: mockReservations,
          pagination: {
            currentPage: parseInt(page),
            totalPages: 1,
            totalReservations: mockReservations.length,
            limit: parseInt(limit)
          }
        }
      });
    } catch (error) {
      console.error('Get all reservations error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch reservations',
        error: error.message
      });
    }
  }

  /**
   * Update reservation status
   * @route PUT /api/admin/shop-finder/reservations/:reservationId/status
   * @access Private (Admin)
   */
  static async updateReservationStatus(req, res) {
    try {
      const { reservationId } = req.params;
      const { status, notes } = req.body;
      
      if (!['active', 'expired', 'fulfilled', 'cancelled'].includes(status)) {
        return res.status(400).json({
          success: false,
          message: 'Invalid status. Must be one of: active, expired, fulfilled, cancelled'
        });
      }
      
      // In a real implementation, you would update the reservation in the database
      res.json({
        success: true,
        data: {
          reservationId,
          status,
          notes,
          updatedAt: new Date().toISOString(),
          updatedBy: req.user.id
        },
        message: 'Reservation status updated successfully'
      });
    } catch (error) {
      console.error('Update reservation status error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to update reservation status',
        error: error.message
      });
    }
  }

  /**
   * Get shop finder analytics
   * @route GET /api/admin/shop-finder/analytics
   * @access Private (Admin)
   */
  static async getAnalytics(req, res) {
    try {
      const { period = 30 } = req.query;
      
      // Get locations
      const locations = await SquareService.getLocations();
      const activeLocations = locations.filter(loc => loc.status === 'ACTIVE');
      
      // Get products with inventory
      const totalProducts = await Product.count({
        where: { isActive: true }
      });
      
      const inStockProducts = await Product.count({
        where: {
          isActive: true,
          quantity: { [Op.gt]: 0 }
        }
      });
      
      const lowStockProducts = await Product.count({
        where: {
          isActive: true,
          quantity: { [Op.lt]: 10, [Op.gt]: 0 }
        }
      });
      
      const outOfStockProducts = await Product.count({
        where: {
          isActive: true,
          quantity: 0
        }
      });
      
      // Mock analytics data
      const analytics = {
        overview: {
          totalLocations: locations.length,
          activeLocations: activeLocations.length,
          totalProducts,
          inStockProducts,
          lowStockProducts,
          outOfStockProducts,
          inventoryValue: 0, // Would calculate from product prices and quantities
          lastSyncAt: new Date().toISOString()
        },
        locationPerformance: activeLocations.map(location => ({
          locationId: location.id,
          locationName: location.name,
          totalViews: Math.floor(Math.random() * 1000),
          totalReservations: Math.floor(Math.random() * 50),
          conversionRate: (Math.random() * 10).toFixed(2)
        })),
        popularProducts: [], // Would get from actual usage data
        inventoryTrends: [], // Would get from historical data
        period: parseInt(period)
      };
      
      res.json({
        success: true,
        data: analytics
      });
    } catch (error) {
      console.error('Get shop finder analytics error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch shop finder analytics',
        error: error.message
      });
    }
  }
}

module.exports = AdminShopFinderController;
