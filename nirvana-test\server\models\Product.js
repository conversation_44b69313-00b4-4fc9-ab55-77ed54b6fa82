const { DataTypes } = require('sequelize');
const { sequelize } = require('./database');

const Product = sequelize.define('Product', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  name: {
    type: DataTypes.STRING(200),
    allowNull: false
  },
  slug: {
    type: DataTypes.STRING,
    allowNull: false,
    unique: true
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: false
  },
  shortDescription: {
    type: DataTypes.STRING(300),
    allowNull: true,
    field: 'short_description'
  },
  sku: {
    type: DataTypes.STRING,
    allowNull: false,
    unique: true
  },
  price: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    validate: {
      min: 0
    }
  },
  comparePrice: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    field: 'compare_price',
    validate: {
      min: 0
    }
  },
  costPrice: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    field: 'cost_price',
    validate: {
      min: 0
    }
  },
  trackQuantity: {
    type: DataTypes.BOOLEAN,
    defaultValue: true,
    field: 'track_quantity'
  },
  quantity: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    validate: {
      min: 0
    }
  },
  lowStockThreshold: {
    type: DataTypes.INTEGER,
    defaultValue: 10,
    field: 'low_stock_threshold',
    validate: {
      min: 0
    }
  },
  weight: {
    type: DataTypes.DECIMAL(8, 2),
    allowNull: true,
    validate: {
      min: 0
    }
  },
  dimensions: {
    type: DataTypes.JSON,
    allowNull: true
  },
  images: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: []
  },
  categoryId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    field: 'category_id',
    references: {
      model: 'categories',
      key: 'id'
    }
  },
  subcategoryId: {
    type: DataTypes.INTEGER,
    allowNull: true,
    field: 'subcategory_id',
    references: {
      model: 'categories',
      key: 'id'
    }
  },
  tags: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: []
  },
  variants: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: []
  },
  attributes: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: []
  },
  seoTitle: {
    type: DataTypes.STRING(60),
    allowNull: true,
    field: 'seo_title'
  },
  seoDescription: {
    type: DataTypes.STRING(160),
    allowNull: true,
    field: 'seo_description'
  },
  status: {
    type: DataTypes.ENUM('active', 'draft', 'archived'),
    defaultValue: 'draft'
  },
  featured: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  bestSeller: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    field: 'best_seller'
  },
  cannabinoid: {
    type: DataTypes.ENUM('THC-A', 'CBD', 'Delta-8', 'Delta-9', 'THC-P'),
    allowNull: false
  },
  strain: {
    type: DataTypes.ENUM('Sativa', 'Indica', 'Hybrid'),
    allowNull: true
  },
  effects: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: []
  },
  potency: {
    type: DataTypes.STRING,
    allowNull: true
  },
  labReports: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: [],
    field: 'lab_reports'
  },
  viewCount: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    field: 'view_count'
  },
  salesCount: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    field: 'sales_count'
  },
  averageRating: {
    type: DataTypes.DECIMAL(3, 2),
    defaultValue: 0,
    field: 'average_rating',
    validate: {
      min: 0,
      max: 5
    }
  },
  reviewCount: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    field: 'review_count'
  },
  isActive: {
    type: DataTypes.BOOLEAN,
    defaultValue: true,
    field: 'is_active'
  }
}, {
  tableName: 'products',
  hooks: {
    beforeCreate: (product) => {
      if (!product.slug && product.name) {
        product.slug = product.name
          .toLowerCase()
          .replace(/[^a-z0-9]+/g, '-')
          .replace(/(^-|-$)/g, '');
      }
    },
    beforeUpdate: (product) => {
      if (product.changed('name') && !product.slug) {
        product.slug = product.name
          .toLowerCase()
          .replace(/[^a-z0-9]+/g, '-')
          .replace(/(^-|-$)/g, '');
      }
    }
  }
});

// Instance methods
Product.prototype.isInStock = function() {
  if (!this.trackQuantity) return true;
  return this.quantity > 0;
};

Product.prototype.isLowStock = function() {
  if (!this.trackQuantity) return false;
  return this.quantity <= this.lowStockThreshold;
};

Product.prototype.getDiscountPercentage = function() {
  if (!this.comparePrice || this.comparePrice <= this.price) return 0;
  return Math.round(((this.comparePrice - this.price) / this.comparePrice) * 100);
};

Product.prototype.getMainImage = function() {
  return this.images && this.images.length > 0 ? this.images[0] : null;
};

module.exports = Product;
