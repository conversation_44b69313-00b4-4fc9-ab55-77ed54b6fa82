# Nirvana Organics - Production Deployment

This folder contains the complete production deployment package for Nirvana Organics e-commerce platform.

## 📁 Folder Contents

```
nirvana-production/
├── server/                          # Complete backend application
│   ├── index.js                     # Main server entry point
│   ├── admin-server.js              # Admin server entry point
│   ├── config/                      # Configuration files
│   ├── controllers/                 # API controllers
│   ├── middleware/                  # Express middleware
│   ├── models/                      # Database models
│   ├── routes/                      # API routes
│   ├── services/                    # Business logic services
│   ├── utils/                       # Utility functions
│   ├── migrations/                  # Database migrations
│   ├── seeders/                     # Database seeders
│   └── templates/                   # Email templates
├── dist/                            # Main frontend build (production)
├── dist-admin/                      # Admin frontend build (production)
├── public/                          # Static assets and uploads
├── .env.production                  # Main server environment config
├── .env.admin                       # Admin server environment config
├── ecosystem.config.js              # PM2 configuration
├── nginx-site.conf                  # Nginx web server configuration
├── package.json                     # Node.js dependencies
├── package-lock.json               # Dependency lock file
├── deploy.sh                        # Backend deployment script
├── deploy-nginx.sh                  # Nginx deployment script
├── diagnose-pm2-env.sh             # Environment diagnostic script
├── fix-admin-health-endpoint.sh    # Admin health endpoint fix
├── fix-pm2-env-loading.sh          # PM2 environment loading fix
├── set-permissions.sh              # File permissions management
├── verify-fix.sh                   # Deployment verification script
├── COMPREHENSIVE_TROUBLESHOOTING_GUIDE.md  # Troubleshooting guide
└── README.md                        # This file
```

## 🚀 Deployment Instructions

### Prerequisites

1. **Server Requirements:**
   - Ubuntu 20.04+ or CentOS 8+
   - Node.js 18+ and npm 9+
   - PM2 process manager (`npm install -g pm2`)
   - Nginx web server (`sudo apt install nginx`)
   - SSL certificate for shopnirvanaorganics.com (Let's Encrypt recommended)

2. **Database:**
   - MySQL 8.0+ or MariaDB 10.6+
   - Database and user credentials configured
   - Database accessible from server (srv1921.hstgr.io:3306)

3. **Domain Configuration:**
   - `shopnirvanaorganics.com` → Main customer site
   - `shopnirvanaorganics.com/admin` → Admin panel (path-based routing)
   - DNS A record pointing to your server IP

4. **Frontend Deployment:**
   - Frontend builds should be deployed to `/var/www/nirvana-frontend/`
   - Main frontend: `/var/www/nirvana-frontend/main/`
   - Admin frontend: `/var/www/nirvana-frontend/admin/`

5. **Production Security:**
   - Firewall configured (UFW recommended)
   - SSH key-based authentication
   - Regular security updates
   - SSL/TLS certificates properly configured

### Step 1: Configure Environment Variables

**Edit `.env.production`:**
```bash
# Update these values with your production credentials
DB_HOST=srv1921.hstgr.io
DB_NAME=u106832845_nirvana
DB_USER=u106832845_root
DB_PASSWORD=your-production-db-password

JWT_SECRET=your-super-secure-jwt-secret-key-here-64-characters-minimum
SQUARE_ACCESS_TOKEN=your-production-square-access-token
SQUARE_ENVIRONMENT=production
GOOGLE_CLIENT_SECRET=your-production-google-client-secret
FRONTEND_URL=https://shopnirvanaorganics.com
ADMIN_FRONTEND_URL=https://shopnirvanaorganics.com/admin
# ... (see file for all required variables)
```

**Edit `.env.admin`:**
```bash
# Update these values with your admin production credentials
DB_HOST=srv1921.hstgr.io
DB_NAME=u106832845_nirvana
DB_USER=u106832845_root
DB_PASSWORD=your-production-db-password

JWT_SECRET=your-super-secure-admin-jwt-secret-key-here-64-characters
ADMIN_IP_WHITELIST=your-admin-ip-addresses-comma-separated
FRONTEND_URL=https://shopnirvanaorganics.com/admin
# ... (see file for all required variables)
```

### Step 2: Upload to Server

```bash
# Upload the entire nirvana-production folder to your server
scp -r nirvana-production/ user@your-server:/tmp/

# Or use rsync for better performance
rsync -avz --progress nirvana-production/ user@your-server:/tmp/nirvana-production/
```

### Step 3: Deploy Backend Services

```bash
# SSH into your server
ssh user@your-server

# Navigate to deployment folder
cd /tmp/nirvana-production

# Make deployment script executable
chmod +x deploy.sh

# Run backend deployment (requires sudo access)
sudo ./deploy.sh
```

### Step 4: Deploy Nginx Configuration

```bash
# Make Nginx deployment script executable
chmod +x deploy-nginx.sh

# Deploy Nginx configuration (requires sudo access)
sudo ./deploy-nginx.sh
```

The `deploy-nginx.sh` script will:
- Copy the Nginx configuration to `/etc/nginx/sites-available/`
- Enable the site by creating a symbolic link
- Test the Nginx configuration
- Reload Nginx to apply changes

### Step 5: Verify Deployment

```bash
# Check PM2 processes
pm2 status

# Check Nginx status
sudo systemctl status nginx

# Test the deployment
./verify-fix.sh
```



## 🔍 Verification

After deployment, verify everything is working:

```bash
# Check PM2 processes
sudo -u Nirvana pm2 status

# Check logs
sudo -u Nirvana pm2 logs

# Test health endpoints
curl https://shopnirvanaorganics.com/api/health
curl https://shopnirvanaorganics.com/admin/api/health

# Test frontend
curl -I https://shopnirvanaorganics.com
curl -I https://shopnirvanaorganics.com/admin

# Check Nginx status
sudo systemctl status nginx

# View Nginx logs
sudo tail -f /var/log/nginx/nirvana-production-main-*.log
```

## 🔧 Management Commands

### Service Management
```bash
# Restart services
sudo -u Nirvana pm2 restart nirvana-backend-main-production nirvana-backend-admin-production

# View logs
sudo -u Nirvana pm2 logs

# Monitor processes
sudo -u Nirvana pm2 monit

# Stop services
sudo -u Nirvana pm2 stop nirvana-backend-main-production nirvana-backend-admin-production

# Start services
sudo -u Nirvana pm2 start ecosystem.config.js

# Reload Nginx configuration
sudo systemctl reload nginx

# Restart Nginx
sudo systemctl restart nginx
```

### Database Management
The production environment includes a comprehensive database management script:

```bash
# Check database status and tables
node database-manager.js check

# Test database connection and functionality
node database-manager.js test

# Initialize database schema and run migrations
node database-manager.js init

# Seed basic data (roles, categories)
node database-manager.js seed

# Comprehensive database fix (recommended for issues)
node database-manager.js fix

# Debug environment variables and configuration
node database-manager.js debug

# Show help and available commands
node database-manager.js help
```

**Common Database Operations:**
```bash
# Fix all database issues (most common)
cd /var/www/nirvana-backend-production
NODE_ENV=production node database-manager.js fix

# Check if database is working properly
NODE_ENV=production node database-manager.js check

# Debug connection issues
NODE_ENV=production node database-manager.js debug
```

## 🏭 Production Features

This production environment includes:

1. **Optimized Performance:** Minified assets and production builds
2. **Enhanced Security:** Strict security headers and rate limiting
3. **Production Database:** Live database with real customer data
4. **Live APIs:** Production Square payments and real integrations
5. **SSL/HTTPS:** Full SSL encryption for all communications
6. **Path-based Admin:** Admin panel accessible at `/admin` path
7. **Production Monitoring:** Enhanced logging and error tracking

## 🌐 Nginx Configuration Features

The included `nginx-site.conf` provides:

1. **SSL/HTTPS Enforcement:** Automatic HTTP to HTTPS redirects
2. **Path-based Admin Routing:** Admin panel at `/admin` instead of subdomain
3. **API Reverse Proxy:** Backend API routing to ports 5000 and 3001
4. **Static File Serving:** Optimized serving with long-term caching
5. **Production Security Headers:** Strict CSP, HSTS, X-Frame-Options
6. **Rate Limiting:** Production-appropriate limits (stricter than test)
7. **Gzip Compression:** Automatic compression for better performance
8. **Upload Security:** Prevents execution of dangerous file types
9. **Health Endpoints:** `/health` endpoint for monitoring
10. **Production Environment Headers:** `X-Environment: PRODUCTION` header

## 🛡️ Security Notes

1. **Environment Files:** Never commit `.env.production` or `.env.admin` to version control
2. **File Permissions:** Ensure environment files have restricted permissions (600)
3. **Admin Access:** Configure `ADMIN_IP_WHITELIST` to restrict admin panel access
4. **SSL/TLS:** Always use HTTPS in production with valid certificates
5. **Database:** Use strong passwords and enable SSL for database connections
6. **Firewall:** Configure UFW or iptables to restrict access
7. **Updates:** Keep system and dependencies updated regularly
8. **Monitoring:** Set up monitoring and alerting for production issues

## 📞 Support

For deployment issues or questions, contact the development team.

**Deployment Paths:**
- Backend: `/var/www/nirvana-backend-production`
- Frontend: `/var/www/nirvana-frontend`
- Logs: `/var/log/pm2/` and `/var/log/nginx/`

## 🔄 Production Deployment Checklist

Before deploying to production:

- [ ] Test environment thoroughly validated
- [ ] All environment variables configured with production values
- [ ] SSL certificates installed and valid
- [ ] Database backups configured
- [ ] Monitoring and alerting set up
- [ ] Admin IP whitelist configured
- [ ] Security headers tested
- [ ] Performance testing completed
- [ ] Rollback plan prepared
