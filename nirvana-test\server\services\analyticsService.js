const { models } = require('../models');
const { Op } = require('sequelize');
const ExcelJS = require('exceljs');
const json2csv = require('json2csv').parse;

/**
 * Analytics Service
 * Handles comprehensive analytics, reporting, and data export functionality
 */
class AnalyticsService {
  /**
   * Get comprehensive dashboard analytics
   */
  async getDashboardAnalytics(dateRange = {}) {
    try {
      const { startDate, endDate } = dateRange;
      const dateFilter = {};
      
      if (startDate) dateFilter[Op.gte] = new Date(startDate);
      if (endDate) dateFilter[Op.lte] = new Date(endDate);

      // Revenue Analytics
      const revenueData = await this.getRevenueAnalytics(dateFilter);
      
      // Order Analytics
      const orderData = await this.getOrderAnalytics(dateFilter);
      
      // Customer Analytics
      const customerData = await this.getCustomerAnalytics(dateFilter);
      
      // Product Analytics
      const productData = await this.getProductAnalytics(dateFilter);
      
      // Traffic Analytics
      const trafficData = await this.getTrafficAnalytics(dateFilter);
      
      // Marketing Analytics
      const marketingData = await this.getMarketingAnalytics(dateFilter);

      return {
        success: true,
        data: {
          revenue: revenueData,
          orders: orderData,
          customers: customerData,
          products: productData,
          traffic: trafficData,
          marketing: marketingData,
          generatedAt: new Date().toISOString()
        }
      };
    } catch (error) {
      console.error('Error getting dashboard analytics:', error);
      return {
        success: false,
        message: 'Failed to get dashboard analytics',
        error: error.message
      };
    }
  }

  /**
   * Get revenue analytics
   */
  async getRevenueAnalytics(dateFilter) {
    const whereClause = {
      status: { [Op.in]: ['completed', 'delivered'] }
    };
    
    if (Object.keys(dateFilter).length > 0) {
      whereClause.createdAt = dateFilter;
    }

    const totalRevenue = await models.Order.sum('total', { where: whereClause });
    
    const revenueByPeriod = await models.Order.findAll({
      attributes: [
        [models.sequelize.fn('DATE', models.sequelize.col('createdAt')), 'date'],
        [models.sequelize.fn('SUM', models.sequelize.col('total')), 'revenue'],
        [models.sequelize.fn('COUNT', models.sequelize.col('id')), 'orders']
      ],
      where: whereClause,
      group: [models.sequelize.fn('DATE', models.sequelize.col('createdAt'))],
      order: [[models.sequelize.fn('DATE', models.sequelize.col('createdAt')), 'ASC']],
      raw: true
    });

    const averageOrderValue = totalRevenue && revenueByPeriod.length > 0 
      ? totalRevenue / revenueByPeriod.reduce((sum, day) => sum + parseInt(day.orders), 0)
      : 0;

    return {
      totalRevenue: totalRevenue || 0,
      averageOrderValue,
      revenueByDay: revenueByPeriod,
      growth: await this.calculateGrowth('revenue', dateFilter)
    };
  }

  /**
   * Get order analytics
   */
  async getOrderAnalytics(dateFilter) {
    const whereClause = {};
    
    if (Object.keys(dateFilter).length > 0) {
      whereClause.createdAt = dateFilter;
    }

    const totalOrders = await models.Order.count({ where: whereClause });
    
    const ordersByStatus = await models.Order.findAll({
      attributes: [
        'status',
        [models.sequelize.fn('COUNT', models.sequelize.col('id')), 'count']
      ],
      where: whereClause,
      group: ['status'],
      raw: true
    });

    const ordersByShipping = await models.Order.findAll({
      attributes: [
        'shippingMethod',
        [models.sequelize.fn('COUNT', models.sequelize.col('id')), 'count'],
        [models.sequelize.fn('SUM', models.sequelize.col('total')), 'revenue']
      ],
      where: whereClause,
      group: ['shippingMethod'],
      raw: true
    });

    return {
      totalOrders,
      ordersByStatus,
      ordersByShipping,
      growth: await this.calculateGrowth('orders', dateFilter)
    };
  }

  /**
   * Get customer analytics
   */
  async getCustomerAnalytics(dateFilter) {
    const whereClause = {};
    
    if (Object.keys(dateFilter).length > 0) {
      whereClause.createdAt = dateFilter;
    }

    const totalCustomers = await models.User.count({
      where: { ...whereClause, role: 'customer' }
    });

    const customersByMembership = await models.User.findAll({
      attributes: [
        'membershipType',
        [models.sequelize.fn('COUNT', models.sequelize.col('id')), 'count']
      ],
      where: { ...whereClause, role: 'customer' },
      group: ['membershipType'],
      raw: true
    });

    const customersByTrafficSource = await models.User.findAll({
      attributes: [
        'trafficSource',
        [models.sequelize.fn('COUNT', models.sequelize.col('id')), 'count']
      ],
      where: { ...whereClause, role: 'customer' },
      group: ['trafficSource'],
      raw: true
    });

    const customersByAuthProvider = await models.User.findAll({
      attributes: [
        'authProvider',
        [models.sequelize.fn('COUNT', models.sequelize.col('id')), 'count']
      ],
      where: { ...whereClause, role: 'customer' },
      group: ['authProvider'],
      raw: true
    });

    return {
      totalCustomers,
      customersByMembership,
      customersByTrafficSource,
      customersByAuthProvider,
      growth: await this.calculateGrowth('customers', dateFilter)
    };
  }

  /**
   * Get product analytics
   */
  async getProductAnalytics(dateFilter) {
    const whereClause = {};
    
    if (Object.keys(dateFilter).length > 0) {
      whereClause.createdAt = dateFilter;
    }

    const topProducts = await models.OrderItem.findAll({
      attributes: [
        [models.sequelize.col('product.name'), 'productName'],
        [models.sequelize.fn('SUM', models.sequelize.col('quantity')), 'totalSold'],
        [models.sequelize.fn('SUM', models.sequelize.literal('quantity * price')), 'totalRevenue']
      ],
      include: [
        {
          model: models.Product,
          as: 'product',
          attributes: []
        },
        {
          model: models.Order,
          as: 'order',
          where: { status: { [Op.in]: ['completed', 'delivered'] }, ...whereClause },
          attributes: []
        }
      ],
      group: ['product.id', 'product.name'],
      order: [[models.sequelize.fn('SUM', models.sequelize.col('quantity')), 'DESC']],
      limit: 10,
      raw: true
    });

    const categoryPerformance = await models.OrderItem.findAll({
      attributes: [
        [models.sequelize.col('product.category.name'), 'categoryName'],
        [models.sequelize.fn('SUM', models.sequelize.col('quantity')), 'totalSold'],
        [models.sequelize.fn('SUM', models.sequelize.literal('quantity * price')), 'totalRevenue']
      ],
      include: [
        {
          model: models.Product,
          as: 'product',
          include: [
            {
              model: models.Category,
              as: 'category',
              attributes: []
            }
          ],
          attributes: []
        },
        {
          model: models.Order,
          as: 'order',
          where: { status: { [Op.in]: ['completed', 'delivered'] }, ...whereClause },
          attributes: []
        }
      ],
      group: ['product.category.id', 'product.category.name'],
      order: [[models.sequelize.fn('SUM', models.sequelize.literal('quantity * price')), 'DESC']],
      raw: true
    });

    return {
      topProducts,
      categoryPerformance
    };
  }

  /**
   * Get traffic analytics
   */
  async getTrafficAnalytics(dateFilter) {
    const whereClause = { role: 'customer' };
    
    if (Object.keys(dateFilter).length > 0) {
      whereClause.createdAt = dateFilter;
    }

    const trafficSources = await models.User.findAll({
      attributes: [
        'trafficSource',
        [models.sequelize.fn('COUNT', models.sequelize.col('id')), 'visitors'],
        [models.sequelize.fn('COUNT', models.sequelize.col('orders.id')), 'conversions']
      ],
      include: [
        {
          model: models.Order,
          as: 'orders',
          where: { status: { [Op.in]: ['completed', 'delivered'] } },
          required: false,
          attributes: []
        }
      ],
      where: whereClause,
      group: ['User.trafficSource'],
      raw: true
    });

    return {
      trafficSources: trafficSources.map(source => ({
        ...source,
        conversionRate: source.visitors > 0 ? (source.conversions / source.visitors * 100).toFixed(2) : 0
      }))
    };
  }

  /**
   * Get marketing analytics
   */
  async getMarketingAnalytics(dateFilter) {
    const whereClause = {};
    
    if (Object.keys(dateFilter).length > 0) {
      whereClause.createdAt = dateFilter;
    }

    // Coupon usage analytics
    const couponUsage = await models.CouponUsage.findAll({
      attributes: [
        [models.sequelize.col('coupon.code'), 'couponCode'],
        [models.sequelize.fn('COUNT', models.sequelize.col('CouponUsage.id')), 'usageCount'],
        [models.sequelize.fn('SUM', models.sequelize.col('discountAmount')), 'totalDiscount']
      ],
      include: [
        {
          model: models.Coupon,
          as: 'coupon',
          attributes: []
        }
      ],
      where: whereClause,
      group: ['coupon.id', 'coupon.code'],
      order: [[models.sequelize.fn('COUNT', models.sequelize.col('CouponUsage.id')), 'DESC']],
      limit: 10,
      raw: true
    });

    // Referral analytics
    const referralStats = await models.Referral.findAll({
      attributes: [
        'status',
        [models.sequelize.fn('COUNT', models.sequelize.col('id')), 'count']
      ],
      where: whereClause,
      group: ['status'],
      raw: true
    });

    return {
      couponUsage,
      referralStats
    };
  }

  /**
   * Calculate growth percentage
   */
  async calculateGrowth(metric, dateFilter) {
    try {
      // This is a simplified growth calculation
      // In a real implementation, you'd compare with the previous period
      return Math.floor(Math.random() * 20) - 5; // Mock growth between -5% and 15%
    } catch (error) {
      console.error('Error calculating growth:', error);
      return 0;
    }
  }

  /**
   * Export analytics data to Excel
   */
  async exportToExcel(data, filename = 'analytics-export') {
    try {
      const workbook = new ExcelJS.Workbook();
      
      // Revenue Sheet
      if (data.revenue) {
        const revenueSheet = workbook.addWorksheet('Revenue Analytics');
        revenueSheet.columns = [
          { header: 'Date', key: 'date', width: 15 },
          { header: 'Revenue', key: 'revenue', width: 15 },
          { header: 'Orders', key: 'orders', width: 10 }
        ];
        revenueSheet.addRows(data.revenue.revenueByDay || []);
      }

      // Customer Sheet
      if (data.customers) {
        const customerSheet = workbook.addWorksheet('Customer Analytics');
        customerSheet.columns = [
          { header: 'Membership Type', key: 'membershipType', width: 20 },
          { header: 'Count', key: 'count', width: 10 }
        ];
        customerSheet.addRows(data.customers.customersByMembership || []);
      }

      // Product Sheet
      if (data.products) {
        const productSheet = workbook.addWorksheet('Product Analytics');
        productSheet.columns = [
          { header: 'Product Name', key: 'productName', width: 30 },
          { header: 'Total Sold', key: 'totalSold', width: 15 },
          { header: 'Total Revenue', key: 'totalRevenue', width: 15 }
        ];
        productSheet.addRows(data.products.topProducts || []);
      }

      const buffer = await workbook.xlsx.writeBuffer();
      
      return {
        success: true,
        data: buffer,
        filename: `${filename}.xlsx`,
        contentType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      };
    } catch (error) {
      console.error('Error exporting to Excel:', error);
      return {
        success: false,
        message: 'Failed to export to Excel',
        error: error.message
      };
    }
  }

  /**
   * Export analytics data to CSV
   */
  async exportToCSV(data, filename = 'analytics-export') {
    try {
      let csvData = [];

      // Flatten the data structure for CSV export
      if (data.revenue && data.revenue.revenueByDay) {
        csvData = data.revenue.revenueByDay.map(item => ({
          type: 'Revenue',
          date: item.date,
          value: item.revenue,
          orders: item.orders
        }));
      }

      if (data.customers && data.customers.customersByMembership) {
        const customerData = data.customers.customersByMembership.map(item => ({
          type: 'Customer',
          category: item.membershipType,
          count: item.count,
          date: new Date().toISOString().split('T')[0]
        }));
        csvData = csvData.concat(customerData);
      }

      if (data.products && data.products.topProducts) {
        const productData = data.products.topProducts.map(item => ({
          type: 'Product',
          name: item.productName,
          totalSold: item.totalSold,
          totalRevenue: item.totalRevenue
        }));
        csvData = csvData.concat(productData);
      }

      const csv = json2csv(csvData);

      return {
        success: true,
        data: csv,
        filename: `${filename}.csv`,
        contentType: 'text/csv'
      };
    } catch (error) {
      console.error('Error exporting to CSV:', error);
      return {
        success: false,
        message: 'Failed to export to CSV',
        error: error.message
      };
    }
  }

  /**
   * Get real-time analytics
   */
  async getRealTimeAnalytics() {
    try {
      const today = new Date();
      const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());

      const todayOrders = await models.Order.count({
        where: {
          createdAt: { [Op.gte]: startOfDay }
        }
      });

      const todayRevenue = await models.Order.sum('total', {
        where: {
          createdAt: { [Op.gte]: startOfDay },
          status: { [Op.in]: ['completed', 'delivered'] }
        }
      });

      const activeUsers = await models.User.count({
        where: {
          lastLoginAt: { [Op.gte]: new Date(Date.now() - 24 * 60 * 60 * 1000) }
        }
      });

      const pendingOrders = await models.Order.count({
        where: {
          status: 'pending'
        }
      });

      return {
        success: true,
        data: {
          todayOrders,
          todayRevenue: todayRevenue || 0,
          activeUsers,
          pendingOrders,
          timestamp: new Date().toISOString()
        }
      };
    } catch (error) {
      console.error('Error getting real-time analytics:', error);
      return {
        success: false,
        message: 'Failed to get real-time analytics',
        error: error.message
      };
    }
  }

  /**
   * Get customer lifetime value analytics
   */
  async getCustomerLTVAnalytics() {
    try {
      const ltvData = await models.User.findAll({
        attributes: [
          'id',
          'firstName',
          'lastName',
          'email',
          'membershipType',
          [models.sequelize.fn('COUNT', models.sequelize.col('orders.id')), 'orderCount'],
          [models.sequelize.fn('SUM', models.sequelize.col('orders.total')), 'lifetimeValue'],
          [models.sequelize.fn('AVG', models.sequelize.col('orders.total')), 'averageOrderValue']
        ],
        include: [
          {
            model: models.Order,
            as: 'orders',
            where: { status: { [Op.in]: ['completed', 'delivered'] } },
            attributes: [],
            required: true
          }
        ],
        where: { role: 'customer' },
        group: ['User.id'],
        having: models.sequelize.where(
          models.sequelize.fn('COUNT', models.sequelize.col('orders.id')),
          '>',
          0
        ),
        order: [[models.sequelize.fn('SUM', models.sequelize.col('orders.total')), 'DESC']],
        limit: 100,
        raw: true
      });

      // Calculate LTV segments
      const ltvSegments = {
        high: ltvData.filter(customer => parseFloat(customer.lifetimeValue) >= 500).length,
        medium: ltvData.filter(customer => parseFloat(customer.lifetimeValue) >= 100 && parseFloat(customer.lifetimeValue) < 500).length,
        low: ltvData.filter(customer => parseFloat(customer.lifetimeValue) < 100).length
      };

      return {
        success: true,
        data: {
          topCustomers: ltvData.slice(0, 20),
          ltvSegments,
          totalCustomersAnalyzed: ltvData.length
        }
      };
    } catch (error) {
      console.error('Error getting customer LTV analytics:', error);
      return {
        success: false,
        message: 'Failed to get customer LTV analytics',
        error: error.message
      };
    }
  }

  /**
   * Get cohort analysis
   */
  async getCohortAnalysis(period = 'monthly') {
    try {
      // This is a simplified cohort analysis
      // In a real implementation, you'd track user retention over time
      const cohorts = await models.User.findAll({
        attributes: [
          [models.sequelize.fn('DATE_TRUNC', period, models.sequelize.col('User.createdAt')), 'cohort'],
          [models.sequelize.fn('COUNT', models.sequelize.col('User.id')), 'totalUsers'],
          [models.sequelize.fn('COUNT', models.sequelize.col('orders.id')), 'activeUsers']
        ],
        include: [
          {
            model: models.Order,
            as: 'orders',
            where: { status: { [Op.in]: ['completed', 'delivered'] } },
            required: false,
            attributes: []
          }
        ],
        where: { role: 'customer' },
        group: [models.sequelize.fn('DATE_TRUNC', period, models.sequelize.col('User.createdAt'))],
        order: [[models.sequelize.fn('DATE_TRUNC', period, models.sequelize.col('User.createdAt')), 'ASC']],
        raw: true
      });

      return {
        success: true,
        data: {
          cohorts: cohorts.map(cohort => ({
            ...cohort,
            retentionRate: cohort.totalUsers > 0 ? (cohort.activeUsers / cohort.totalUsers * 100).toFixed(2) : 0
          })),
          period
        }
      };
    } catch (error) {
      console.error('Error getting cohort analysis:', error);
      return {
        success: false,
        message: 'Failed to get cohort analysis',
        error: error.message
      };
    }
  }
}

module.exports = new AnalyticsService();
