const { User, Role } = require('../models'); // Assuming User and Role models are defined
const { generateToken, generateRefreshToken, verifyRefreshToken } = require('../utils/jwt'); // JWT utility functions
const { validationResult } = require('express-validator'); // For handling validation errors from express-validator
const crypto = require('crypto'); // Node.js built-in crypto module for token generation
const nodemailer = require('nodemailer'); // For sending emails
const { OAuth2Client } = require('google-auth-library'); // For Google OAuth
const axios = require('axios'); // For making HTTP requests (e.g., to external APIs if needed, though not directly used in these functions)

// Email transporter setup
const createEmailTransporter = () => {
  return nodemailer.createTransport({
    host: process.env.EMAIL_HOST,
    port: process.env.EMAIL_PORT,
    secure: false, // Use 'true' if your SMTP server uses SSL/TLS
    auth: {
      user: process.env.EMAIL_USER,
      pass: process.env.EMAIL_PASS
    }
  });
};

/**
 * Register new user
 * Handles user registration, including age verification, duplicate email check,
 * email verification token generation, and sending a verification email.
 */
const register = async (req, res) => {
  try {
    // Check for validation errors from express-validator middleware
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { email, password, firstName, lastName, phone, dateOfBirth } = req.body;

    // Age verification (21+ required for cannabis products, if applicable)
    if (dateOfBirth) {
      const birthDate = new Date(dateOfBirth);
      const today = new Date();
      let age = today.getFullYear() - birthDate.getFullYear();
      const monthDiff = today.getMonth() - birthDate.getMonth();

      // Adjust age if birthday hasn't occurred yet this year
      if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
        age--;
      }

      if (age < 21) {
        return res.status(400).json({
          success: false,
          message: 'You must be at least 21 years old to register'
        });
      }
    }

    // Check if user with the given email already exists
    const existingUser = await User.findOne({ where: { email: email.toLowerCase() } });
    if (existingUser) {
      return res.status(400).json({
        success: false,
        message: 'User with this email already exists'
      });
    }

    // Generate a unique verification token for email confirmation
    const verificationToken = crypto.randomBytes(32).toString('hex');

    // Create new user in the database
    // The User model should handle password hashing before saving
    const user = await User.create({
      email: email.toLowerCase(),
      password, // Password will be hashed by a model hook (e.g., beforeCreate)
      firstName,
      lastName,
      phone,
      dateOfBirth,
      verificationToken,
      isVerified: false // User is not verified until email confirmation
    });

    // Send verification email asynchronously
    try {
      const transporter = createEmailTransporter();
      const verificationUrl = `${process.env.FRONTEND_URL}/verify-email?token=${verificationToken}`;

      await transporter.sendMail({
        from: process.env.EMAIL_FROM,
        to: user.email,
        subject: 'Verify Your Email - Nirvana Organics',
        html: `
          <h1>Welcome to Nirvana Organics!</h1>
          <p>Thank you for registering. Please verify your email address by clicking the link below:</p>
          <a href="${verificationUrl}" style="background-color: #22c55e; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Verify Email</a>
          <p>If you didn't create this account, please ignore this email.</p>
        `
      });
    } catch (emailError) {
      console.error('Failed to send verification email:', emailError);
      // Do not block user registration if email sending fails, just log the error
    }

    // Generate JWT and refresh tokens for the newly registered user
    const token = generateToken({ userId: user.id, email: user.email });
    const refreshToken = generateRefreshToken({ userId: user.id });

    // Send successful registration response
    res.status(201).json({
      success: true,
      message: 'User registered successfully. Please check your email for verification.',
      data: {
        user: {
          id: user.id,
          email: user.email,
          firstName: user.firstName,
          lastName: user.lastName,
          role: user.role, // Assuming a default role is set on creation or fetched
          isVerified: user.isVerified
        },
        token,
        refreshToken
      }
    });

  } catch (error) {
    console.error('Registration error:', error);
    res.status(500).json({
      success: false,
      message: 'Registration failed',
      error: error.message
    });
  }
};

/**
 * Login user (for regular customer accounts only)
 * Authenticates a user based on email and password.
 */
const loginUser = async (req, res) => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { email, password } = req.body;

    // Find user by email, including their associated role
    const user = await User.findOne({
      where: { email: email.toLowerCase() },
      include: [{ model: Role, as: 'Role' }] // 'Role' is the alias for the association
    });
    if (!user) {
      return res.status(401).json({
        success: false,
        message: 'Invalid email or password'
      });
    }

    // Prevent admin/manager logins through this endpoint
    if (user.Role?.name === 'admin' || user.Role?.name === 'manager') {
      return res.status(403).json({
        success: false,
        message: 'Admin users must use the admin login endpoint'
      });
    }

    // Check if account is active
    if (!user.isActive) {
      return res.status(401).json({
        success: false,
        message: 'Account has been deactivated'
      });
    }

    // Verify password using a method on the User model (e.g., bcrypt comparison)
    const isPasswordValid = await user.comparePassword(password);
    if (!isPasswordValid) {
      return res.status(401).json({
        success: false,
        message: 'Invalid email or password'
      });
    }

    // Update last login timestamp
    user.lastLogin = new Date();
    await user.save();

    // Generate JWT and refresh tokens
    const token = generateToken({ userId: user.id, email: user.email });
    const refreshToken = generateRefreshToken({ userId: user.id });

    res.json({
      success: true,
      message: 'Login successful',
      data: {
        user: {
          id: user.id,
          email: user.email,
          firstName: user.firstName,
          lastName: user.lastName,
          role: user.Role?.name || 'customer', // Default to 'customer' if role not found
          isVerified: user.isVerified
        },
        token,
        refreshToken
      }
    });

  } catch (error) {
    console.error('User login error:', error);
    res.status(500).json({
      success: false,
      message: 'Login failed',
      error: error.message
    });
  }
};

/**
 * Login admin user (for 'admin' or 'manager' roles only)
 * Authenticates an admin/manager based on email and password.
 */
const loginAdmin = async (req, res) => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { email, password } = req.body;

    // Find user by email, including their associated role
    const user = await User.findOne({
      where: { email: email.toLowerCase() },
      include: [{ model: Role, as: 'Role' }]
    });
    if (!user) {
      return res.status(401).json({
        success: false,
        message: 'Invalid email or password'
      });
    }

    // Check if user is an admin or manager
    if (user.Role?.name !== 'admin' && user.Role?.name !== 'manager') {
      return res.status(403).json({
        success: false,
        message: 'Access denied. Admin privileges required.'
      });
    }

    // Check if account is active
    if (!user.isActive) {
      return res.status(401).json({
        success: false,
        message: 'Account has been deactivated'
      });
    }

    // Verify password
    const isPasswordValid = await user.comparePassword(password);
    if (!isPasswordValid) {
      return res.status(401).json({
        success: false,
        message: 'Invalid email or password'
      });
    }

    // Update last login timestamp
    user.lastLogin = new Date();
    await user.save();

    // Generate JWT and refresh tokens with an 'isAdmin' flag or role in payload
    const token = generateToken({
      userId: user.id,
      email: user.email,
      role: user.Role?.name,
      isAdmin: true // Indicate admin status in token
    });
    const refreshToken = generateRefreshToken({ userId: user.id });

    res.json({
      success: true,
      message: 'Admin login successful',
      data: {
        user: {
          id: user.id,
          email: user.email,
          firstName: user.firstName,
          lastName: user.lastName,
          role: user.Role?.name,
          isVerified: user.isVerified,
          isAdmin: true
        },
        token,
        refreshToken
      }
    });

  } catch (error) {
    console.error('Admin login error:', error);
    res.status(500).json({
      success: false,
      message: 'Admin login failed',
      error: error.message
    });
  }
};

/**
 * Legacy login method for backward compatibility.
 * This function attempts to redirect to either `loginAdmin` or `loginUser`
 * based on the user's role.
 */
const login = async (req, res) => {
  try {
    const { email } = req.body;

    // Find user to determine their role
    const user = await User.findOne({
      where: { email: email.toLowerCase() },
      include: [{ model: Role, as: 'Role' }]
    });

    // If user exists and has admin/manager role, use admin login
    if (user && (user.Role?.name === 'admin' || user.Role?.name === 'manager')) {
      return loginAdmin(req, res);
    } else {
      // Otherwise, default to regular user login
      return loginUser(req, res);
    }
  } catch (error) {
    // If there's an error finding the user initially, default to user login
    console.warn('Error in legacy login role check, defaulting to user login:', error.message);
    return loginUser(req, res);
  }
};

/**
 * Refresh token endpoint.
 * Issues a new access token and refresh token using a valid refresh token.
 */
const refreshToken = async (req, res) => {
  try {
    const { refreshToken } = req.body;

    if (!refreshToken) {
      return res.status(401).json({
        success: false,
        message: 'Refresh token required'
      });
    }

    // Verify the refresh token
    const decoded = verifyRefreshToken(refreshToken); // This function should throw if invalid/expired
    const user = await User.findByPk(decoded.userId);

    // Check if user exists and is active
    if (!user || !user.isActive) {
      return res.status(401).json({
        success: false,
        message: 'Invalid refresh token'
      });
    }

    // Generate new JWT and refresh tokens
    const newToken = generateToken({ userId: user.id, email: user.email });
    const newRefreshToken = generateRefreshToken({ userId: user.id });

    res.json({
      success: true,
      data: {
        token: newToken,
        refreshToken: newRefreshToken
      }
    });

  } catch (error) {
    // Catch errors from verifyRefreshToken (e.g., token expired, invalid)
    res.status(401).json({
      success: false,
      message: 'Invalid refresh token',
      error: error.message
    });
  }
};

/**
 * Verify email endpoint.
 * Activates a user account by validating a verification token sent via email.
 */
const verifyEmail = async (req, res) => {
  try {
    const { token } = req.body;

    // Find user by the provided verification token
    const user = await User.findOne({ where: { verificationToken: token } });
    if (!user) {
      return res.status(400).json({
        success: false,
        message: 'Invalid verification token'
      });
    }

    // Mark user as verified and clear the token
    user.isVerified = true;
    user.verificationToken = null;
    await user.save();

    res.json({
      success: true,
      message: 'Email verified successfully'
    });

  } catch (error) {
    console.error('Email verification failed:', error);
    res.status(500).json({
      success: false,
      message: 'Email verification failed',
      error: error.message
    });
  }
};

/**
 * Get current user profile.
 * Retrieves the profile details of the authenticated user.
 */
const getProfile = async (req, res) => {
  try {
    // req.user is populated by the 'authenticate' middleware
    const user = await User.findByPk(req.user.id, {
      // Exclude sensitive fields from the response
      attributes: { exclude: ['password', 'verificationToken', 'resetPasswordToken', 'resetPasswordExpires'] }
    });

    res.json({
      success: true,
      message: 'User profile fetched successfully.',
      user: user || null // Return null if user not found (shouldn't happen if authenticated)
    });

  } catch (error) {
    console.error('Failed to get profile:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get profile',
      error: error.message
    });
  }
};

/**
 * Update user profile.
 * Allows authenticated users to update their profile information.
 */
const updateProfile = async (req, res) => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { firstName, lastName, phone, dateOfBirth } = req.body;

    // Find the authenticated user
    const user = await User.findByPk(req.user.id);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found' // Should not happen if authentication works
      });
    }

    // Update user fields
    await user.update({ firstName, lastName, phone, dateOfBirth });

    res.json({
      success: true,
      message: 'Profile updated successfully',
      data: { user } // Return the updated user object
    });

  } catch (error) {
    console.error('Profile update failed:', error);
    res.status(500).json({
      success: false,
      message: 'Profile update failed',
      error: error.message
    });
  }
};

/**
 * Request password reset.
 * Sends a password reset link to the user's email if an account exists.
 * Implements security to avoid revealing user existence.
 */
const requestPasswordReset = async (req, res) => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { email } = req.body;
    const normalizedEmail = email.toLowerCase();

    // Find user by email, including role for email routing
    const user = await User.findOne({
      where: { email: normalizedEmail },
      include: [{
        model: require('../models/Role'), // Dynamically require Role model
        as: 'Role'
      }]
    });

    if (!user) {
      // For security, always return a generic success message
      return res.json({
        success: true,
        message: 'If an account with that email exists, a password reset link has been sent.'
      });
    }

    // Check if user account is active
    if (!user.isActive) {
      return res.json({
        success: true,
        message: 'If an account with that email exists, a password reset link has been sent.'
      });
    }

    // Generate a secure, time-limited reset token
    const tokenGenerator = require('../utils/tokenGenerator'); // Assuming tokenGenerator utility exists
    const { token: resetToken, expiresAt } = tokenGenerator.generateSecurePasswordResetToken({
      expirationHours: 1, // Token valid for 1 hour
      userId: user.id,
      email: normalizedEmail
    });

    // Update user record with the new reset token and its expiration
    await user.update({
      resetPasswordToken: resetToken,
      resetPasswordExpires: expiresAt
    });

    // Determine user type (admin/manager vs. customer) for email template/routing
    const userRole = user.Role ? user.Role.name : 'customer';
    const userType = ['admin', 'manager'].includes(userRole) ? 'admin' : 'customer';

    // Send password reset email using an email service
    let emailSent = false;
    try {
      const emailService = require('../services/emailService'); // Assuming emailService exists
      await emailService.sendPasswordResetEmail(user.email, resetToken, userType);
      emailSent = true;
      console.log(`Password reset email sent to ${user.email} (${userType})`);
    } catch (emailError) {
      console.error('Failed to send password reset email:', emailError);
      // Log the email error but don't fail the HTTP request
      console.error(`Email service error for user ${user.email}:`, emailError.message);
    }

    // Log password reset attempt for security monitoring
    const securityLogger = require('../services/securityLogger'); // Assuming securityLogger exists
    await securityLogger.logPasswordResetRequest({
      userId: user.id,
      email: user.email,
      userRole: userRole,
      ipAddress: req.ip || req.connection.remoteAddress,
      userAgent: req.get('User-Agent'),
      success: true,
      tokenGenerated: true,
      emailSent: emailSent
    });

    res.json({
      success: true,
      message: 'If an account with that email exists, a password reset link has been sent.'
    });

  } catch (error) {
    console.error('Password reset request error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to process password reset request',
      error: error.message
    });
  }
};

/**
 * Reset password endpoint.
 * Allows a user to set a new password using a valid reset token.
 */
const resetPassword = async (req, res) => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { token, password } = req.body;

    // Validate token format (e.g., hex string)
    const tokenGenerator = require('../utils/tokenGenerator');
    if (!tokenGenerator.validateTokenFormat(token, 'hex')) {
      return res.status(400).json({
        success: false,
        message: 'Invalid token format'
      });
    }

    // Find user with a valid (non-expired, matching) reset token
    const user = await User.findOne({
      where: {
        resetPasswordToken: token,
        resetPasswordExpires: { [require('sequelize').Op.gt]: new Date() }, // Token must not be expired
        isActive: true // User account must be active
      },
      include: [{
        model: require('../models/Role'),
        as: 'Role'
      }]
    });

    if (!user) {
      // Log failed password reset attempt for security
      const securityLogger = require('../services/securityLogger');
      await securityLogger.logPasswordResetFailure({
        email: 'unknown', // Email might not be known if token is invalid
        token: token,
        reason: 'Invalid or expired token',
        ipAddress: req.ip || req.connection.remoteAddress,
        userAgent: req.get('User-Agent')
      });

      return res.status(400).json({
        success: false,
        message: 'Invalid or expired reset token'
      });
    }

    // Additional security check: ensure token is still valid (redundant with Sequelize query but safe)
    if (!tokenGenerator.isTokenValid(user.resetPasswordExpires)) {
      const securityLogger = require('../services/securityLogger');
      await securityLogger.logPasswordResetFailure({
        email: user.email,
        token: token,
        reason: 'Token expired',
        ipAddress: req.ip || req.connection.remoteAddress,
        userAgent: req.get('User-Agent')
      });

      return res.status(400).json({
        success: false,
        message: 'Reset token has expired'
      });
    }

    // Update password and clear reset token fields
    await user.update({
      password, // Password will be hashed by a model hook
      resetPasswordToken: null,
      resetPasswordExpires: null
    });

    // Log successful password reset for security monitoring
    const userRole = user.Role ? user.Role.name : 'customer';
    const securityLogger = require('../services/securityLogger');
    await securityLogger.logPasswordResetCompletion({
      userId: user.id,
      email: user.email,
      userRole: userRole,
      ipAddress: req.ip || req.connection.remoteAddress,
      userAgent: req.get('User-Agent'),
      tokenUsed: token.substring(0, 8) + '...', // Log partial token for security
      success: true
    });

    res.json({
      success: true,
      message: 'Password reset successfully. You can now log in with your new password.'
    });

  } catch (error) {
    console.error('Password reset error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to reset password',
      error: error.message
    });
  }
};

/**
 * Logout endpoint.
 * For stateless JWTs, this primarily indicates client-side token removal.
 * For session-based systems, it would destroy the session.
 */
const logout = async (req, res) => {
  try {
    // For JWTs, logout is primarily a client-side action (removing tokens).
    // If using refresh tokens stored in DB, you might invalidate them here.
    // If using sessions, you would destroy the session: req.session.destroy();
    res.json({
      success: true,
      message: 'Logged out successfully'
    });

  } catch (error) {
    console.error('Logout error:', error);
    res.status(500).json({
      success: false,
      message: 'Logout failed',
      error: error.message
    });
  }
};

/**
 * Get user order history.
 * Retrieves a paginated list of orders for the authenticated user.
 */
const getOrderHistory = async (req, res) => {
  try {
    const { page = 1, limit = 10, status } = req.query;
    const { Order } = require('../models'); // Dynamically require Order model

    const where = { userId: req.user.id }; // Filter by authenticated user's ID
    if (status) {
      where.status = status; // Filter by order status if provided
    }

    // Find and count all orders matching criteria
    const { count, rows: orders } = await Order.findAndCountAll({
      where,
      order: [['createdAt', 'DESC']], // Sort by creation date descending
      offset: (parseInt(page) - 1) * parseInt(limit), // Pagination offset
      limit: parseInt(limit), // Pagination limit
      attributes: [
        'id', 'orderNumber', 'status', 'paymentStatus', 'total',
        'items', 'shippingAddress', 'createdAt', 'estimatedDelivery'
      ] // Select specific attributes to return
    });

    const totalPages = Math.ceil(count / parseInt(limit));

    res.json({
      success: true,
      data: {
        orders,
        pagination: {
          currentPage: parseInt(page),
          totalPages,
          totalOrders: count,
          hasNextPage: parseInt(page) < totalPages,
          hasPrevPage: parseInt(page) > 1
        }
      }
    });

  } catch (error) {
    console.error('Get order history error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch order history',
      error: error.message
    });
  }
};

/**
 * Get specific order details.
 * Retrieves details for a single order belonging to the authenticated user.
 */
const getOrderDetails = async (req, res) => {
  try {
    const { orderId } = req.params; // Get order ID from URL parameters
    const { Order } = require('../models');

    // Find the order, ensuring it belongs to the authenticated user
    const order = await Order.findOne({
      where: {
        id: orderId,
        userId: req.user.id
      }
    });

    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'Order not found'
      });
    }

    res.json({
      success: true,
      data: { order }
    });

  } catch (error) {
    console.error('Get order details error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch order details',
      error: error.message
    });
  }
};

/**
 * Update user address.
 * Allows authenticated users to update their billing or shipping address.
 */
const updateAddress = async (req, res) => {
  try {
    const { type, address } = req.body; // `type` can be 'billing' or 'shipping'
    const { Address } = require('../models'); // Dynamically require Address model

    // Validate address type
    if (!['billing', 'shipping'].includes(type)) {
      return res.status(400).json({
        success: false,
        message: 'Address type must be either "billing" or "shipping"'
      });
    }

    // Find existing address of the specified type for the user, or create a new one
    let userAddress = await Address.findOne({
      where: {
        userId: req.user.id,
        type
      }
    });

    if (userAddress) {
      // Update existing address
      await userAddress.update(address);
    } else {
      // Create new address
      userAddress = await Address.create({
        ...address,
        userId: req.user.id,
        type
      });
    }

    res.json({
      success: true,
      message: `${type.charAt(0).toUpperCase() + type.slice(1)} address updated successfully`,
      data: { address: userAddress }
    });

  } catch (error) {
    console.error('Update address error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update address',
      error: error.message
    });
  }
};

/**
 * Get user addresses.
 * Retrieves all addresses (billing and shipping) associated with the authenticated user.
 */
const getAddresses = async (req, res) => {
  try {
    const { Address } = require('../models');

    const addresses = await Address.findAll({
      where: { userId: req.user.id },
      order: [['type', 'ASC']] // Order by type for consistent display
    });

    res.json({
      success: true,
      data: { addresses }
    });

  } catch (error) {
    console.error('Get addresses error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch addresses',
      error: error.message
    });
  }
};

/**
 * Change user password.
 * Allows an authenticated user to change their password after verifying the current one.
 */
const changePassword = async (req, res) => {
  try {
    const { currentPassword, newPassword } = req.body;

    if (!currentPassword || !newPassword) {
      return res.status(400).json({
        success: false,
        message: 'Current password and new password are required'
      });
    }

    const user = await User.findByPk(req.user.id);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found' // Should not happen if authentication works
      });
    }

    // Verify current password
    const isCurrentPasswordValid = await user.comparePassword(currentPassword);
    if (!isCurrentPasswordValid) {
      return res.status(400).json({
        success: false,
        message: 'Current password is incorrect'
      });
    }

    // Update password (User model hook should hash newPassword)
    await user.update({ password: newPassword });

    res.json({
      success: true,
      message: 'Password changed successfully'
    });

  } catch (error) {
    console.error('Change password error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to change password',
      error: error.message
    });
  }
};

// Google OAuth client initialization
const googleClient = new OAuth2Client(process.env.GOOGLE_CLIENT_ID);

/**
 * Google OAuth login.
 * Handles user login/registration via Google ID token verification.
 */
const googleLogin = async (req, res) => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { credential } = req.body; // Google ID token

    // Verify the Google ID token
    const ticket = await googleClient.verifyIdToken({
      idToken: credential,
      audience: process.env.GOOGLE_CLIENT_ID // Your Google Client ID
    });

    const payload = ticket.getPayload();
    const { sub: googleId, email, given_name: firstName, family_name: lastName, picture } = payload;

    // Check if user already exists in your database by email
    let user = await User.findOne({ where: { email } });

    if (!user) {
      // If user doesn't exist, create a new user account
      user = await User.create({
        email,
        firstName: firstName || 'User',
        lastName: lastName || '',
        password: crypto.randomBytes(32).toString('hex'), // Set a random password for OAuth users
        isVerified: true, // Google emails are typically pre-verified
        googleId, // Store Google user ID
        profilePicture: picture,
        authProvider: 'google'
      });
    } else if (!user.googleId) {
      // If user exists but is not linked to Google, link the account
      await user.update({
        googleId,
        profilePicture: picture || user.profilePicture,
        authProvider: user.authProvider === 'local' ? 'both' : 'google' // Update auth provider
      });
    }

    // Update last login timestamp
    await user.update({ lastLogin: new Date() });

    // Generate JWT and refresh tokens
    const token = generateToken({ userId: user.id, email: user.email });
    const refreshToken = generateRefreshToken({ userId: user.id });

    res.json({
      success: true,
      message: 'Google login successful',
      data: {
        user: {
          id: user.id,
          firstName: user.firstName,
          lastName: user.lastName,
          email: user.email,
          role: user.role, // Assuming role is available or defaulted
          isVerified: user.isVerified,
          profilePicture: user.profilePicture
        },
        token,
        refreshToken
      }
    });

  } catch (error) {
    console.error('Google login error:', error);
    res.status(500).json({
      success: false,
      message: 'Google login failed',
      error: error.message
    });
  }
};

/**
 * Delete user account.
 * Allows an authenticated user to deactivate their account after password verification.
 * This is typically a soft delete (setting `isActive` to false).
 */
const deleteAccount = async (req, res) => {
  try {
    const userId = req.user.id;
    const { password } = req.body;

    if (!password) {
      return res.status(400).json({
        success: false,
        message: 'Password is required to delete account'
      });
    }

    // Get user with password for verification (ensure password field is included if necessary)
    const user = await User.findByPk(userId);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Verify password using bcrypt (assuming User model has comparePassword method)
    // Note: The original code snippet had `bcrypt.compare` here, but `user.comparePassword` is more consistent.
    const isPasswordValid = await user.comparePassword(password);
    if (!isPasswordValid) {
      return res.status(400).json({
        success: false,
        message: 'Password is incorrect'
      });
    }

    // Soft delete by deactivating account
    await user.update({ isActive: false });

    res.json({
      success: true,
      message: 'Account deleted successfully'
    });
  } catch (error) {
    console.error('Delete account error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete account',
      error: error.message
    });
  }
};

// Export all controller functions
module.exports = {
  register,
  login, // Legacy login handler
  loginUser,
  loginAdmin,
  refreshToken,
  verifyEmail,
  getProfile,
  updateProfile,
  requestPasswordReset,
  resetPassword,
  logout,
  getOrderHistory,
  getOrderDetails,
  updateAddress,
  getAddresses,
  changePassword,
  googleLogin,
  deleteAccount
};
