const { DataTypes } = require('sequelize');
const { sequelize } = require('./database');

const Category = sequelize.define('Category', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  name: {
    type: DataTypes.STRING(100),
    allowNull: false
  },
  slug: {
    type: DataTypes.STRING,
    allowNull: false,
    unique: true
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  image: {
    type: DataTypes.STRING,
    allowNull: true
  },
  parentId: {
    type: DataTypes.INTEGER,
    allowNull: true,
    field: 'parent_id',
    references: {
      model: 'categories',
      key: 'id'
    }
  },
  level: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    validate: {
      min: 0,
      max: 3
    }
  },
  status: {
    type: DataTypes.ENUM('active', 'inactive'),
    defaultValue: 'active'
  },
  sortOrder: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    field: 'sort_order'
  },
  seoTitle: {
    type: DataTypes.STRING(60),
    allowNull: true,
    field: 'seo_title'
  },
  seoDescription: {
    type: DataTypes.STRING(160),
    allowNull: true,
    field: 'seo_description'
  },
  productCount: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    field: 'product_count'
  }
}, {
  tableName: 'categories',
  hooks: {
    beforeCreate: (category) => {
      if (!category.slug && category.name) {
        category.slug = category.name
          .toLowerCase()
          .replace(/[^a-z0-9]+/g, '-')
          .replace(/(^-|-$)/g, '');
      }
    },
    beforeUpdate: (category) => {
      if (category.changed('name') && !category.slug) {
        category.slug = category.name
          .toLowerCase()
          .replace(/[^a-z0-9]+/g, '-')
          .replace(/(^-|-$)/g, '');
      }
    }
  }
});

// Self-referencing association for parent-child relationship
// Temporarily commented out to fix association issues
// Category.belongsTo(Category, { as: 'parent', foreignKey: 'parentId' });
// Category.hasMany(Category, { as: 'children', foreignKey: 'parentId' });

// Instance methods
Category.prototype.getChildren = async function() {
  return await Category.findAll({
    where: { parentId: this.id, status: 'active' },
    order: [['sortOrder', 'ASC'], ['name', 'ASC']]
  });
};

Category.prototype.getPath = async function() {
  const path = [this];
  let current = this;

  while (current.parentId) {
    current = await Category.findByPk(current.parentId);
    if (current) {
      path.unshift(current);
    } else {
      break;
    }
  }

  return path;
};

// Static methods
Category.buildTree = async function(parentId = null) {
  const categories = await Category.findAll({
    where: { parentId, status: 'active' },
    order: [['sortOrder', 'ASC'], ['name', 'ASC']]
  });

  const tree = [];
  for (const category of categories) {
    const children = await Category.buildTree(category.id);
    tree.push({
      ...category.toJSON(),
      children
    });
  }

  return tree;
};

module.exports = Category;
