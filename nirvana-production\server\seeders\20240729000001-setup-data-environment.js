'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    // Get admin users to set up their data environments
    const adminUsers = await queryInterface.sequelize.query(
      `SELECT u.id FROM users u 
       JOIN roles r ON u.role_id = r.id 
       WHERE r.name IN ('admin', 'manager')`,
      { type: Sequelize.QueryTypes.SELECT }
    );

    if (adminUsers.length > 0) {
      // Create default data environment entries for admin users
      const dataEnvironments = adminUsers.map(user => ({
        user_id: user.id,
        mode: 'real',
        session_id: null,
        is_active: true,
        last_switched: new Date(),
        metadata: JSON.stringify({
          initialSetup: true,
          setupDate: new Date().toISOString()
        }),
        created_at: new Date(),
        updated_at: new Date()
      }));

      await queryInterface.bulkInsert('data_environments', dataEnvironments);
    }

    // Create some sample mock data for demonstration
    const sampleMockData = [
      {
        entity_type: 'product',
        entity_id: null,
        mock_id: 'mock_product_sample_001',
        data: JSON.stringify({
          name: 'Sample Mock Product 1',
          slug: 'sample-mock-product-1',
          description: 'This is a sample mock product for testing the data environment system',
          price: 29.99,
          comparePrice: 39.99,
          sku: 'MOCK-PROD-001',
          isActive: true,
          stock: 100,
          category: 'Sample Category',
          images: ['/images/mock-product-1.jpg'],
          tags: ['sample', 'mock', 'testing']
        }),
        created_by: adminUsers.length > 0 ? adminUsers[0].id : 1,
        tags: JSON.stringify(['sample', 'demo', 'product']),
        is_active: true,
        description: 'Sample mock product for demonstration purposes',
        metadata: JSON.stringify({
          sampleData: true,
          category: 'demo'
        }),
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        entity_type: 'product',
        entity_id: null,
        mock_id: 'mock_product_sample_002',
        data: JSON.stringify({
          name: 'Sample Mock Product 2',
          slug: 'sample-mock-product-2',
          description: 'Another sample mock product for testing',
          price: 49.99,
          comparePrice: 59.99,
          sku: 'MOCK-PROD-002',
          isActive: true,
          stock: 75,
          category: 'Sample Category',
          images: ['/images/mock-product-2.jpg'],
          tags: ['sample', 'mock', 'testing']
        }),
        created_by: adminUsers.length > 0 ? adminUsers[0].id : 1,
        tags: JSON.stringify(['sample', 'demo', 'product']),
        is_active: true,
        description: 'Another sample mock product for demonstration',
        metadata: JSON.stringify({
          sampleData: true,
          category: 'demo'
        }),
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        entity_type: 'order',
        entity_id: null,
        mock_id: 'mock_order_sample_001',
        data: JSON.stringify({
          orderNumber: 'MOCK-ORDER-001',
          status: 'completed',
          total: 79.98,
          subtotal: 79.98,
          tax: 0,
          shipping: 0,
          items: [
            {
              productId: 'mock_product_sample_001',
              productName: 'Sample Mock Product 1',
              quantity: 1,
              price: 29.99,
              total: 29.99
            },
            {
              productId: 'mock_product_sample_002',
              productName: 'Sample Mock Product 2',
              quantity: 1,
              price: 49.99,
              total: 49.99
            }
          ],
          customer: {
            name: 'Mock Customer',
            email: '<EMAIL>'
          },
          shippingAddress: {
            street: '123 Mock Street',
            city: 'Mock City',
            state: 'MC',
            zipCode: '12345',
            country: 'Mock Country'
          }
        }),
        created_by: adminUsers.length > 0 ? adminUsers[0].id : 1,
        tags: JSON.stringify(['sample', 'demo', 'order']),
        is_active: true,
        description: 'Sample mock order for demonstration purposes',
        metadata: JSON.stringify({
          sampleData: true,
          category: 'demo'
        }),
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        entity_type: 'category',
        entity_id: null,
        mock_id: 'mock_category_sample_001',
        data: JSON.stringify({
          name: 'Sample Mock Category',
          slug: 'sample-mock-category',
          description: 'A sample category for mock products',
          isActive: true,
          sortOrder: 1,
          parentId: null,
          image: '/images/mock-category.jpg'
        }),
        created_by: adminUsers.length > 0 ? adminUsers[0].id : 1,
        tags: JSON.stringify(['sample', 'demo', 'category']),
        is_active: true,
        description: 'Sample mock category for demonstration',
        metadata: JSON.stringify({
          sampleData: true,
          category: 'demo'
        }),
        created_at: new Date(),
        updated_at: new Date()
      }
    ];

    await queryInterface.bulkInsert('mock_data', sampleMockData);
  },

  down: async (queryInterface, Sequelize) => {
    // Remove sample mock data
    await queryInterface.bulkDelete('mock_data', {
      mock_id: {
        [Sequelize.Op.in]: [
          'mock_product_sample_001',
          'mock_product_sample_002',
          'mock_order_sample_001',
          'mock_category_sample_001'
        ]
      }
    });

    // Remove data environment entries
    await queryInterface.bulkDelete('data_environments', {
      metadata: {
        [Sequelize.Op.like]: '%"initialSetup":true%'
      }
    });
  }
};
