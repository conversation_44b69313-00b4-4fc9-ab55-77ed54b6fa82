const { models } = require('../models');
const { Op } = require('sequelize');
const nodemailer = require('nodemailer');
const crypto = require('crypto');

/**
 * Campaign Service
 * Handles email campaigns, push notifications, and marketing automation
 */
class CampaignService {
  constructor() {
    // Initialize email transporter
    if (process.env.NODE_ENV === 'test') {
      // Use mock transporter for testing
      this.emailTransporter = nodemailer.createTransport({
        streamTransport: true,
        newline: 'unix',
        buffer: true
      });
    } else {
      // Use real SMTP for production/development
      this.emailTransporter = nodemailer.createTransport({
        host: process.env.SMTP_HOST || process.env.EMAIL_HOST || 'smtp.hostinger.com',
        port: process.env.SMTP_PORT || process.env.EMAIL_PORT || 587,
        secure: false,
        auth: {
          user: process.env.SMTP_USER || process.env.EMAIL_USER || '<EMAIL>',
          pass: process.env.SMTP_PASS || process.env.EMAIL_PASS
        }
      });
    }
  }

  /**
   * Create a new campaign
   */
  async createCampaign(campaignData, createdBy) {
    try {
      const campaign = await models.Campaign.create({
        ...campaignData,
        createdBy
      });

      return {
        success: true,
        data: campaign,
        message: 'Campaign created successfully'
      };
    } catch (error) {
      console.error('Error creating campaign:', error);
      return {
        success: false,
        message: 'Failed to create campaign',
        error: error.message
      };
    }
  }

  /**
   * Get campaign recipients based on targeting criteria
   */
  async getCampaignRecipients(campaign) {
    try {
      let recipients = [];

      switch (campaign.targetAudience) {
        case 'all':
          // Get all newsletter subscribers
          const allSubscribers = await models.NewsletterSubscription.findAll({
            where: { status: 'active' },
            include: [
              {
                model: models.User,
                as: 'user',
                required: false
              }
            ]
          });
          recipients = allSubscribers.map(sub => ({
            email: sub.email,
            userId: sub.userId,
            firstName: sub.firstName || sub.user?.firstName,
            lastName: sub.lastName || sub.user?.lastName
          }));
          break;

        case 'customers':
          // Get users who have made at least one order
          const customers = await models.User.findAll({
            include: [
              {
                model: models.Order,
                as: 'orders',
                where: { status: { [Op.in]: ['completed', 'delivered'] } },
                required: true
              },
              {
                model: models.NewsletterSubscription,
                as: 'newsletterSubscription',
                where: { status: 'active' },
                required: false
              }
            ]
          });
          recipients = customers.map(user => ({
            email: user.email,
            userId: user.id,
            firstName: user.firstName,
            lastName: user.lastName
          }));
          break;

        case 'subscribers':
          // Get newsletter subscribers only
          const subscribers = await models.NewsletterSubscription.findAll({
            where: { status: 'active' },
            include: [
              {
                model: models.User,
                as: 'user',
                required: false
              }
            ]
          });
          recipients = subscribers.map(sub => ({
            email: sub.email,
            userId: sub.userId,
            firstName: sub.firstName || sub.user?.firstName,
            lastName: sub.lastName || sub.user?.lastName
          }));
          break;

        case 'segment':
          // Apply segment criteria
          recipients = await this.getSegmentedRecipients(campaign.segmentCriteria);
          break;

        case 'custom':
          // Custom recipient list (would be provided in campaign data)
          recipients = campaign.customRecipients || [];
          break;
      }

      return {
        success: true,
        data: recipients
      };
    } catch (error) {
      console.error('Error getting campaign recipients:', error);
      return {
        success: false,
        message: 'Failed to get campaign recipients',
        error: error.message
      };
    }
  }

  /**
   * Get segmented recipients based on criteria
   */
  async getSegmentedRecipients(criteria) {
    try {
      const whereClause = {};
      const includeClause = [
        {
          model: models.NewsletterSubscription,
          as: 'newsletterSubscription',
          where: { status: 'active' },
          required: true
        }
      ];

      // Apply segmentation criteria
      if (criteria.membershipType) {
        whereClause.membershipType = criteria.membershipType;
      }

      if (criteria.trafficSource) {
        whereClause.trafficSource = criteria.trafficSource;
      }

      if (criteria.minOrderCount) {
        includeClause.push({
          model: models.Order,
          as: 'orders',
          where: { status: { [Op.in]: ['completed', 'delivered'] } },
          required: true,
          having: models.sequelize.where(
            models.sequelize.fn('COUNT', models.sequelize.col('orders.id')),
            '>=',
            criteria.minOrderCount
          )
        });
      }

      if (criteria.minTotalSpent) {
        includeClause.push({
          model: models.Order,
          as: 'orders',
          where: { status: { [Op.in]: ['completed', 'delivered'] } },
          required: true,
          having: models.sequelize.where(
            models.sequelize.fn('SUM', models.sequelize.col('orders.total')),
            '>=',
            criteria.minTotalSpent
          )
        });
      }

      const users = await models.User.findAll({
        where: whereClause,
        include: includeClause,
        group: ['User.id']
      });

      return users.map(user => ({
        email: user.email,
        userId: user.id,
        firstName: user.firstName,
        lastName: user.lastName
      }));
    } catch (error) {
      console.error('Error getting segmented recipients:', error);
      return [];
    }
  }

  /**
   * Send email campaign
   */
  async sendEmailCampaign(campaignId) {
    try {
      const campaign = await models.Campaign.findByPk(campaignId);
      if (!campaign) {
        return {
          success: false,
          message: 'Campaign not found'
        };
      }

      // Get recipients
      const recipientsResult = await this.getCampaignRecipients(campaign);
      if (!recipientsResult.success) {
        return recipientsResult;
      }

      const recipients = recipientsResult.data;

      // Create recipient records
      const recipientRecords = await Promise.all(
        recipients.map(recipient =>
          models.CampaignRecipient.create({
            campaignId: campaign.id,
            userId: recipient.userId,
            email: recipient.email,
            status: 'pending'
          })
        )
      );

      // Update campaign with recipient count
      await campaign.update({
        totalRecipients: recipients.length,
        status: 'active'
      });

      // Send emails in batches
      const batchSize = 50;
      let sentCount = 0;
      let deliveredCount = 0;

      for (let i = 0; i < recipientRecords.length; i += batchSize) {
        const batch = recipientRecords.slice(i, i + batchSize);
        
        await Promise.all(
          batch.map(async (recipientRecord) => {
            try {
              const recipient = recipients.find(r => r.email === recipientRecord.email);
              
              // Personalize content
              const personalizedContent = this.personalizeContent(
                campaign.htmlContent || campaign.content,
                recipient
              );

              const personalizedSubject = this.personalizeContent(
                campaign.subject,
                recipient
              );

              // Send email
              await this.emailTransporter.sendMail({
                from: `"Nirvana Organics" <${process.env.SMTP_USER}>`,
                to: recipient.email,
                subject: personalizedSubject,
                html: personalizedContent,
                text: campaign.content
              });

              // Update recipient status
              await recipientRecord.update({
                status: 'sent',
                sentAt: new Date()
              });

              sentCount++;
              deliveredCount++; // Assume delivered for now
            } catch (error) {
              console.error(`Error sending email to ${recipientRecord.email}:`, error);
              await recipientRecord.update({
                status: 'bounced',
                errorMessage: error.message
              });
            }
          })
        );

        // Small delay between batches to avoid overwhelming the SMTP server
        await new Promise(resolve => setTimeout(resolve, 1000));
      }

      // Update campaign statistics
      await campaign.update({
        sentCount,
        deliveredCount,
        status: 'completed'
      });

      return {
        success: true,
        data: {
          campaignId: campaign.id,
          totalRecipients: recipients.length,
          sentCount,
          deliveredCount
        },
        message: `Campaign sent successfully to ${sentCount} recipients`
      };

    } catch (error) {
      console.error('Error sending email campaign:', error);
      return {
        success: false,
        message: 'Failed to send email campaign',
        error: error.message
      };
    }
  }

  /**
   * Personalize content with recipient data
   */
  personalizeContent(content, recipient) {
    if (!content) return '';

    return content
      .replace(/\{\{firstName\}\}/g, recipient.firstName || 'Valued Customer')
      .replace(/\{\{lastName\}\}/g, recipient.lastName || '')
      .replace(/\{\{email\}\}/g, recipient.email || '')
      .replace(/\{\{fullName\}\}/g, `${recipient.firstName || ''} ${recipient.lastName || ''}`.trim() || 'Valued Customer');
  }

  /**
   * Create email template
   */
  async createEmailTemplate(templateData, createdBy) {
    try {
      const template = await models.EmailTemplate.create({
        ...templateData,
        createdBy
      });

      return {
        success: true,
        data: template,
        message: 'Email template created successfully'
      };
    } catch (error) {
      console.error('Error creating email template:', error);
      return {
        success: false,
        message: 'Failed to create email template',
        error: error.message
      };
    }
  }

  /**
   * Subscribe to newsletter
   */
  async subscribeToNewsletter(email, userData = {}) {
    try {
      const unsubscribeToken = crypto.randomBytes(32).toString('hex');

      const [subscription, created] = await models.NewsletterSubscription.findOrCreate({
        where: { email },
        defaults: {
          ...userData,
          email,
          status: 'active',
          confirmedAt: new Date(),
          unsubscribeToken
        }
      });

      if (!created && subscription.status === 'unsubscribed') {
        // Resubscribe
        await subscription.update({
          status: 'active',
          confirmedAt: new Date(),
          unsubscribedAt: null
        });
      }

      return {
        success: true,
        data: subscription,
        message: created ? 'Successfully subscribed to newsletter' : 'Already subscribed'
      };
    } catch (error) {
      console.error('Error subscribing to newsletter:', error);
      return {
        success: false,
        message: 'Failed to subscribe to newsletter',
        error: error.message
      };
    }
  }

  /**
   * Unsubscribe from newsletter
   */
  async unsubscribeFromNewsletter(token) {
    try {
      const subscription = await models.NewsletterSubscription.findOne({
        where: { unsubscribeToken: token }
      });

      if (!subscription) {
        return {
          success: false,
          message: 'Invalid unsubscribe token'
        };
      }

      await subscription.update({
        status: 'unsubscribed',
        unsubscribedAt: new Date()
      });

      return {
        success: true,
        message: 'Successfully unsubscribed from newsletter'
      };
    } catch (error) {
      console.error('Error unsubscribing from newsletter:', error);
      return {
        success: false,
        message: 'Failed to unsubscribe from newsletter',
        error: error.message
      };
    }
  }

  /**
   * Get campaign analytics
   */
  async getCampaignAnalytics(campaignId) {
    try {
      const campaign = await models.Campaign.findByPk(campaignId, {
        include: [
          {
            model: models.CampaignRecipient,
            as: 'recipients'
          }
        ]
      });

      if (!campaign) {
        return {
          success: false,
          message: 'Campaign not found'
        };
      }

      const analytics = {
        campaign: {
          id: campaign.id,
          name: campaign.name,
          type: campaign.type,
          status: campaign.status,
          createdAt: campaign.createdAt
        },
        metrics: {
          totalRecipients: campaign.totalRecipients,
          sentCount: campaign.sentCount,
          deliveredCount: campaign.deliveredCount,
          openedCount: campaign.openedCount,
          clickedCount: campaign.clickedCount,
          unsubscribedCount: campaign.unsubscribedCount,
          deliveryRate: campaign.totalRecipients > 0 ? (campaign.deliveredCount / campaign.totalRecipients) * 100 : 0,
          openRate: campaign.deliveredCount > 0 ? (campaign.openedCount / campaign.deliveredCount) * 100 : 0,
          clickRate: campaign.openedCount > 0 ? (campaign.clickedCount / campaign.openedCount) * 100 : 0,
          unsubscribeRate: campaign.deliveredCount > 0 ? (campaign.unsubscribedCount / campaign.deliveredCount) * 100 : 0
        },
        recipients: campaign.recipients.map(recipient => ({
          email: recipient.email,
          status: recipient.status,
          sentAt: recipient.sentAt,
          deliveredAt: recipient.deliveredAt,
          openedAt: recipient.openedAt,
          clickedAt: recipient.clickedAt
        }))
      };

      return {
        success: true,
        data: analytics
      };
    } catch (error) {
      console.error('Error getting campaign analytics:', error);
      return {
        success: false,
        message: 'Failed to get campaign analytics',
        error: error.message
      };
    }
  }

  /**
   * Send automated welcome email
   */
  async sendWelcomeEmail(userId) {
    try {
      const user = await models.User.findByPk(userId);
      if (!user) {
        return { success: false, message: 'User not found' };
      }

      const welcomeTemplate = await models.EmailTemplate.findOne({
        where: { category: 'welcome', isActive: true }
      });

      if (!welcomeTemplate) {
        return { success: false, message: 'Welcome template not found' };
      }

      const personalizedContent = this.personalizeContent(welcomeTemplate.htmlContent, {
        firstName: user.firstName,
        lastName: user.lastName,
        email: user.email
      });

      const personalizedSubject = this.personalizeContent(welcomeTemplate.subject, {
        firstName: user.firstName,
        lastName: user.lastName,
        email: user.email
      });

      await this.emailTransporter.sendMail({
        from: `"Nirvana Organics" <${process.env.SMTP_USER}>`,
        to: user.email,
        subject: personalizedSubject,
        html: personalizedContent,
        text: welcomeTemplate.textContent
      });

      return {
        success: true,
        message: 'Welcome email sent successfully'
      };
    } catch (error) {
      console.error('Error sending welcome email:', error);
      return {
        success: false,
        message: 'Failed to send welcome email',
        error: error.message
      };
    }
  }

  /**
   * Send abandoned cart email
   */
  async sendAbandonedCartEmail(userId) {
    try {
      const user = await models.User.findByPk(userId, {
        include: [
          {
            model: models.Cart,
            as: 'cart',
            include: [
              {
                model: models.CartItem,
                as: 'items',
                include: [
                  {
                    model: models.Product,
                    as: 'product'
                  }
                ]
              }
            ]
          }
        ]
      });

      if (!user || !user.cart || user.cart.items.length === 0) {
        return { success: false, message: 'No abandoned cart found' };
      }

      const abandonedCartTemplate = await models.EmailTemplate.findOne({
        where: { category: 'abandoned_cart', isActive: true }
      });

      if (!abandonedCartTemplate) {
        return { success: false, message: 'Abandoned cart template not found' };
      }

      // Generate cart items HTML
      const cartItemsHtml = user.cart.items.map(item => `
        <tr>
          <td>${item.product.name}</td>
          <td>${item.quantity}</td>
          <td>$${(item.price * item.quantity).toFixed(2)}</td>
        </tr>
      `).join('');

      const cartTotal = user.cart.items.reduce((total, item) => total + (item.price * item.quantity), 0);

      let personalizedContent = this.personalizeContent(abandonedCartTemplate.htmlContent, {
        firstName: user.firstName,
        lastName: user.lastName,
        email: user.email
      });

      // Replace cart-specific placeholders
      personalizedContent = personalizedContent
        .replace(/\{\{cartItems\}\}/g, cartItemsHtml)
        .replace(/\{\{cartTotal\}\}/g, `$${cartTotal.toFixed(2)}`)
        .replace(/\{\{cartUrl\}\}/g, `${process.env.FRONTEND_URL}/cart`);

      const personalizedSubject = this.personalizeContent(abandonedCartTemplate.subject, {
        firstName: user.firstName,
        lastName: user.lastName,
        email: user.email
      });

      await this.emailTransporter.sendMail({
        from: `"Nirvana Organics" <${process.env.SMTP_USER}>`,
        to: user.email,
        subject: personalizedSubject,
        html: personalizedContent,
        text: abandonedCartTemplate.textContent
      });

      return {
        success: true,
        message: 'Abandoned cart email sent successfully'
      };
    } catch (error) {
      console.error('Error sending abandoned cart email:', error);
      return {
        success: false,
        message: 'Failed to send abandoned cart email',
        error: error.message
      };
    }
  }
}

module.exports = new CampaignService();
