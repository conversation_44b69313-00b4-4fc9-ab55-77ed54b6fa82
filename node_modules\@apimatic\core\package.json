{"name": "@apimatic/core", "author": "APIMatic Ltd.", "version": "0.10.23", "license": "MIT", "sideEffects": false, "main": "lib/index.js", "module": "es/index.js", "types": "lib/index.d.ts", "files": ["umd", "lib/**/*.js", "lib/**/*.d.ts", "es/**/*.js", "es/**/*.d.ts", "src", "LICENSE.md"], "engines": {"node": ">=14.15.0 || >=16.0.0"}, "scripts": {"clean": "rimraf lib es umd tsconfig.tsbuildinfo", "test": "jest", "build": "npm run clean && tsc && rollup -c --bundleConfigAsCjs && npm run annotate:es", "annotate:es": "babel es --out-dir es --no-babelrc --plugins annotate-pure-calls", "preversion": "npm run test", "prepublishOnly": "npm run build", "size": "size-limit", "analyze": "size-limit --why", "lint": "tslint --project .", "lint:fix": "tslint --project . --fix", "check-style": "prettier --check \"{src,test}/**/*.ts\"", "check-style:fix": "prettier --write \"{src,test}/**/*.ts\""}, "size-limit": [{"path": "es/index.js", "limit": "5 KB"}], "devDependencies": {"@babel/cli": "^7.26.4", "@babel/core": "^7.26.10", "@size-limit/preset-small-lib": "^7.0.8", "@types/detect-node": "^2.0.2", "@types/lodash.defaultsdeep": "^4.6.9", "@types/lodash.flatmap": "^4.5.6", "abort-controller": "^3.0.0", "babel-plugin-annotate-pure-calls": "^0.4.0", "jest": "^26.6.3", "jsdom": "^19.0.0", "jsdom-global": "^3.0.2", "lerna-alias": "3.0.3-0", "rollup": "^3.29.5", "rollup-plugin-typescript2": "^0.31.0", "size-limit": "^7.0.8", "ts-jest": "^26.4.0", "typescript": "^4.9.5"}, "dependencies": {"@apimatic/convert-to-stream": "^0.1.6", "@apimatic/core-interfaces": "^0.2.11", "@apimatic/file-wrapper": "^0.3.6", "@apimatic/http-headers": "^0.3.6", "@apimatic/http-query": "^0.3.6", "@apimatic/json-bigint": "^1.2.0", "@apimatic/schema": "^0.7.17", "detect-browser": "^5.3.0", "detect-node": "^2.1.0", "form-data": "^4.0.1", "lodash.defaultsdeep": "^4.6.1", "lodash.flatmap": "^4.5.0", "tiny-warning": "^1.0.3", "tslib": "^2.8.1"}, "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "**************:apimatic/apimatic-js-runtime.git", "directory": "packages/core"}, "gitHead": "c4a8899936e2d855c0a0a05733eea872d29d3749"}