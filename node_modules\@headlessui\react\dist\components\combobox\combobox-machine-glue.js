import{createContext as r,useContext as a,useMemo as m}from"react";import{useOnUnmount as c}from'../../hooks/use-on-unmount.js';import{ComboboxMachine as i}from'./combobox-machine.js';const u=r(null);function p(n){let o=a(u);if(o===null){let e=new Error(`<${n} /> is missing a parent <Combobox /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(e,b),e}return o}function b({id:n,virtual:o=null,__demoMode:e=!1}){let t=m(()=>i.new({id:n,virtual:o,__demoMode:e}),[]);return c(()=>t.dispose()),t}export{u as ComboboxContext,b as useComboboxMachine,p as useComboboxMachineContext};
