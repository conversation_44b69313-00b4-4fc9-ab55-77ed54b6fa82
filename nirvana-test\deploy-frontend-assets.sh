#!/bin/bash
# Make script executable: chmod +x deploy-frontend-assets.sh

# Nirvana Organics - Frontend Assets Deployment Script
# Deploys both main and admin frontend builds to test environment

set -e

echo "🚀 Nirvana Organics - Frontend Assets Deployment"
echo "================================================"

# Color codes
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}✅ $1${NC}"
}

warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if running as correct user
if [ "$USER" != "Nirvana" ]; then
    error "This script must be run as the 'Nirvana' user"
    echo "Please run: sudo -u Nirvana $0"
    exit 1
fi

# Get current directory (should be nirvana-test)
CURRENT_DIR=$(pwd)
log "Current directory: $CURRENT_DIR"

# Check if we're in the right directory
if [[ ! "$CURRENT_DIR" =~ nirvana-test$ ]]; then
    error "This script should be run from the nirvana-test directory"
    echo "Please navigate to the nirvana-test directory first"
    exit 1
fi

# Step 1: Verify source build directories exist
log "Step 1: Verifying source build directories..."

if [ ! -d "dist" ]; then
    error "Main frontend build directory 'dist' not found"
    echo "Please run: npm run build:frontend:test"
    exit 1
fi

if [ ! -d "dist-admin" ]; then
    error "Admin frontend build directory 'dist-admin' not found"
    echo "Please run: npm run build:admin:test"
    exit 1
fi

# Check for specific asset files
if [ ! -f "dist-admin/assets/admin-DitisM-I.js" ]; then
    error "Admin JavaScript assets not found in dist-admin/assets/"
    echo "Please rebuild admin frontend: npm run build:admin:test"
    exit 1
fi

if [ ! -f "dist-admin/assets/admin-TY7ZtfqV.css" ]; then
    error "Admin CSS assets not found in dist-admin/assets/"
    echo "Please rebuild admin frontend: npm run build:admin:test"
    exit 1
fi

success "Source build directories verified"

# Step 2: Create target deployment directories
log "Step 2: Creating target deployment directories..."

TARGET_MAIN="/var/www/nirvana-frontend-test/main"
TARGET_ADMIN="/var/www/nirvana-frontend-test/admin"

# Create directories with proper permissions
sudo mkdir -p "$TARGET_MAIN"
sudo mkdir -p "$TARGET_ADMIN"
sudo chown -R Nirvana:Nirvana /var/www/nirvana-frontend-test/

success "Target directories created"

# Step 3: Deploy main frontend
log "Step 3: Deploying main frontend..."

# Copy all main frontend files
cp -r dist/* "$TARGET_MAIN/"

# Verify main deployment
if [ -f "$TARGET_MAIN/index.html" ]; then
    success "Main frontend deployed successfully"
else
    error "Main frontend deployment failed"
    exit 1
fi

# Step 4: Deploy admin frontend
log "Step 4: Deploying admin frontend..."

# Copy all admin frontend files
cp -r dist-admin/* "$TARGET_ADMIN/"

# Verify admin deployment
if [ -f "$TARGET_ADMIN/admin.html" ]; then
    success "Admin frontend deployed successfully"
else
    error "Admin frontend deployment failed"
    exit 1
fi

# Step 5: Verify admin assets specifically
log "Step 5: Verifying admin assets deployment..."

if [ -f "$TARGET_ADMIN/assets/admin-DitisM-I.js" ]; then
    success "Admin JavaScript assets deployed"
else
    error "Admin JavaScript assets missing"
    exit 1
fi

if [ -f "$TARGET_ADMIN/assets/admin-TY7ZtfqV.css" ]; then
    success "Admin CSS assets deployed"
else
    error "Admin CSS assets missing"
    exit 1
fi

# Step 6: Set proper permissions
log "Step 6: Setting proper file permissions..."

# Set ownership
sudo chown -R Nirvana:Nirvana /var/www/nirvana-frontend-test/

# Set directory permissions (755)
find /var/www/nirvana-frontend-test/ -type d -exec chmod 755 {} \;

# Set file permissions (644)
find /var/www/nirvana-frontend-test/ -type f -exec chmod 644 {} \;

success "File permissions set correctly"

# Step 7: Verify deployment structure
log "Step 7: Verifying deployment structure..."

echo ""
echo "📁 Deployment Structure:"
echo "========================"
echo "Main Frontend:"
ls -la "$TARGET_MAIN/" | head -10

echo ""
echo "Admin Frontend:"
ls -la "$TARGET_ADMIN/" | head -10

echo ""
echo "Admin Assets:"
ls -la "$TARGET_ADMIN/assets/" 2>/dev/null || warning "Admin assets directory not found"

# Step 8: Test file accessibility
log "Step 8: Testing file accessibility..."

# Test main index.html
if [ -r "$TARGET_MAIN/index.html" ]; then
    success "Main index.html is readable"
else
    error "Main index.html is not readable"
fi

# Test admin.html
if [ -r "$TARGET_ADMIN/admin.html" ]; then
    success "Admin admin.html is readable"
else
    error "Admin admin.html is not readable"
fi

# Test admin assets
if [ -r "$TARGET_ADMIN/assets/admin-DitisM-I.js" ]; then
    success "Admin JavaScript is readable"
else
    error "Admin JavaScript is not readable"
fi

if [ -r "$TARGET_ADMIN/assets/admin-TY7ZtfqV.css" ]; then
    success "Admin CSS is readable"
else
    error "Admin CSS is not readable"
fi

# Step 9: Display deployment summary
log "Step 9: Deployment completed!"

echo ""
echo "📋 DEPLOYMENT SUMMARY"
echo "===================="
success "✅ Main frontend deployed to: $TARGET_MAIN"
success "✅ Admin frontend deployed to: $TARGET_ADMIN"
success "✅ Admin assets available at: $TARGET_ADMIN/assets/"
success "✅ File permissions set correctly"

echo ""
echo "🔍 VERIFICATION COMMANDS"
echo "======================="
echo "Test main frontend:  curl -I https://test.shopnirvanaorganics.com/"
echo "Test admin frontend: curl -I https://test.shopnirvanaorganics.com/admin/"
echo "Test admin JS:       curl -I https://test.shopnirvanaorganics.com/admin/assets/admin-DitisM-I.js"
echo "Test admin CSS:      curl -I https://test.shopnirvanaorganics.com/admin/assets/admin-TY7ZtfqV.css"

echo ""
echo "📝 NEXT STEPS"
echo "============="
echo "1. Test the admin panel in browser: https://test.shopnirvanaorganics.com/admin/"
echo "2. Check browser console for any remaining 404 errors"
echo "3. Verify all admin functionality works correctly"
echo "4. Monitor server logs for any issues"

echo ""
success "🎉 Frontend assets deployment completed successfully!"
