# APIMatic Schema Libary for JavaScript

> This library is currently in preview.

Provides utilities for creating schema for different types, along with serialization information.

These schema can later be used to:

1. Validate data against schema.
2. Prepare data for serialization to JSON or XML.
3. Convert deserialized JSON or XML data to the schema type.
3. Provide strong-typing in TypeScript for untrusted/untyped data.

This library is used by JavaScript SDKs generated by the [APIMatic Code Generator](http://www.apimatic.io).

## Builds

The following environments are supported:

1. Node.js v10+
1. Bundlers like Rollup or Webpack
1. Web browsers

To support multiple environments, we export various builds:

| Environment | Usage |
| --- | --- |
| Common.js | Import like this: `require('@apimatic/schema')`. |
| ES Module | Import like this: `import { /* your imports */ } from '@apimatic/schema'`. |
| Browsers | *Use script: `https://unpkg.com/@apimatic/schema@VERSION/umd/schema.js` |
| Modern Browsers (supports ESM and uses modern JS) | *Use script: `https://unpkg.com/@apimatic/schema@VERSION/umd/schema.esm.js` |

_* Don't forget to replace VERSION with the version number._

**Note**: We discourage importing files or modules directly from the package. These are likely to change in the future and should not be considered stable.
