# Nirvana Organics - Test Environment Deployment

This folder contains the complete test deployment package for Nirvana Organics e-commerce platform.

## 📁 Folder Contents

```
nirvana-test/
├── server/                          # Complete backend application
│   ├── index.js                     # Main server entry point
│   ├── admin-server.js              # Admin server entry point
│   ├── config/                      # Configuration files
│   ├── controllers/                 # API controllers
│   ├── middleware/                  # Express middleware
│   ├── models/                      # Database models
│   ├── routes/                      # API routes
│   ├── services/                    # Business logic services
│   ├── utils/                       # Utility functions
│   ├── migrations/                  # Database migrations
│   ├── seeders/                     # Database seeders
│   └── templates/                   # Email templates
├── dist/                            # Main frontend build (test)
├── dist-admin/                      # Admin frontend build (test)
├── public/                          # Static assets and uploads
├── .env.test                        # Main server environment config
├── .env.admin.test                  # Admin server environment config
├── ecosystem.config.js              # PM2 configuration
├── nginx-site.conf                  # Nginx web server configuration
├── package.json                     # Node.js dependencies
├── package-lock.json               # Dependency lock file
├── deploy.sh                        # Backend deployment script
├── deploy-nginx.sh                  # Nginx deployment script
├── diagnose-pm2-env.sh             # Environment diagnostic script
├── fix-admin-health-endpoint.sh    # Admin health endpoint fix
├── fix-pm2-env-loading.sh          # PM2 environment loading fix
├── set-permissions.sh              # File permissions management
├── verify-fix.sh                   # Deployment verification script
├── COMPREHENSIVE_TROUBLESHOOTING_GUIDE.md  # Troubleshooting guide
└── README.md                        # This file
```

## 🚀 Deployment Instructions

### Prerequisites

1. **Server Requirements:**
   - Ubuntu 20.04+ or CentOS 8+
   - Node.js 18+ and npm 9+
   - PM2 process manager (`npm install -g pm2`)
   - Nginx web server (`sudo apt install nginx`)
   - SSL certificate for test.shopnirvanaorganics.com (Let's Encrypt recommended)

2. **Database:**
   - MySQL 8.0+ or MariaDB 10.6+
   - Database and user credentials configured
   - Database accessible from server (srv1921.hstgr.io:3306)

3. **Domain Configuration:**
   - `test.shopnirvanaorganics.com` → Main test site
   - `test.shopnirvanaorganics.com/admin` → Admin panel (path-based routing)
   - DNS A record pointing to your server IP

4. **Frontend Deployment:**
   - Frontend builds should be deployed to `/var/www/nirvana-frontend-test/`
   - Main frontend: `/var/www/nirvana-frontend-test/main/`
   - Admin frontend: `/var/www/nirvana-frontend-test/admin/`

### Step 1: Configure Environment Variables

**Edit `.env.test` (if needed):**
```bash
# Most values are pre-configured for test environment
# Update database credentials if different:
DB_HOST=srv1921.hstgr.io
DB_NAME=u106832845_nirvana
DB_USER=u106832845_root
DB_PASSWORD=Hrishikesh@0912

# Update API keys for test environment:
SQUARE_ACCESS_TOKEN=your-test-square-access-token
GOOGLE_CLIENT_SECRET=your-test-google-client-secret
```

**Edit `.env.admin.test` (if needed):**
```bash
# Most values are pre-configured for test environment
# Update database credentials if different:
DB_HOST=srv1921.hstgr.io
DB_NAME=u106832845_nirvana
DB_USER=u106832845_root
DB_PASSWORD=Hrishikesh@0912
```

### Step 2: Upload to Server

```bash
# Upload the entire nirvana-test folder to your server
scp -r nirvana-test/ user@your-server:/tmp/

# Or use rsync for better performance
rsync -avz --progress nirvana-test/ user@your-server:/tmp/nirvana-test/
```

### Step 3: Deploy Backend Services

```bash
# SSH into your server
ssh user@your-server

# Navigate to deployment folder
cd /tmp/nirvana-test

# Make deployment script executable
chmod +x deploy.sh

# Run backend deployment (requires sudo access)
sudo ./deploy.sh
```

### Step 4: Deploy Nginx Configuration

```bash
# Make Nginx deployment script executable
chmod +x deploy-nginx.sh

# Deploy Nginx configuration (requires sudo access)
sudo ./deploy-nginx.sh
```

The `deploy-nginx.sh` script will:
- Copy the Nginx configuration to `/etc/nginx/sites-available/`
- Enable the site by creating a symbolic link
- Test the Nginx configuration
- Reload Nginx to apply changes

### Step 5: Verify Deployment

```bash
# Check PM2 processes
pm2 status

# Check Nginx status
sudo systemctl status nginx

# Test the deployment
./verify-fix.sh
```
## 🔍 Verification

After deployment, verify everything is working:

```bash
# Check PM2 processes
sudo -u Nirvana pm2 status

# Check logs
sudo -u Nirvana pm2 logs

# Test health endpoints
curl https://test.shopnirvanaorganics.com/api/health
curl https://test.shopnirvanaorganics.com/admin/api/health

# Test frontend
curl -I https://test.shopnirvanaorganics.com
curl -I https://test.shopnirvanaorganics.com/admin

# Check Nginx status
sudo systemctl status nginx

# View Nginx logs
sudo tail -f /var/log/nginx/nirvana-test-main-*.log
```

## 🔧 Management Commands

### Service Management
```bash
# Restart services
sudo -u Nirvana pm2 restart nirvana-backend-main-test nirvana-backend-admin-test

# View logs
sudo -u Nirvana pm2 logs

# Monitor processes
sudo -u Nirvana pm2 monit

# Stop services
sudo -u Nirvana pm2 stop nirvana-backend-main-test nirvana-backend-admin-test

# Start services
sudo -u Nirvana pm2 start ecosystem.config.js

# Reload Nginx configuration
sudo systemctl reload nginx

# Restart Nginx
sudo systemctl restart nginx
```

### Database Management
The test environment includes a comprehensive database management script:

```bash
# Check database status and tables
node database-manager.js check

# Test database connection and functionality
node database-manager.js test

# Initialize database schema and run migrations
node database-manager.js init

# Seed basic data (roles, categories)
node database-manager.js seed

# Comprehensive database fix (recommended for issues)
node database-manager.js fix

# Debug environment variables and configuration
node database-manager.js debug

# Show help and available commands
node database-manager.js help
```

**Common Database Operations:**
```bash
# Fix all database issues (most common)
cd /var/www/nirvana-backend-test
NODE_ENV=test node database-manager.js fix

# Check if database is working properly
NODE_ENV=test node database-manager.js check

# Debug connection issues
NODE_ENV=test node database-manager.js debug
```

## 🧪 Testing Features

This test environment includes:

1. **Debug Mode:** Enhanced logging and error reporting
2. **Source Maps:** For easier debugging of frontend issues
3. **Test Database:** Separate from production data
4. **Sandbox APIs:** Square payments in sandbox mode
5. **Development Tools:** Additional debugging endpoints
6. **Path-based Admin:** Admin panel accessible at `/admin` path
7. **Relaxed Security:** More lenient rate limiting and CORS for testing

## 🌐 Nginx Configuration Features

The included `nginx-site.conf` provides:

1. **SSL/HTTPS Enforcement:** Automatic HTTP to HTTPS redirects
2. **Path-based Admin Routing:** Admin panel at `/admin` instead of subdomain
3. **API Reverse Proxy:** Backend API routing to ports 5000 and 3001
4. **Static File Serving:** Optimized serving of frontend assets
5. **Security Headers:** CSP, HSTS, X-Frame-Options for test environment
6. **Rate Limiting:** Appropriate limits for testing (more lenient than production)
7. **Gzip Compression:** Automatic compression for better performance
8. **Upload Security:** Prevents execution of dangerous file types
9. **Health Endpoints:** `/health` endpoint for monitoring
10. **Test Environment Headers:** `X-Environment: TEST` header for identification

## 🛡️ Security Notes

1. **Test Environment:** This is for testing only - not for production use
2. **Database:** Uses test database with sample data
3. **API Keys:** Uses sandbox/test API keys
4. **SSL/TLS:** Still requires HTTPS for proper testing
5. **Access:** Can be more permissive than production for testing

## 📞 Support

For deployment issues or questions, contact the development team.

**Deployment Paths:**
- Backend: `/var/www/nirvana-backend-test`
- Frontend: `/var/www/nirvana-frontend-test`
- Logs: `/var/log/pm2/` and `/var/log/deploy/`

## 🔄 Deployment Workflow

1. **Test First:** Always deploy to test environment first
2. **Validate:** Thoroughly test all functionality
3. **Production:** Only deploy to production after test validation
4. **Rollback:** Keep backups for quick rollback if needed
