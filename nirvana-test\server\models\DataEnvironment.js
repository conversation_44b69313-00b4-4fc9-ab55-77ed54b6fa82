const { DataTypes } = require('sequelize');
const { sequelize } = require('./database');

const DataEnvironment = sequelize.define('DataEnvironment', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  userId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    field: 'user_id',
    references: {
      model: 'users',
      key: 'id'
    }
  },
  mode: {
    type: DataTypes.ENUM('mock', 'real'),
    allowNull: false,
    defaultValue: 'real',
    comment: 'Data mode: mock for test data, real for production data'
  },
  sessionId: {
    type: DataTypes.STRING,
    allowNull: true,
    field: 'session_id',
    comment: 'Session identifier for tracking data mode per session'
  },
  isActive: {
    type: DataTypes.BOOLEAN,
    defaultValue: true,
    field: 'is_active'
  },
  lastSwitched: {
    type: DataTypes.DATE,
    allowNull: true,
    field: 'last_switched'
  },
  metadata: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: {},
    comment: 'Additional metadata about the data environment'
  }
}, {
  tableName: 'data_environments',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      unique: true,
      fields: ['user_id', 'session_id']
    },
    {
      fields: ['mode']
    },
    {
      fields: ['is_active']
    }
  ]
});

// Instance methods
DataEnvironment.prototype.switchMode = async function(newMode) {
  this.mode = newMode;
  this.lastSwitched = new Date();
  await this.save();
  return this;
};

DataEnvironment.prototype.isMockMode = function() {
  return this.mode === 'mock';
};

DataEnvironment.prototype.isRealMode = function() {
  return this.mode === 'real';
};

// Static methods
DataEnvironment.getCurrentMode = async function(userId, sessionId = null) {
  const environment = await this.findOne({
    where: {
      userId,
      sessionId,
      isActive: true
    },
    order: [['updatedAt', 'DESC']]
  });
  
  return environment ? environment.mode : 'real';
};

DataEnvironment.setMode = async function(userId, mode, sessionId = null) {
  const [environment, created] = await this.findOrCreate({
    where: {
      userId,
      sessionId
    },
    defaults: {
      mode,
      isActive: true,
      lastSwitched: new Date()
    }
  });

  if (!created) {
    environment.mode = mode;
    environment.lastSwitched = new Date();
    environment.isActive = true;
    await environment.save();
  }

  return environment;
};

DataEnvironment.clearUserSessions = async function(userId) {
  await this.update(
    { isActive: false },
    { where: { userId } }
  );
};

module.exports = DataEnvironment;
