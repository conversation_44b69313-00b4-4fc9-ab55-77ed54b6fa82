<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/Nirvana_logo.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    
    <!-- Admin-specific meta tags -->
    <title>Admin Panel - Nirvana Organics</title>
    <meta name="description" content="Nirvana Organics Admin Panel - Manage your e-commerce platform" />
    <meta name="robots" content="noindex, nofollow" />

    <!-- Note: Security headers are now handled by Nginx for better performance and security -->
    
    <!-- Favicon and app icons -->
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png">
    <link rel="manifest" href="/site.webmanifest">
    <link rel="mask-icon" href="/safari-pinned-tab.svg" color="#059669">
    <meta name="msapplication-TileColor" content="#059669">
    <meta name="theme-color" content="#059669">
    
    <!-- Admin-specific styling -->
    <style>
      /* Admin panel loading styles */
      .admin-loading {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #059669 0%, #047857 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 9999;
        color: white;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      }
      
      .admin-loading-content {
        text-align: center;
      }
      
      .admin-loading-spinner {
        width: 50px;
        height: 50px;
        border: 3px solid rgba(255, 255, 255, 0.3);
        border-top: 3px solid white;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin: 0 auto 20px;
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
      
      .admin-loading-text {
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 10px;
      }
      
      .admin-loading-subtext {
        font-size: 14px;
        opacity: 0.8;
      }
      
      /* Hide loading screen when app loads */
      .app-loaded .admin-loading {
        display: none;
      }
    </style>
    <script type="module" crossorigin src="/assets/admin-EiM8XHsg.js"></script>
    <link rel="stylesheet" crossorigin href="/assets/admin-TY7ZtfqV.css">
  </head>
  <body>
    <!-- Admin loading screen -->
    <div class="admin-loading" id="admin-loading">
      <div class="admin-loading-content">
        <div class="admin-loading-spinner"></div>
        <div class="admin-loading-text">Nirvana Organics</div>
        <div class="admin-loading-subtext">Loading Admin Panel...</div>
      </div>
    </div>
    
    <!-- Main app container -->
    <div id="root"></div>
    
    <!-- Admin-specific scripts -->
    <script>
      // Admin panel configuration
      window.__ADMIN_MODE__ = true;
      window.__APP_VERSION__ = '1.0.0';
      window.__BUILD_TIME__ = new Date().toISOString();
      
      // Remove loading screen when app loads
      window.addEventListener('load', function() {
        setTimeout(function() {
          document.body.classList.add('app-loaded');
        }, 500);
      });
      
      // Admin-specific error handling
      window.addEventListener('error', function(e) {
        console.error('Admin Panel Error:', e.error);
      });
      
      // Prevent right-click in production
      if (location.hostname !== 'localhost' && location.hostname !== '127.0.0.1') {
        document.addEventListener('contextmenu', function(e) {
          e.preventDefault();
        });
        
        // Prevent F12, Ctrl+Shift+I, Ctrl+U
        document.addEventListener('keydown', function(e) {
          if (e.key === 'F12' || 
              (e.ctrlKey && e.shiftKey && e.key === 'I') ||
              (e.ctrlKey && e.key === 'u')) {
            e.preventDefault();
          }
        });
      }
    </script>
    
    <!-- Load admin app -->
  </body>
</html>
