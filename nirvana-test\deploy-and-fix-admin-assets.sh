#!/bin/bash
# Make script executable: chmod +x deploy-and-fix-admin-assets.sh

# Nirvana Organics - Deploy and Fix Admin Assets
# Complete solution for admin 404 asset errors

set -e

echo "🚀 Nirvana Organics - Deploy and Fix Admin Assets"
echo "================================================="

# Color codes
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}✅ $1${NC}"
}

warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if running as correct user
if [ "$USER" != "Nirvana" ]; then
    error "This script must be run as the 'Nirvana' user"
    echo "Please run: sudo -u Nirvana $0"
    exit 1
fi

# Step 1: Deploy admin assets using existing script
log "Step 1: Running comprehensive admin assets fix..."

if [ -f "fix-admin-assets-comprehensive.sh" ]; then
    chmod +x fix-admin-assets-comprehensive.sh
    ./fix-admin-assets-comprehensive.sh
    
    if [ $? -eq 0 ]; then
        success "Admin assets deployment completed"
    else
        error "Admin assets deployment failed"
        exit 1
    fi
else
    error "fix-admin-assets-comprehensive.sh not found"
    exit 1
fi

# Step 2: Update Nginx configuration
log "Step 2: Updating Nginx configuration..."

NGINX_CONFIG="nginx-site.conf"
NGINX_TARGET="/etc/nginx/sites-available/nirvana-organics-test"

if [ -f "$NGINX_CONFIG" ]; then
    # Check if the updated configuration has the admin assets block
    if grep -q "location /admin/assets/" "$NGINX_CONFIG"; then
        success "Nginx configuration includes admin assets handling"
        
        # Copy to Nginx sites-available
        sudo cp "$NGINX_CONFIG" "$NGINX_TARGET"
        success "Nginx configuration updated"
        
        # Test Nginx configuration
        if sudo nginx -t; then
            success "Nginx configuration test passed"
            
            # Reload Nginx
            sudo systemctl reload nginx
            success "Nginx reloaded successfully"
        else
            error "Nginx configuration test failed"
            echo "Please check the configuration manually"
            exit 1
        fi
    else
        warning "Nginx configuration may not properly handle admin assets"
        echo "The configuration should include a specific location block for /admin/assets/"
    fi
else
    error "Nginx configuration file not found: $NGINX_CONFIG"
    exit 1
fi

# Step 3: Final verification
log "Step 3: Final verification of admin assets..."

echo "🌐 Testing Asset URLs:"
echo "====================="

# Test the specific assets that were causing 404 errors
MISSING_JS="admin-DitisM-I.js"
MISSING_CSS="admin-TY7ZtfqV.css"

# Test JavaScript asset
echo "Testing JavaScript asset:"
JS_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "https://test.shopnirvanaorganics.com/admin/assets/$MISSING_JS" 2>/dev/null || echo "000")
if [ "$JS_STATUS" = "200" ]; then
    success "JavaScript asset accessible (HTTP $JS_STATUS)"
else
    error "JavaScript asset still not accessible (HTTP $JS_STATUS)"
fi

# Test CSS asset
echo "Testing CSS asset:"
CSS_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "https://test.shopnirvanaorganics.com/admin/assets/$MISSING_CSS" 2>/dev/null || echo "000")
if [ "$CSS_STATUS" = "200" ]; then
    success "CSS asset accessible (HTTP $CSS_STATUS)"
else
    error "CSS asset still not accessible (HTTP $CSS_STATUS)"
fi

# Test admin panel itself
echo "Testing admin panel:"
ADMIN_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "https://test.shopnirvanaorganics.com/admin/" 2>/dev/null || echo "000")
if [ "$ADMIN_STATUS" = "200" ]; then
    success "Admin panel accessible (HTTP $ADMIN_STATUS)"
else
    error "Admin panel not accessible (HTTP $ADMIN_STATUS)"
fi

# Step 4: Summary and next steps
echo ""
log "Step 4: Deployment and fix completed!"

echo ""
echo "📋 FINAL SUMMARY"
echo "================"

if [ "$JS_STATUS" = "200" ] && [ "$CSS_STATUS" = "200" ] && [ "$ADMIN_STATUS" = "200" ]; then
    success "🎉 All admin assets are now working correctly!"
    success "✅ 404 errors should be completely resolved"
    success "✅ Admin panel should load with full styling and functionality"
    
    echo ""
    echo "🔍 VERIFICATION COMPLETED"
    echo "========================="
    echo "✅ Admin HTML: HTTP $ADMIN_STATUS"
    echo "✅ JavaScript: HTTP $JS_STATUS"
    echo "✅ CSS: HTTP $CSS_STATUS"
    
else
    warning "⚠️  Some issues may still exist"
    echo ""
    echo "📊 CURRENT STATUS"
    echo "================="
    echo "Admin HTML: HTTP $ADMIN_STATUS"
    echo "JavaScript: HTTP $JS_STATUS"
    echo "CSS: HTTP $CSS_STATUS"
    
    echo ""
    echo "🔧 ADDITIONAL TROUBLESHOOTING"
    echo "============================="
    echo "If assets are still not accessible, check:"
    echo "1. File permissions: ls -la /var/www/nirvana-frontend-test/admin/assets/"
    echo "2. Nginx error logs: sudo tail -f /var/log/nginx/nirvana-test-main-error.log"
    echo "3. Admin server logs: sudo -u Nirvana pm2 logs nirvana-backend-admin-test"
fi

echo ""
echo "🌐 TEST URLS"
echo "============"
echo "Admin Panel: https://test.shopnirvanaorganics.com/admin/"
echo "JavaScript:  https://test.shopnirvanaorganics.com/admin/assets/$MISSING_JS"
echo "CSS:         https://test.shopnirvanaorganics.com/admin/assets/$MISSING_CSS"

echo ""
echo "📝 NEXT STEPS"
echo "============="
echo "1. Open the admin panel in your browser"
echo "2. Check browser console - 404 errors should be gone"
echo "3. Verify admin panel styling and functionality"
echo "4. Test admin operations to ensure everything works"

echo ""
success "🎉 Admin assets deployment and fix completed!"
