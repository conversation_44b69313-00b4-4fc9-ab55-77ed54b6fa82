#!/bin/bash

# Set proper file permissions for Nirvana Organics Production Environment
echo "🔒 Setting file permissions for PRODUCTION environment..."
echo "======================================================="

DEPLOY_DIR="/var/www/nirvana-backend-production"
DEPLOY_USER="Nirvana"

# Set ownership for all files
echo "Setting ownership to $DEPLOY_USER:$DEPLOY_USER..."
sudo chown -R $DEPLOY_USER:$DEPLOY_USER $DEPLOY_DIR/

# Set permissions for environment files (600 - read/write for owner only)
echo "Setting environment file permissions (600)..."
sudo chmod 600 $DEPLOY_DIR/.env.production
sudo chmod 600 $DEPLOY_DIR/.env.admin

# Set permissions for scripts (755 - executable)
echo "Setting script permissions (755)..."
sudo chmod 755 $DEPLOY_DIR/fix-pm2-env-loading.sh
sudo chmod 755 $DEPLOY_DIR/diagnose-pm2-env.sh
sudo chmod 755 $DEPLOY_DIR/verify-fix.sh
sudo chmod 755 $DEPLOY_DIR/fix-admin-health-endpoint.sh
sudo chmod 755 $DEPLOY_DIR/set-permissions.sh

# Set permissions for PM2 ecosystem config (644)
echo "Setting ecosystem config permissions (644)..."
sudo chmod 644 $DEPLOY_DIR/ecosystem.config.js

# Set permissions for server files (644)
echo "Setting server file permissions (644)..."
sudo chmod 644 $DEPLOY_DIR/server/index.js
sudo chmod 644 $DEPLOY_DIR/server/admin-server.js

# Set permissions for documentation (644)
echo "Setting documentation permissions (644)..."
sudo chmod 644 $DEPLOY_DIR/COMPREHENSIVE_TROUBLESHOOTING_GUIDE.md

echo "✅ File permissions set successfully for PRODUCTION environment!"
echo ""
echo "Summary:"
echo "- Environment files (.env.*): 600 (owner read/write only)"
echo "- Scripts (*.sh): 755 (executable)"
echo "- Config files: 644 (owner read/write, group/others read)"
echo "- All files owned by: $DEPLOY_USER:$DEPLOY_USER"
