const cacheService = require('../services/cacheService');

/**
 * Cache middleware for different types of data
 */

/**
 * Generic cache middleware
 */
const cache = (ttl = 3600) => {
  return cacheService.middleware(ttl);
};

/**
 * Cache for product data (30 minutes)
 */
const cacheProducts = () => {
  return cache(1800); // 30 minutes
};

/**
 * Cache for category data (1 hour)
 */
const cacheCategories = () => {
  return cache(3600); // 1 hour
};

/**
 * Cache for dashboard stats (5 minutes)
 */
const cacheDashboard = () => {
  return cache(300); // 5 minutes
};

/**
 * Cache for user profile data (15 minutes)
 */
const cacheProfile = () => {
  return cache(900); // 15 minutes
};

/**
 * Cache for analytics data (10 minutes)
 */
const cacheAnalytics = () => {
  return cache(600); // 10 minutes
};

/**
 * Cache invalidation middleware
 * Clears cache patterns when data is modified
 */
const invalidateCache = (patterns = []) => {
  return async (req, res, next) => {
    // Store original res.json
    const originalJson = res.json;
    
    // Override res.json to invalidate cache after successful response
    res.json = async (data) => {
      // Call original res.json first
      const result = originalJson.call(res, data);
      
      // If response was successful, invalidate cache
      if (res.statusCode >= 200 && res.statusCode < 300) {
        try {
          for (const pattern of patterns) {
            await cacheService.delPattern(pattern);
          }
        } catch (error) {
          console.error('Cache invalidation error:', error);
        }
      }
      
      return result;
    };

    next();
  };
};

/**
 * Invalidate product-related cache
 */
const invalidateProductCache = () => {
  return invalidateCache([
    'cache:GET:/api/products*',
    'cache:GET:/api/admin/products*',
    'cache:GET:/api/categories*'
  ]);
};

/**
 * Invalidate category-related cache
 */
const invalidateCategoryCache = () => {
  return invalidateCache([
    'cache:GET:/api/categories*',
    'cache:GET:/api/products*',
    'cache:GET:/api/admin/categories*'
  ]);
};

/**
 * Invalidate dashboard cache
 */
const invalidateDashboardCache = () => {
  return invalidateCache([
    'cache:GET:/api/admin/dashboard*',
    'cache:GET:/api/admin/analytics*'
  ]);
};

/**
 * Invalidate user-related cache
 */
const invalidateUserCache = (userId = null) => {
  const patterns = [
    'cache:GET:/api/auth/profile*',
    'cache:GET:/api/admin/users*'
  ];
  
  if (userId) {
    patterns.push(`cache:*user:${userId}*`);
  }
  
  return invalidateCache(patterns);
};

/**
 * Cache key generator utilities
 */
const generateCacheKey = {
  product: (productId) => `product:${productId}`,
  category: (categoryId) => `category:${categoryId}`,
  user: (userId) => `user:${userId}`,
  dashboard: (userId) => `dashboard:${userId}`,
  analytics: (type, period) => `analytics:${type}:${period}`,
  search: (query, filters) => `search:${query}:${JSON.stringify(filters)}`,
  cart: (userId) => `cart:${userId}`,
  wishlist: (userId) => `wishlist:${userId}`
};

/**
 * Conditional cache middleware
 * Only caches if certain conditions are met
 */
const conditionalCache = (condition, ttl = 3600) => {
  return async (req, res, next) => {
    if (typeof condition === 'function') {
      const shouldCache = await condition(req, res);
      if (shouldCache) {
        return cache(ttl)(req, res, next);
      }
    } else if (condition) {
      return cache(ttl)(req, res, next);
    }
    
    next();
  };
};

/**
 * Cache warming utilities
 */
const warmCache = {
  /**
   * Warm product cache
   */
  products: async () => {
    try {
      const { Product } = require('../models');
      const products = await Product.findAll({
        where: { isActive: true },
        limit: 50,
        order: [['createdAt', 'DESC']]
      });
      
      for (const product of products) {
        const key = generateCacheKey.product(product.id);
        await cacheService.set(key, product.toJSON(), 1800);
      }
      
      console.log(`✅ Warmed cache for ${products.length} products`);
    } catch (error) {
      console.error('Error warming product cache:', error);
    }
  },

  /**
   * Warm category cache
   */
  categories: async () => {
    try {
      const { Category } = require('../models');
      const categories = await Category.findAll({
        where: { isActive: true },
        order: [['sortOrder', 'ASC']]
      });
      
      for (const category of categories) {
        const key = generateCacheKey.category(category.id);
        await cacheService.set(key, category.toJSON(), 3600);
      }
      
      console.log(`✅ Warmed cache for ${categories.length} categories`);
    } catch (error) {
      console.error('Error warming category cache:', error);
    }
  }
};

/**
 * Cache health check
 */
const cacheHealthCheck = async () => {
  try {
    const stats = await cacheService.getStats();
    return {
      status: stats.connected ? 'healthy' : 'unhealthy',
      enabled: stats.enabled,
      connected: stats.connected,
      stats: stats
    };
  } catch (error) {
    return {
      status: 'unhealthy',
      enabled: false,
      connected: false,
      error: error.message
    };
  }
};

module.exports = {
  cache,
  cacheProducts,
  cacheCategories,
  cacheDashboard,
  cacheProfile,
  cacheAnalytics,
  invalidateCache,
  invalidateProductCache,
  invalidateCategoryCache,
  invalidateDashboardCache,
  invalidateUserCache,
  generateCacheKey,
  conditionalCache,
  warmCache,
  cacheHealthCheck,
  cacheService
};
