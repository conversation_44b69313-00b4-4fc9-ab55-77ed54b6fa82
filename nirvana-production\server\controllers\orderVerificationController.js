const { Order, User, OrderItem, Product } = require('../models');
const whatsappService = require('../services/whatsappService');
const uspsService = require('../services/uspsService');
const emailNotificationService = require('../services/emailNotificationService');
const realTimeService = require('../services/realTimeService');

/**
 * Get orders pending verification
 */
const getPendingVerificationOrders = async (req, res) => {
  try {
    const { page = 1, limit = 20 } = req.query;
    const offset = (parseInt(page) - 1) * parseInt(limit);

    const { count, rows: orders } = await Order.findAndCountAll({
      where: {
        status: ['pending', 'processing'],
        paymentStatus: 'paid'
      },
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'firstName', 'lastName', 'email', 'phone', 'membershipType']
        },
        {
          model: OrderItem,
          as: 'items',
          include: [
            {
              model: Product,
              as: 'product',
              attributes: ['name', 'sku', 'categoryId']
            }
          ]
        }
      ],
      order: [['createdAt', 'DESC']],
      offset,
      limit: parseInt(limit)
    });

    const enrichedOrders = await Promise.all(
      orders.map(async (order) => {
        const orderData = await realTimeService.enrichOrderData(order);
        return {
          ...orderData,
          verificationStatus: order.verificationStatus || 'pending',
          riskScore: calculateRiskScore(order),
          requiresManualReview: requiresManualReview(order)
        };
      })
    );

    res.json({
      success: true,
      data: {
        orders: enrichedOrders,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: count,
          pages: Math.ceil(count / parseInt(limit))
        }
      }
    });

  } catch (error) {
    console.error('Get pending verification orders error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch pending verification orders',
      error: error.message
    });
  }
};

/**
 * Verify customer details
 */
const verifyCustomerDetails = async (req, res) => {
  try {
    const { orderId } = req.params;
    const { verificationMethod, notes, approved } = req.body;

    const order = await Order.findByPk(orderId, {
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'firstName', 'lastName', 'email', 'phone']
        }
      ]
    });

    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'Order not found'
      });
    }

    // Update verification status
    const updateData = {
      verificationStatus: approved ? 'verified' : 'rejected',
      verificationMethod: verificationMethod,
      verificationNotes: notes,
      verifiedBy: req.user.id,
      verifiedAt: new Date()
    };

    if (approved) {
      updateData.status = 'processing';
    } else {
      updateData.status = 'cancelled';
    }

    await order.update(updateData);

    // Send notification to customer
    try {
      const orderData = await realTimeService.enrichOrderData(order);
      
      if (approved) {
        // Send verification success notification
        if (order.user?.phone) {
          await whatsappService.sendTextMessage(
            order.user.phone,
            `✅ Your order ${order.orderNumber} has been verified and is now being processed! We'll send you tracking information once it ships. Thank you for choosing Nirvana Organics! 🌿`
          );
        }
        
        await emailNotificationService.sendStatusUpdate(orderData, 'processing');
      } else {
        // Send verification rejection notification
        if (order.user?.phone) {
          await whatsappService.sendTextMessage(
            order.user.phone,
            `❌ We were unable to verify your order ${order.orderNumber}. Please contact our support team at ${process.env.EMAIL_SUPPORT || '<EMAIL>'} for assistance. We apologize for any inconvenience.`
          );
        }
        
        await emailNotificationService.sendStatusUpdate(orderData, 'cancelled');
      }
    } catch (notificationError) {
      console.error('Failed to send verification notification:', notificationError);
    }

    res.json({
      success: true,
      message: `Order ${approved ? 'verified' : 'rejected'} successfully`,
      data: { order }
    });

  } catch (error) {
    console.error('Verify customer details error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to verify customer details',
      error: error.message
    });
  }
};

/**
 * Contact customer for verification
 */
const contactCustomerForVerification = async (req, res) => {
  try {
    const { orderId } = req.params;
    const { method, message, phoneNumber } = req.body;

    const order = await Order.findByPk(orderId, {
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'firstName', 'lastName', 'email', 'phone']
        }
      ]
    });

    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'Order not found'
      });
    }

    let contactResult = { success: false };

    if (method === 'whatsapp') {
      const customerPhone = phoneNumber || order.user?.phone;
      if (customerPhone) {
        const verificationMessage = `🔍 Order Verification - Nirvana Organics

Hi ${order.user?.firstName || 'Customer'}! We need to verify some details for your order ${order.orderNumber}.

${message}

Please reply to this message or call us to complete the verification process.

Thank you for your patience! 🌿`;

        contactResult = await whatsappService.sendTextMessage(customerPhone, verificationMessage);
      }
    } else if (method === 'email') {
      const orderData = await realTimeService.enrichOrderData(order);
      contactResult = await emailNotificationService.sendCustomVerificationEmail(orderData, message);
    }

    if (contactResult.success) {
      // Log the contact attempt
      await order.update({
        verificationContactAttempts: (order.verificationContactAttempts || 0) + 1,
        lastVerificationContact: new Date(),
        verificationContactMethod: method
      });

      res.json({
        success: true,
        message: 'Customer contacted successfully',
        data: { contactResult }
      });
    } else {
      res.status(400).json({
        success: false,
        message: 'Failed to contact customer',
        error: contactResult.error
      });
    }

  } catch (error) {
    console.error('Contact customer error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to contact customer',
      error: error.message
    });
  }
};

/**
 * Generate shipping label
 */
const generateShippingLabel = async (req, res) => {
  try {
    const { orderId } = req.params;
    const { shippingMethod, weight, dimensions } = req.body;

    const order = await Order.findByPk(orderId);
    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'Order not found'
      });
    }

    // For now, we'll create a placeholder for USPS label generation
    // This would require USPS eVS account setup
    const labelResult = await uspsService.createShippingLabel({
      orderId: order.id,
      orderNumber: order.orderNumber,
      shippingAddress: {
        name: `${order.shippingFirstName} ${order.shippingLastName}`,
        street: order.shippingAddress,
        city: order.shippingCity,
        state: order.shippingState,
        zipCode: order.shippingZipCode,
        country: order.shippingCountry || 'US'
      },
      weight: weight || 1,
      dimensions: dimensions || { length: 12, width: 9, height: 3 },
      service: shippingMethod || 'PRIORITY'
    });

    if (labelResult.success) {
      await order.update({
        shippingLabelGenerated: true,
        shippingLabelUrl: labelResult.labelUrl,
        shippingLabelId: labelResult.labelId
      });
    }

    res.json({
      success: labelResult.success,
      message: labelResult.success ? 'Shipping label generated successfully' : 'Failed to generate shipping label',
      data: labelResult
    });

  } catch (error) {
    console.error('Generate shipping label error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to generate shipping label',
      error: error.message
    });
  }
};

/**
 * Calculate risk score for order
 */
function calculateRiskScore(order) {
  let score = 0;
  
  // High value orders
  if (parseFloat(order.total) > 500) score += 20;
  else if (parseFloat(order.total) > 200) score += 10;
  
  // New customer
  if (order.user?.membershipType === 'first-time') score += 15;
  
  // No phone number
  if (!order.user?.phone && !order.billingAddress?.phone) score += 10;
  
  // Different billing and shipping addresses
  if (order.billingAddress !== order.shippingAddress) score += 5;
  
  // Multiple items of same product (potential reseller)
  const itemQuantities = order.items?.map(item => item.quantity) || [];
  if (Math.max(...itemQuantities) > 5) score += 15;
  
  return Math.min(score, 100); // Cap at 100
}

/**
 * Check if order requires manual review
 */
function requiresManualReview(order) {
  const riskScore = calculateRiskScore(order);
  return riskScore > 30 || parseFloat(order.total) > 300;
}

module.exports = {
  getPendingVerificationOrders,
  verifyCustomerDetails,
  contactCustomerForVerification,
  generateShippingLabel
};
