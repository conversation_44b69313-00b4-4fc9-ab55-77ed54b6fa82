const jwt = require('jsonwebtoken');
const authService = require('../services/authService'); // Assuming authService exists and has getUserById

/**
 * Authentication middleware to verify JWT tokens
 * This middleware extracts a Bearer token from the Authorization header,
 * verifies it, and attaches the authenticated user object to `req.user`.
 */
const authenticate = async (req, res, next) => {
  try {
    // Get token from header
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        success: false,
        message: 'Access denied. No token provided.'
      });
    }

    const token = authHeader.substring(7); // Remove 'Bearer ' prefix

    // Verify token
    // jwt.verify will throw an error if the token is invalid or expired
    const decoded = jwt.verify(token, process.env.JWT_SECRET);

    // Get user from database using the decoded userId or id
    const userId = decoded.userId || decoded.id; // Support both common JWT payload formats
    const user = await authService.getUserById(userId);
    if (!user) {
      return res.status(401).json({
        success: false,
        message: 'Invalid token. User not found.'
      });
    }

    // Check if user account is active
    if (!user.isActive) {
      return res.status(401).json({
        success: false,
        message: 'Account is deactivated.'
      });
    }

    // Attach user object to the request for subsequent middleware/route handlers
    req.user = user;
    next(); // Pass control to the next middleware/route handler
  } catch (error) {
    // Handle specific JWT errors
    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({
        success: false,
        message: 'Invalid token.'
      });
    }

    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({
        success: false,
        message: 'Token expired.'
      });
    }

    // Catch any other authentication errors
    return res.status(500).json({
      success: false,
      message: 'Authentication error.',
      error: error.message
    });
  }
};

/**
 * Authorization middleware to check user roles (legacy support).
 * This middleware takes a list of allowed roles and checks if the authenticated user's role
 * matches any of them.
 */
const authorize = (...roles) => {
  return async (req, res, next) => {
    // Ensure user is authenticated before checking roles
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required.'
      });
    }

    try {
      // Fetch the user's role from the database (assuming a getRole method on the User model)
      const role = await req.user.getRole();
      if (!role) {
        return res.status(403).json({
          success: false,
          message: 'User role not found.'
        });
      }

      // Check if the user's role name is included in the allowed roles list
      if (!roles.includes(role.name)) {
        return res.status(403).json({
          success: false,
          message: 'Insufficient permissions.'
        });
      }

      next(); // User has required role, proceed
    } catch (error) {
      console.error('Authorization error:', error);
      return res.status(500).json({
        success: false,
        message: 'Authorization error.'
      });
    }
  };
};

/**
 * Permission-based authorization middleware.
 * This middleware checks if the authenticated user has a specific permission
 * (e.g., 'products.edit', 'users.view').
 * @param {string} category - Permission category (e.g., 'products', 'users', 'system')
 * @param {string} action - Permission action (e.g., 'view', 'create', 'edit', 'delete', 'accessSettings')
 */
const requirePermission = (category, action) => {
  return async (req, res, next) => {
    // Ensure user is authenticated before checking permissions
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required.'
      });
    }

    try {
      // Assuming req.user has a hasPermission method that checks against roles/permissions
      const hasPermission = await req.user.hasPermission(category, action);
      if (!hasPermission) {
        return res.status(403).json({
          success: false,
          message: `Insufficient permissions. Required: ${category}.${action}`
        });
      }

      next(); // User has required permission, proceed
    } catch (error) {
      console.error('Permission check error:', error);
      return res.status(500).json({
        success: false,
        message: 'Permission check failed.'
      });
    }
  };
};

/**
 * Optional authentication middleware.
 * This middleware attempts to authenticate the user but does not fail if no token
 * is provided or if the token is invalid/expired. It simply proceeds without `req.user`
 * if authentication fails.
 */
const optionalAuth = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return next(); // No token, proceed without authentication
    }

    const token = authHeader.substring(7);
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    const user = await authService.getUserById(decoded.id);

    if (user && user.isActive) {
      req.user = user; // Attach user if successfully authenticated and active
    }

    next(); // Proceed regardless of authentication success/failure
  } catch (error) {
    // Log the error but continue without authentication
    console.warn('Optional authentication failed:', error.message);
    next();
  }
};

/**
 * Age verification middleware.
 * Specifically designed for content requiring users to be 21 or older (e.g., cannabis products).
 */
const verifyAge = (req, res, next) => {
  if (!req.user) {
    return res.status(401).json({
      success: false,
      message: 'Authentication required for age verification.'
    });
  }

  if (!req.user.dateOfBirth) {
    return res.status(400).json({
      success: false,
      message: 'Date of birth required for age verification.'
    });
  }

  // Calculate age based on dateOfBirth
  const age = Math.floor((Date.now() - new Date(req.user.dateOfBirth)) / (365.25 * 24 * 60 * 60 * 1000));
  if (age < 21) {
    return res.status(403).json({
      success: false,
      message: 'You must be at least 21 years old to access this content.'
    });
  }

  next(); // User meets age requirement, proceed
};

/**
 * Convenience middleware: Requires 'admin' role.
 */
const requireAdmin = authorize('admin');

/**
 * Convenience middleware: Requires 'manager' or 'admin' role.
 */
const requireManagerOrAdmin = authorize('manager', 'admin');

/**
 * Convenience middleware: Requires 'customer', 'manager', or 'admin' role.
 */
const requireCustomerOrAdmin = authorize('customer', 'manager', 'admin');

/**
 * Specific permission middleware functions for common operations.
 * These leverage the generic `requirePermission` middleware.
 */
const requireProductManagement = requirePermission('products', 'edit'); // Can create, edit, delete products
const requireProductView = requirePermission('products', 'view'); // Can view products
const requireUserManagement = requirePermission('users', 'create'); // Can create, edit, delete users
const requireUserView = requirePermission('users', 'view'); // Can view user details
const requireOrderManagement = requirePermission('orders', 'manage'); // Can manage orders (view, update status)
const requireSystemAccess = requirePermission('system', 'accessSettings'); // Can access system-wide settings
const requireAuditAccess = requirePermission('system', 'viewAuditLogs'); // Can view audit logs

/**
 * Middleware to check if user can invite managers.
 */
const requireManagerInvitation = requirePermission('users', 'inviteManagers');

/**
 * Middleware to check if user can manage roles.
 */
const requireRoleManagement = requirePermission('system', 'manageRoles');

/**
 * Audit logging middleware for admin actions.
 * This middleware captures details about an action and logs it, typically after a successful response.
 * It overrides `res.json` to capture the response status and data.
 * @param {string} action - The specific action being performed (e.g., 'CREATE_PRODUCT', 'DELETE_USER')
 * @param {string} entityType - The type of entity being acted upon (e.g., 'Product', 'User', 'Order')
 */
const auditAdminAction = (action, entityType) => {
  return async (req, res, next) => {
    // Store audit info in request for later use by the overridden res.json
    req.auditInfo = {
      action,
      entityType,
      ipAddress: req.ip || req.connection.remoteAddress,
      userAgent: req.get('User-Agent'),
      sessionId: req.sessionID
    };

    // Override res.json to log audit after successful response
    const originalJson = res.json;
    res.json = function(data) {
      // Only log if the response was successful (status code < 400)
      if (data && data.success !== false && res.statusCode < 400) {
        // Log audit action asynchronously to avoid blocking the response
        setImmediate(async () => {
          try {
            // For now, just log to console as AuditLog model is not provided.
            // In a real application, this would save to a database.
            console.log('🔍 AUDIT LOG:', {
              userId: req.user?.id, // User performing the action
              action,
              entityType,
              // Attempt to get entity ID from response data or request parameters
              entityId: data.data?.id || req.params.id || req.params.userId,
              ipAddress: req.auditInfo.ipAddress,
              userAgent: req.auditInfo.userAgent,
              sessionId: req.auditInfo.sessionId,
              endpoint: req.originalUrl,
              method: req.method,
              responseStatus: res.statusCode,
              timestamp: new Date().toISOString()
            });
          } catch (error) {
            console.error('Audit logging error:', error);
          }
        });
      }
      // Call the original res.json to send the actual response
      return originalJson.call(this, data);
    };

    next(); // Proceed to the next middleware/route handler
  };
};

/**
 * Helper function to determine severity based on action.
 * This can be used for logging or alerting purposes.
 */
const getSeverityForAction = (action) => {
  const highSeverityActions = [
    'DELETE_PRODUCT', 'DELETE_USER', 'UPDATE_USER_ROLE',
    'INVITE_MANAGER', 'BULK_UPDATE_PRODUCTS', 'DELETE_BANNER'
  ];
  const mediumSeverityActions = [
    'CREATE_PRODUCT', 'UPDATE_PRODUCT', 'CREATE_USER',
    'UPDATE_CUSTOMER', 'SEND_PROMOTIONAL_EMAIL'
  ];

  if (highSeverityActions.includes(action)) return 'high';
  if (mediumSeverityActions.includes(action)) return 'medium';
  return 'low';
};

// Export all middleware and helper functions for use in routes
module.exports = {
  authenticateToken: authenticate, // Aliased for clarity, but 'authenticate' is also exported below
  authenticate, // Direct export of authenticate
  authorize,
  requirePermission,
  optionalAuth,
  verifyAge,
  requireAdmin,
  requireManagerOrAdmin,
  requireCustomerOrAdmin,
  requireProductManagement,
  requireProductView,
  requireUserManagement,
  requireUserView,
  requireOrderManagement,
  requireSystemAccess,
  requireAuditAccess,
  requireManagerInvitation,
  requireRoleManagement,
  auditAdminAction,
  getSeverityForAction
};
