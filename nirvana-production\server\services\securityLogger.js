const fs = require('fs').promises;
const path = require('path');

/**
 * Security Logger Service for Nirvana Organics
 * Handles logging of security-related events including password resets, login attempts, etc.
 */

class SecurityLogger {
  constructor() {
    this.logDir = path.join(__dirname, '../logs');
    this.securityLogFile = path.join(this.logDir, 'security.log');
    this.passwordResetLogFile = path.join(this.logDir, 'password-reset.log');
    this.auditLogFile = path.join(this.logDir, 'audit.log');
    
    // Ensure log directory exists
    this.ensureLogDirectory();
  }

  /**
   * Ensure log directory exists
   */
  async ensureLogDirectory() {
    try {
      await fs.mkdir(this.logDir, { recursive: true });
    } catch (error) {
      console.error('Failed to create log directory:', error);
    }
  }

  /**
   * Format log entry with timestamp and metadata
   */
  formatLogEntry(level, event, data) {
    const timestamp = new Date().toISOString();
    const logEntry = {
      timestamp,
      level,
      event,
      ...data
    };
    return JSON.stringify(logEntry) + '\n';
  }

  /**
   * Write log entry to file
   */
  async writeLog(filename, entry) {
    try {
      await fs.appendFile(filename, entry);
    } catch (error) {
      console.error(`Failed to write to log file ${filename}:`, error);
    }
  }

  /**
   * Log password reset request
   */
  async logPasswordResetRequest(data) {
    const logEntry = this.formatLogEntry('INFO', 'PASSWORD_RESET_REQUEST', {
      userId: data.userId,
      email: data.email,
      userRole: data.userRole,
      ipAddress: data.ipAddress,
      userAgent: data.userAgent,
      success: data.success,
      tokenGenerated: data.tokenGenerated,
      emailSent: data.emailSent
    });

    await this.writeLog(this.passwordResetLogFile, logEntry);
    await this.writeLog(this.securityLogFile, logEntry);

    // Also log to console in development
    if (process.env.NODE_ENV === 'development') {
      console.log(`[SECURITY] Password reset requested: ${data.email} (${data.userRole})`);
    }
  }

  /**
   * Log password reset completion
   */
  async logPasswordResetCompletion(data) {
    const logEntry = this.formatLogEntry('INFO', 'PASSWORD_RESET_COMPLETED', {
      userId: data.userId,
      email: data.email,
      userRole: data.userRole,
      ipAddress: data.ipAddress,
      userAgent: data.userAgent,
      tokenUsed: data.tokenUsed,
      success: data.success
    });

    await this.writeLog(this.passwordResetLogFile, logEntry);
    await this.writeLog(this.securityLogFile, logEntry);

    if (process.env.NODE_ENV === 'development') {
      console.log(`[SECURITY] Password reset completed: ${data.email} (${data.userRole})`);
    }
  }

  /**
   * Log failed password reset attempt
   */
  async logPasswordResetFailure(data) {
    const logEntry = this.formatLogEntry('WARNING', 'PASSWORD_RESET_FAILED', {
      email: data.email,
      token: data.token ? data.token.substring(0, 8) + '...' : null, // Log only first 8 chars
      reason: data.reason,
      ipAddress: data.ipAddress,
      userAgent: data.userAgent,
      timestamp: new Date().toISOString()
    });

    await this.writeLog(this.passwordResetLogFile, logEntry);
    await this.writeLog(this.securityLogFile, logEntry);

    if (process.env.NODE_ENV === 'development') {
      console.log(`[SECURITY] Password reset failed: ${data.email} - ${data.reason}`);
    }
  }

  /**
   * Log suspicious activity
   */
  async logSuspiciousActivity(data) {
    const logEntry = this.formatLogEntry('ALERT', 'SUSPICIOUS_ACTIVITY', {
      type: data.type,
      description: data.description,
      ipAddress: data.ipAddress,
      userAgent: data.userAgent,
      userId: data.userId,
      email: data.email,
      metadata: data.metadata
    });

    await this.writeLog(this.securityLogFile, logEntry);
    await this.writeLog(this.auditLogFile, logEntry);

    // Log suspicious activity to console always
    console.warn(`[SECURITY ALERT] ${data.type}: ${data.description} from ${data.ipAddress}`);
  }

  /**
   * Log login attempts
   */
  async logLoginAttempt(data) {
    const logEntry = this.formatLogEntry(data.success ? 'INFO' : 'WARNING', 'LOGIN_ATTEMPT', {
      email: data.email,
      success: data.success,
      reason: data.reason,
      ipAddress: data.ipAddress,
      userAgent: data.userAgent,
      userId: data.userId,
      userRole: data.userRole
    });

    await this.writeLog(this.securityLogFile, logEntry);

    if (!data.success && process.env.NODE_ENV === 'development') {
      console.log(`[SECURITY] Failed login attempt: ${data.email} - ${data.reason}`);
    }
  }

  /**
   * Log admin actions
   */
  async logAdminAction(data) {
    const logEntry = this.formatLogEntry('INFO', 'ADMIN_ACTION', {
      adminUserId: data.adminUserId,
      adminEmail: data.adminEmail,
      action: data.action,
      targetType: data.targetType,
      targetId: data.targetId,
      changes: data.changes,
      ipAddress: data.ipAddress,
      userAgent: data.userAgent
    });

    await this.writeLog(this.auditLogFile, logEntry);
    await this.writeLog(this.securityLogFile, logEntry);

    if (process.env.NODE_ENV === 'development') {
      console.log(`[AUDIT] Admin action: ${data.adminEmail} performed ${data.action} on ${data.targetType}`);
    }
  }

  /**
   * Log email delivery status
   */
  async logEmailDelivery(data) {
    const logEntry = this.formatLogEntry(data.success ? 'INFO' : 'ERROR', 'EMAIL_DELIVERY', {
      type: data.type,
      recipient: data.recipient,
      subject: data.subject,
      success: data.success,
      messageId: data.messageId,
      error: data.error,
      provider: data.provider
    });

    await this.writeLog(this.securityLogFile, logEntry);

    if (!data.success) {
      console.error(`[EMAIL] Failed to send ${data.type} email to ${data.recipient}: ${data.error}`);
    }
  }

  /**
   * Get recent security events
   */
  async getRecentSecurityEvents(hours = 24, eventType = null) {
    try {
      const data = await fs.readFile(this.securityLogFile, 'utf8');
      const lines = data.trim().split('\n').filter(line => line);
      const events = lines.map(line => {
        try {
          return JSON.parse(line);
        } catch {
          return null;
        }
      }).filter(event => event);

      const cutoffTime = new Date(Date.now() - (hours * 60 * 60 * 1000));
      
      return events.filter(event => {
        const eventTime = new Date(event.timestamp);
        const isRecent = eventTime > cutoffTime;
        const matchesType = !eventType || event.event === eventType;
        return isRecent && matchesType;
      });
    } catch (error) {
      console.error('Failed to read security log:', error);
      return [];
    }
  }

  /**
   * Get password reset statistics
   */
  async getPasswordResetStats(days = 7) {
    try {
      const events = await this.getRecentSecurityEvents(days * 24, 'PASSWORD_RESET_REQUEST');
      const completions = await this.getRecentSecurityEvents(days * 24, 'PASSWORD_RESET_COMPLETED');
      const failures = await this.getRecentSecurityEvents(days * 24, 'PASSWORD_RESET_FAILED');

      return {
        requests: events.length,
        completions: completions.length,
        failures: failures.length,
        successRate: events.length > 0 ? (completions.length / events.length * 100).toFixed(2) : 0,
        period: `${days} days`
      };
    } catch (error) {
      console.error('Failed to get password reset stats:', error);
      return null;
    }
  }

  /**
   * Detect suspicious patterns
   */
  async detectSuspiciousPatterns() {
    try {
      const recentEvents = await this.getRecentSecurityEvents(1); // Last hour
      const patterns = {
        multipleFailedResets: {},
        rapidResetRequests: {},
        suspiciousIPs: {}
      };

      recentEvents.forEach(event => {
        if (event.event === 'PASSWORD_RESET_FAILED') {
          const key = event.email || event.ipAddress;
          patterns.multipleFailedResets[key] = (patterns.multipleFailedResets[key] || 0) + 1;
        }

        if (event.event === 'PASSWORD_RESET_REQUEST') {
          const ip = event.ipAddress;
          patterns.rapidResetRequests[ip] = (patterns.rapidResetRequests[ip] || 0) + 1;
        }
      });

      // Flag suspicious activity
      const alerts = [];
      
      Object.entries(patterns.multipleFailedResets).forEach(([key, count]) => {
        if (count >= 5) {
          alerts.push({
            type: 'MULTIPLE_FAILED_RESETS',
            target: key,
            count,
            severity: 'HIGH'
          });
        }
      });

      Object.entries(patterns.rapidResetRequests).forEach(([ip, count]) => {
        if (count >= 10) {
          alerts.push({
            type: 'RAPID_RESET_REQUESTS',
            target: ip,
            count,
            severity: 'MEDIUM'
          });
        }
      });

      return alerts;
    } catch (error) {
      console.error('Failed to detect suspicious patterns:', error);
      return [];
    }
  }
}

// Create singleton instance
const securityLogger = new SecurityLogger();

module.exports = securityLogger;
