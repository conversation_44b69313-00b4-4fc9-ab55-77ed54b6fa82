{"name": "@apimatic/schema", "author": "APIMatic Ltd.", "version": "0.7.17", "license": "MIT", "sideEffects": false, "main": "lib/index.js", "module": "es/index.js", "types": "lib/index.d.ts", "files": ["umd", "lib/**/*.js", "lib/**/*.d.ts", "es/**/*.js", "es/**/*.d.ts", "src", "LICENSE.md"], "engines": {"node": ">=14.15.0 || >=16.0.0"}, "scripts": {"clean": "rimraf lib es umd tsconfig.tsbuildinfo", "test": "jest", "build": "npm run clean && tsc && rollup -c --bundleConfigAsCjs && npm run annotate:es", "annotate:es": "babel es --out-dir es --no-babelrc --plugins annotate-pure-calls", "preversion": "npm run test", "prepublishOnly": "npm run build", "size": "size-limit", "analyze": "size-limit --why", "lint": "tslint --project .", "lint:fix": "tslint --project . --fix", "check-style": "prettier --check \"{src,test}/**/*.ts\"", "check-style:fix": "prettier --write \"{src,test}/**/*.ts\""}, "size-limit": [{"path": "umd/schema.js", "limit": "5 KB"}, {"path": "umd/schema.esm.js", "limit": "5 KB"}], "devDependencies": {"@babel/cli": "^7.26.4", "@babel/core": "^7.26.10", "@rollup/plugin-terser": "^0.4.3", "@size-limit/preset-small-lib": "^7.0.8", "babel-plugin-annotate-pure-calls": "^0.4.0", "jest": "^26.6.3", "jsdom": "^19.0.0", "jsdom-global": "^3.0.2", "lerna-alias": "3.0.3-0", "rollup": "^3.29.5", "rollup-plugin-replace": "^2.2.0", "rollup-plugin-typescript2": "^0.31.0", "size-limit": "^7.0.8", "ts-jest": "^26.4.0", "typescript": "^4.9.5"}, "dependencies": {"tslib": "^2.8.1"}, "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "**************:apimatic/apimatic-js-runtime.git", "directory": "packages/schema"}, "gitHead": "25d6a0e48d0a7860b36c72b959577aaf49bfc02a"}