#!/bin/bash

# Nirvana Organics - Comprehensive Test Environment Deployment Script
# Handles complete deployment, configuration, and fixes for test.shopnirvanaorganics.com
# 
# Usage: sudo ./deploy.sh
# 
# This script combines all deployment functionality including:
# - Frontend builds (main and admin)
# - Backend deployment and configuration
# - Nginx configuration and deployment
# - Database schema fixes
# - Asset deployment and permissions
# - Service management and restarts
# - Comprehensive testing and verification

set -e  # Exit on any error

# =============================================================================
# CONFIGURATION
# =============================================================================

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
DEPLOY_USER="Nirvana"
BACKEND_PATH="/var/www/nirvana-backend-test"
FRONTEND_PATH="/var/www/nirvana-frontend-test"
BACKUP_PATH="/var/backups/nirvana-organics-test"
LOG_FILE="/var/log/deploy/test-$(date +%Y%m%d-%H%M%S).log"
DOMAIN="test.shopnirvanaorganics.com"

# Nginx configuration
NGINX_SITE_NAME="nirvana-organics-test"
NGINX_CONFIG_FILE="nginx-site.conf"
SITES_AVAILABLE="/etc/nginx/sites-available"
SITES_ENABLED="/etc/nginx/sites-enabled"

# Asset files to verify
ADMIN_JS="admin-DitisM-I.js"
ADMIN_CSS="admin-TY7ZtfqV.css"

# =============================================================================
# COLORS AND LOGGING
# =============================================================================

RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
NC='\033[0m'

log() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}❌ [ERROR]${NC} $1" | tee -a "$LOG_FILE"
    exit 1
}

success() {
    echo -e "${GREEN}✅ [SUCCESS]${NC} $1" | tee -a "$LOG_FILE"
}

warning() {
    echo -e "${YELLOW}⚠️  [WARNING]${NC} $1" | tee -a "$LOG_FILE"
}

info() {
    echo -e "${CYAN}ℹ️  [INFO]${NC} $1" | tee -a "$LOG_FILE"
}

step() {
    echo -e "\n${WHITE}🔧 $1${NC}" | tee -a "$LOG_FILE"
    echo -e "${WHITE}$(printf '=%.0s' {1..60})${NC}" | tee -a "$LOG_FILE"
}

# =============================================================================
# UTILITY FUNCTIONS
# =============================================================================

check_root() {
    if [[ $EUID -ne 0 ]]; then
        error "This script must be run as root (use sudo)"
    fi
}

check_user_exists() {
    if ! id "$DEPLOY_USER" &>/dev/null; then
        error "User '$DEPLOY_USER' does not exist"
    fi
}

create_directories() {
    log "Creating necessary directories..."
    
    # Create log directory
    mkdir -p /var/log/deploy
    
    # Create backup directory
    mkdir -p "$BACKUP_PATH"
    
    # Create frontend directories
    mkdir -p "$FRONTEND_PATH/main"
    mkdir -p "$FRONTEND_PATH/admin"
    
    # Set ownership
    chown -R $DEPLOY_USER:$DEPLOY_USER "$FRONTEND_PATH"
    chown -R $DEPLOY_USER:$DEPLOY_USER "$BACKUP_PATH"
    
    success "Directories created and configured"
}

backup_current_deployment() {
    log "Creating backup of current deployment..."
    
    BACKUP_DIR="$BACKUP_PATH/backup-$(date +%Y%m%d-%H%M%S)"
    mkdir -p "$BACKUP_DIR"
    
    # Backup frontend if exists
    if [ -d "$FRONTEND_PATH" ]; then
        cp -r "$FRONTEND_PATH" "$BACKUP_DIR/frontend" 2>/dev/null || true
    fi
    
    # Backup nginx config if exists
    if [ -f "$SITES_AVAILABLE/$NGINX_SITE_NAME" ]; then
        cp "$SITES_AVAILABLE/$NGINX_SITE_NAME" "$BACKUP_DIR/nginx-config.conf" 2>/dev/null || true
    fi
    
    success "Backup created at $BACKUP_DIR"
}

# =============================================================================
# MAIN DEPLOYMENT FUNCTIONS
# =============================================================================

build_frontend() {
    step "Building Frontend Applications"
    
    cd "$SCRIPT_DIR"
    
    # Check if we have the necessary files
    if [ ! -f "package.json" ]; then
        error "package.json not found in $SCRIPT_DIR"
    fi
    
    # Install dependencies as Nirvana user
    log "Installing dependencies..."
    sudo -u $DEPLOY_USER npm install
    
    # Build main frontend
    log "Building main frontend..."
    sudo -u $DEPLOY_USER npm run build:test
    
    if [ ! -d "dist" ]; then
        error "Main frontend build failed - dist directory not found"
    fi
    
    success "Main frontend built successfully"
    
    # Build admin frontend
    log "Building admin frontend..."
    sudo -u $DEPLOY_USER npm run build:admin:test
    
    if [ ! -d "dist-admin" ]; then
        error "Admin frontend build failed - dist-admin directory not found"
    fi
    
    success "Admin frontend built successfully"
}

deploy_frontend_assets() {
    step "Deploying Frontend Assets"
    
    cd "$SCRIPT_DIR"
    
    # Deploy main frontend
    log "Deploying main frontend assets..."
    cp -r dist/* "$FRONTEND_PATH/main/"
    
    # Deploy admin frontend
    log "Deploying admin frontend assets..."
    cp -r dist-admin/* "$FRONTEND_PATH/admin/"
    
    # Set proper permissions
    log "Setting frontend permissions..."
    chown -R $DEPLOY_USER:$DEPLOY_USER "$FRONTEND_PATH"
    find "$FRONTEND_PATH" -type d -exec chmod 755 {} \;
    find "$FRONTEND_PATH" -type f -exec chmod 644 {} \;
    
    # Verify critical files exist
    if [ ! -f "$FRONTEND_PATH/main/index.html" ]; then
        error "Main frontend deployment failed - index.html not found"
    fi
    
    if [ ! -f "$FRONTEND_PATH/admin/admin.html" ]; then
        error "Admin frontend deployment failed - admin.html not found"
    fi
    
    success "Frontend assets deployed successfully"
}

fix_database_schema() {
    step "Fixing Database Schema"
    
    cd "$SCRIPT_DIR"
    
    # Run the database fix script as Nirvana user
    log "Running database schema fixes..."
    
    if [ -f "fix-products-is-active-column.js" ]; then
        sudo -u $DEPLOY_USER node fix-products-is-active-column.js || warning "Database fix script encountered issues (may already be correct)"
    else
        warning "Database fix script not found - skipping database fixes"
    fi
    
    success "Database schema fixes completed"
}

deploy_nginx_config() {
    step "Deploying Nginx Configuration"
    
    cd "$SCRIPT_DIR"
    
    if [ ! -f "$NGINX_CONFIG_FILE" ]; then
        error "Nginx configuration file '$NGINX_CONFIG_FILE' not found"
    fi
    
    # Deploy configuration
    log "Deploying Nginx configuration..."
    cp "$NGINX_CONFIG_FILE" "$SITES_AVAILABLE/$NGINX_SITE_NAME"
    chmod 644 "$SITES_AVAILABLE/$NGINX_SITE_NAME"
    chown root:root "$SITES_AVAILABLE/$NGINX_SITE_NAME"
    
    # Enable site
    log "Enabling Nginx site..."
    if [ -L "$SITES_ENABLED/$NGINX_SITE_NAME" ]; then
        rm "$SITES_ENABLED/$NGINX_SITE_NAME"
    fi
    ln -s "$SITES_AVAILABLE/$NGINX_SITE_NAME" "$SITES_ENABLED/$NGINX_SITE_NAME"
    
    # Test configuration
    log "Testing Nginx configuration..."
    nginx -t || error "Nginx configuration test failed"
    
    # Reload Nginx
    log "Reloading Nginx..."
    systemctl reload nginx || error "Failed to reload Nginx"
    
    success "Nginx configuration deployed and reloaded"
}

manage_backend_services() {
    step "Managing Backend Services"
    
    cd "$BACKEND_PATH" || error "Backend path not found: $BACKEND_PATH"
    
    # Install/update backend dependencies
    log "Installing backend dependencies..."
    sudo -u $DEPLOY_USER npm install
    
    # Restart PM2 services
    log "Restarting backend services..."
    
    # Stop existing services
    sudo -u $DEPLOY_USER pm2 stop all 2>/dev/null || true
    
    # Start services using ecosystem config
    if [ -f "$SCRIPT_DIR/ecosystem.config.js" ]; then
        sudo -u $DEPLOY_USER pm2 start "$SCRIPT_DIR/ecosystem.config.js"
    else
        warning "Ecosystem config not found - starting services individually"
        sudo -u $DEPLOY_USER pm2 start server/index.js --name "nirvana-backend-main-test"
        sudo -u $DEPLOY_USER pm2 start server/admin-server.js --name "nirvana-backend-admin-test"
    fi
    
    # Save PM2 configuration
    sudo -u $DEPLOY_USER pm2 save
    
    # Wait for services to start
    sleep 5
    
    # Check service status
    if sudo -u $DEPLOY_USER pm2 list | grep -q "online"; then
        success "Backend services started successfully"
    else
        error "Backend services failed to start"
    fi
}

# =============================================================================
# TESTING AND VERIFICATION FUNCTIONS
# =============================================================================

test_http_endpoints() {
    step "Testing HTTP Endpoints"

    # Wait for services to be fully ready
    sleep 10

    # Test main frontend
    log "Testing main frontend..."
    MAIN_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "https://$DOMAIN/" 2>/dev/null || echo "000")
    if [ "$MAIN_STATUS" = "200" ]; then
        success "Main frontend accessible (HTTP $MAIN_STATUS)"
    else
        warning "Main frontend not accessible (HTTP $MAIN_STATUS)"
    fi

    # Test admin frontend
    log "Testing admin frontend..."
    ADMIN_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "https://$DOMAIN/admin/" 2>/dev/null || echo "000")
    if [ "$ADMIN_STATUS" = "200" ]; then
        success "Admin frontend accessible (HTTP $ADMIN_STATUS)"
    else
        warning "Admin frontend not accessible (HTTP $ADMIN_STATUS)"
    fi

    # Test admin JavaScript asset
    log "Testing admin JavaScript asset..."
    JS_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "https://$DOMAIN/admin/assets/$ADMIN_JS" 2>/dev/null || echo "000")
    if [ "$JS_STATUS" = "200" ]; then
        success "Admin JavaScript asset accessible (HTTP $JS_STATUS)"
    else
        warning "Admin JavaScript asset not accessible (HTTP $JS_STATUS)"
    fi

    # Test admin CSS asset
    log "Testing admin CSS asset..."
    CSS_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "https://$DOMAIN/admin/assets/$ADMIN_CSS" 2>/dev/null || echo "000")
    if [ "$CSS_STATUS" = "200" ]; then
        success "Admin CSS asset accessible (HTTP $CSS_STATUS)"
    else
        warning "Admin CSS asset not accessible (HTTP $CSS_STATUS)"
    fi

    # Test backend API endpoints
    log "Testing backend API endpoints..."
    API_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "https://$DOMAIN/api/health" 2>/dev/null || echo "000")
    if [ "$API_STATUS" = "200" ]; then
        success "Backend API accessible (HTTP $API_STATUS)"
    else
        warning "Backend API not accessible (HTTP $API_STATUS)"
    fi

    # Test admin API endpoints
    log "Testing admin API endpoints..."
    ADMIN_API_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "https://$DOMAIN/admin-api/health" 2>/dev/null || echo "000")
    if [ "$ADMIN_API_STATUS" = "200" ]; then
        success "Admin API accessible (HTTP $ADMIN_API_STATUS)"
    else
        warning "Admin API not accessible (HTTP $ADMIN_API_STATUS)"
    fi
}

test_database_connectivity() {
    step "Testing Database Connectivity"

    cd "$SCRIPT_DIR"

    # Test database connection and is_active column
    log "Testing database query with is_active column..."

    DB_TEST_RESULT=$(sudo -u $DEPLOY_USER node -e "
const { Sequelize } = require('sequelize');
require('dotenv').config({ path: './.env.test' });
const sequelize = new Sequelize(process.env.DB_NAME, process.env.DB_USER, process.env.DB_PASSWORD, {
  host: process.env.DB_HOST,
  dialect: 'mysql',
  logging: false
});
sequelize.query('SELECT COUNT(*) as count FROM products WHERE is_active = 1')
  .then(([results]) => console.log('SUCCESS:' + results[0].count))
  .catch(err => console.log('ERROR:' + err.message))
  .finally(() => sequelize.close());
" 2>/dev/null || echo "ERROR:Connection failed")

    if [[ "$DB_TEST_RESULT" == SUCCESS:* ]]; then
        PRODUCT_COUNT=$(echo "$DB_TEST_RESULT" | cut -d: -f2)
        success "Database query successful - found $PRODUCT_COUNT active products"
    else
        warning "Database query failed: $(echo "$DB_TEST_RESULT" | cut -d: -f2)"
    fi
}

verify_file_permissions() {
    step "Verifying File Permissions"

    # Check frontend directory permissions
    log "Checking frontend directory permissions..."

    if [ -d "$FRONTEND_PATH" ]; then
        FRONTEND_OWNER=$(stat -c '%U:%G' "$FRONTEND_PATH")
        if [ "$FRONTEND_OWNER" = "$DEPLOY_USER:$DEPLOY_USER" ]; then
            success "Frontend directory has correct ownership ($FRONTEND_OWNER)"
        else
            warning "Frontend directory ownership incorrect: $FRONTEND_OWNER (expected: $DEPLOY_USER:$DEPLOY_USER)"
        fi
    fi

    # Check critical files exist and have correct permissions
    CRITICAL_FILES=(
        "$FRONTEND_PATH/main/index.html"
        "$FRONTEND_PATH/admin/admin.html"
        "$FRONTEND_PATH/admin/assets/$ADMIN_JS"
        "$FRONTEND_PATH/admin/assets/$ADMIN_CSS"
    )

    for file in "${CRITICAL_FILES[@]}"; do
        if [ -f "$file" ]; then
            FILE_PERMS=$(stat -c '%a' "$file")
            if [ "$FILE_PERMS" = "644" ]; then
                success "$(basename "$file") has correct permissions ($FILE_PERMS)"
            else
                warning "$(basename "$file") has incorrect permissions: $FILE_PERMS (expected: 644)"
            fi
        else
            warning "Critical file missing: $(basename "$file")"
        fi
    done
}

generate_deployment_report() {
    step "Generating Deployment Report"

    REPORT_FILE="/var/log/deploy/deployment-report-$(date +%Y%m%d-%H%M%S).txt"

    {
        echo "Nirvana Organics Test Environment Deployment Report"
        echo "=================================================="
        echo "Deployment Date: $(date)"
        echo "Domain: $DOMAIN"
        echo "Script Version: Comprehensive Deploy v1.0"
        echo ""

        echo "DEPLOYMENT STATUS"
        echo "=================="
        echo "✅ Frontend builds completed"
        echo "✅ Assets deployed to $FRONTEND_PATH"
        echo "✅ Nginx configuration deployed"
        echo "✅ Database schema fixes applied"
        echo "✅ Backend services restarted"
        echo ""

        echo "HTTP ENDPOINT TESTS"
        echo "==================="
        echo "Main Frontend: HTTP $MAIN_STATUS"
        echo "Admin Frontend: HTTP $ADMIN_STATUS"
        echo "Admin JS Asset: HTTP $JS_STATUS"
        echo "Admin CSS Asset: HTTP $CSS_STATUS"
        echo "Backend API: HTTP $API_STATUS"
        echo "Admin API: HTTP $ADMIN_API_STATUS"
        echo ""

        echo "DATABASE CONNECTIVITY"
        echo "===================="
        echo "Database Test: $DB_TEST_RESULT"
        echo ""

        echo "VERIFICATION URLS"
        echo "================="
        echo "Main Site: https://$DOMAIN/"
        echo "Admin Panel: https://$DOMAIN/admin/"
        echo "Admin JS: https://$DOMAIN/admin/assets/$ADMIN_JS"
        echo "Admin CSS: https://$DOMAIN/admin/assets/$ADMIN_CSS"
        echo "API Health: https://$DOMAIN/api/health"
        echo "Admin API Health: https://$DOMAIN/admin-api/health"
        echo ""

        echo "LOG FILES"
        echo "========="
        echo "Deployment Log: $LOG_FILE"
        echo "Report File: $REPORT_FILE"

    } > "$REPORT_FILE"

    success "Deployment report generated: $REPORT_FILE"
}

# =============================================================================
# ERROR HANDLING AND ROLLBACK
# =============================================================================

rollback_deployment() {
    step "Rolling Back Deployment"

    warning "Deployment failed - attempting rollback..."

    # Find the most recent backup
    LATEST_BACKUP=$(find "$BACKUP_PATH" -name "backup-*" -type d | sort -r | head -n 1)

    if [ -n "$LATEST_BACKUP" ] && [ -d "$LATEST_BACKUP" ]; then
        log "Rolling back to: $LATEST_BACKUP"

        # Restore frontend if backup exists
        if [ -d "$LATEST_BACKUP/frontend" ]; then
            rm -rf "$FRONTEND_PATH"
            cp -r "$LATEST_BACKUP/frontend" "$FRONTEND_PATH"
            chown -R $DEPLOY_USER:$DEPLOY_USER "$FRONTEND_PATH"
            success "Frontend rolled back"
        fi

        # Restore nginx config if backup exists
        if [ -f "$LATEST_BACKUP/nginx-config.conf" ]; then
            cp "$LATEST_BACKUP/nginx-config.conf" "$SITES_AVAILABLE/$NGINX_SITE_NAME"
            nginx -t && systemctl reload nginx
            success "Nginx configuration rolled back"
        fi

        success "Rollback completed"
    else
        warning "No backup found for rollback"
    fi
}

# Trap errors and attempt rollback
trap 'rollback_deployment' ERR

# =============================================================================
# MAIN EXECUTION
# =============================================================================

main() {
    echo -e "${CYAN}"
    echo "╔══════════════════════════════════════════════════════════════════════════════╗"
    echo "║                    Nirvana Organics Test Deployment                         ║"
    echo "║                         Comprehensive Deploy Script                         ║"
    echo "╚══════════════════════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"

    log "🚀 Starting comprehensive test environment deployment"
    log "📅 Deployment Date: $(date)"
    log "🌐 Target Domain: $DOMAIN"
    log "📁 Script Directory: $SCRIPT_DIR"
    log "📝 Log File: $LOG_FILE"

    # Pre-flight checks
    step "Pre-flight Checks"
    check_root
    check_user_exists

    # Verify we're in the correct directory
    if [ ! -f "$SCRIPT_DIR/package.json" ] || [ ! -f "$SCRIPT_DIR/.env.test" ]; then
        error "Missing required files. Are you running this from the nirvana-test directory?"
    fi

    success "Pre-flight checks passed"

    # Create necessary directories and backup
    create_directories
    backup_current_deployment

    # Main deployment steps
    build_frontend
    deploy_frontend_assets
    fix_database_schema
    deploy_nginx_config
    manage_backend_services

    # Testing and verification
    test_http_endpoints
    test_database_connectivity
    verify_file_permissions

    # Generate final report
    generate_deployment_report

    # Final summary
    step "Deployment Summary"

    TOTAL_TESTS=6
    PASSED_TESTS=0

    # Count successful tests
    [ "$MAIN_STATUS" = "200" ] && ((PASSED_TESTS++))
    [ "$ADMIN_STATUS" = "200" ] && ((PASSED_TESTS++))
    [ "$JS_STATUS" = "200" ] && ((PASSED_TESTS++))
    [ "$CSS_STATUS" = "200" ] && ((PASSED_TESTS++))
    [ "$API_STATUS" = "200" ] && ((PASSED_TESTS++))
    [[ "$DB_TEST_RESULT" == SUCCESS:* ]] && ((PASSED_TESTS++))

    echo ""
    info "📊 Test Results: $PASSED_TESTS/$TOTAL_TESTS tests passed"

    if [ $PASSED_TESTS -eq $TOTAL_TESTS ]; then
        echo -e "${GREEN}"
        echo "╔══════════════════════════════════════════════════════════════════════════════╗"
        echo "║                          🎉 DEPLOYMENT SUCCESSFUL! 🎉                       ║"
        echo "╚══════════════════════════════════════════════════════════════════════════════╝"
        echo -e "${NC}"

        success "All systems operational!"
        success "Main site: https://$DOMAIN/"
        success "Admin panel: https://$DOMAIN/admin/"
        success "Admin assets are now accessible without 404 errors"
        success "Database schema issues have been resolved"

    elif [ $PASSED_TESTS -gt $((TOTAL_TESTS / 2)) ]; then
        echo -e "${YELLOW}"
        echo "╔══════════════════════════════════════════════════════════════════════════════╗"
        echo "║                      ⚠️  DEPLOYMENT PARTIALLY SUCCESSFUL ⚠️                  ║"
        echo "╚══════════════════════════════════════════════════════════════════════════════╝"
        echo -e "${NC}"

        warning "Some tests failed but core functionality is working"
        info "Check the deployment report for details: $REPORT_FILE"

    else
        echo -e "${RED}"
        echo "╔══════════════════════════════════════════════════════════════════════════════╗"
        echo "║                         ❌ DEPLOYMENT FAILED ❌                              ║"
        echo "╚══════════════════════════════════════════════════════════════════════════════╝"
        echo -e "${NC}"

        error "Multiple critical tests failed - manual intervention required"
    fi

    echo ""
    info "📋 Next Steps:"
    info "1. Review the deployment report: $REPORT_FILE"
    info "2. Check the deployment log: $LOG_FILE"
    info "3. Test the admin panel functionality manually"
    info "4. Monitor backend services: sudo -u $DEPLOY_USER pm2 status"

    log "🏁 Deployment process completed"
}

# Execute main function if script is run directly
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
