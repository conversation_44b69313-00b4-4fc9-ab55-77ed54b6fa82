const express = require('express');
const router = express.Router();
const { authenticate, requireAdmin } = require('../middleware/auth');
const adminShopFinderController = require('../controllers/adminShopFinderController');

// All routes require admin authentication
router.use(authenticate);
router.use(requireAdmin);

// @route   GET /api/admin/shop-finder/settings
// @desc    Get admin settings for shop finder
// @access  Private (Admin)
router.get('/settings', adminShopFinderController.getAdminSettings);

// @route   PUT /api/admin/shop-finder/settings
// @desc    Update admin settings for shop finder
// @access  Private (Admin)
router.put('/settings', adminShopFinderController.updateAdminSettings);

// @route   POST /api/admin/shop-finder/locations/:locationId/sync
// @desc    Sync inventory with Square for a specific location
// @access  Private (Admin)
router.post('/locations/:locationId/sync', adminShopFinderController.syncLocationInventory);

// @route   GET /api/admin/shop-finder/reservations
// @desc    Get all reservations with filtering
// @access  Private (Admin)
router.get('/reservations', adminShopFinderController.getAllReservations);

// @route   PUT /api/admin/shop-finder/reservations/:reservationId/status
// @desc    Update reservation status
// @access  Private (Admin)
router.put('/reservations/:reservationId/status', adminShopFinderController.updateReservationStatus);

// @route   GET /api/admin/shop-finder/analytics
// @desc    Get shop finder analytics
// @access  Private (Admin)
router.get('/analytics', adminShopFinderController.getAnalytics);

module.exports = router;
