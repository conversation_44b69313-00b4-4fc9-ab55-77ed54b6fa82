#!/bin/bash
# Make script executable: chmod +x fix-admin-404-assets.sh

# Nirvana Organics - Fix Admin 404 Assets Script
# Comprehensive fix for missing admin frontend assets

set -e

echo "🔧 Nirvana Organics - Fix Admin 404 Assets"
echo "==========================================="

# Color codes
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}✅ $1${NC}"
}

warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if running as correct user
if [ "$USER" != "Nirvana" ]; then
    error "This script must be run as the 'Nirvana' user"
    echo "Please run: sudo -u Nirvana $0"
    exit 1
fi

# Step 1: Diagnose current state
log "Step 1: Diagnosing current state..."

TARGET_ADMIN="/var/www/nirvana-frontend-test/admin"
MISSING_JS="admin-DitisM-I.js"
MISSING_CSS="admin-TY7ZtfqV.css"

echo "🔍 Current State Check:"

# Check source files
if [ -f "dist-admin/assets/$MISSING_JS" ]; then
    success "Source JavaScript exists: dist-admin/assets/$MISSING_JS"
    SOURCE_JS_EXISTS=true
else
    error "Source JavaScript missing: dist-admin/assets/$MISSING_JS"
    SOURCE_JS_EXISTS=false
fi

if [ -f "dist-admin/assets/$MISSING_CSS" ]; then
    success "Source CSS exists: dist-admin/assets/$MISSING_CSS"
    SOURCE_CSS_EXISTS=true
else
    error "Source CSS missing: dist-admin/assets/$MISSING_CSS"
    SOURCE_CSS_EXISTS=false
fi

# Check deployment files
if [ -f "$TARGET_ADMIN/assets/$MISSING_JS" ]; then
    success "Deployed JavaScript exists: $TARGET_ADMIN/assets/$MISSING_JS"
    DEPLOYED_JS_EXISTS=true
else
    error "Deployed JavaScript missing: $TARGET_ADMIN/assets/$MISSING_JS"
    DEPLOYED_JS_EXISTS=false
fi

if [ -f "$TARGET_ADMIN/assets/$MISSING_CSS" ]; then
    success "Deployed CSS exists: $TARGET_ADMIN/assets/$MISSING_CSS"
    DEPLOYED_CSS_EXISTS=true
else
    error "Deployed CSS missing: $TARGET_ADMIN/assets/$MISSING_CSS"
    DEPLOYED_CSS_EXISTS=false
fi

# Step 2: Rebuild admin frontend if source files are missing
if [ "$SOURCE_JS_EXISTS" = false ] || [ "$SOURCE_CSS_EXISTS" = false ]; then
    log "Step 2: Rebuilding admin frontend (source files missing)..."
    
    if [ -f "package.json" ]; then
        echo "📦 Running admin frontend build..."
        npm run build:admin:test
        
        # Verify build completed
        if [ -f "dist-admin/assets/$MISSING_JS" ] && [ -f "dist-admin/assets/$MISSING_CSS" ]; then
            success "Admin frontend build completed successfully"
        else
            error "Admin frontend build failed - assets still missing"
            exit 1
        fi
    else
        error "package.json not found - cannot rebuild frontend"
        exit 1
    fi
else
    log "Step 2: Source files exist, skipping rebuild"
fi

# Step 3: Create deployment directories
log "Step 3: Creating deployment directories..."

# Create target directories with proper permissions
sudo mkdir -p "$TARGET_ADMIN/assets"
sudo chown -R Nirvana:Nirvana /var/www/nirvana-frontend-test/

success "Deployment directories created"

# Step 4: Deploy admin frontend assets
log "Step 4: Deploying admin frontend assets..."

# Copy all admin frontend files
cp -r dist-admin/* "$TARGET_ADMIN/"

# Verify critical files were copied
if [ -f "$TARGET_ADMIN/admin.html" ]; then
    success "admin.html deployed"
else
    error "admin.html deployment failed"
    exit 1
fi

if [ -f "$TARGET_ADMIN/assets/$MISSING_JS" ]; then
    success "JavaScript assets deployed"
else
    error "JavaScript assets deployment failed"
    exit 1
fi

if [ -f "$TARGET_ADMIN/assets/$MISSING_CSS" ]; then
    success "CSS assets deployed"
else
    error "CSS assets deployment failed"
    exit 1
fi

# Step 5: Set proper permissions
log "Step 5: Setting proper permissions..."

# Set ownership
sudo chown -R Nirvana:Nirvana /var/www/nirvana-frontend-test/

# Set directory permissions (755)
find /var/www/nirvana-frontend-test/ -type d -exec chmod 755 {} \;

# Set file permissions (644)
find /var/www/nirvana-frontend-test/ -type f -exec chmod 644 {} \;

success "Permissions set correctly"

# Step 6: Verify deployment
log "Step 6: Verifying deployment..."

echo "📁 Deployed Files:"
ls -la "$TARGET_ADMIN/" | head -10

echo ""
echo "📁 Deployed Assets:"
ls -la "$TARGET_ADMIN/assets/" | head -10

# Check file sizes
JS_SIZE=$(stat -c%s "$TARGET_ADMIN/assets/$MISSING_JS" 2>/dev/null || echo "0")
CSS_SIZE=$(stat -c%s "$TARGET_ADMIN/assets/$MISSING_CSS" 2>/dev/null || echo "0")

echo ""
echo "📊 Asset File Sizes:"
echo "  JavaScript: ${JS_SIZE} bytes"
echo "  CSS: ${CSS_SIZE} bytes"

if [ "$JS_SIZE" -gt 1000 ] && [ "$CSS_SIZE" -gt 1000 ]; then
    success "Asset files have reasonable sizes"
else
    warning "Asset files may be empty or corrupted"
fi

# Step 7: Restart admin server
log "Step 7: Restarting admin server..."

pm2 restart nirvana-backend-admin-test 2>/dev/null || {
    warning "PM2 restart failed, trying alternative restart..."
    pm2 stop nirvana-backend-admin-test 2>/dev/null || true
    sleep 2
    pm2 start /var/www/nirvana-backend-test/ecosystem.config.js --only nirvana-backend-admin-test
}

# Wait for server to start
sleep 3

# Check if server is running
if pm2 list | grep -q "nirvana-backend-admin-test.*online"; then
    success "Admin server is running"
else
    error "Admin server failed to start"
    pm2 logs nirvana-backend-admin-test --lines 10
    exit 1
fi

# Step 8: Test asset accessibility
log "Step 8: Testing asset accessibility..."

echo "🌐 HTTP Tests:"

# Test admin.html
echo "Testing admin.html:"
ADMIN_HTML_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "https://test.shopnirvanaorganics.com/admin/" 2>/dev/null || echo "000")
if [ "$ADMIN_HTML_STATUS" = "200" ]; then
    success "admin.html accessible (HTTP $ADMIN_HTML_STATUS)"
else
    error "admin.html not accessible (HTTP $ADMIN_HTML_STATUS)"
fi

# Test JavaScript asset
echo "Testing JavaScript asset:"
JS_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "https://test.shopnirvanaorganics.com/admin/assets/$MISSING_JS" 2>/dev/null || echo "000")
if [ "$JS_STATUS" = "200" ]; then
    success "JavaScript asset accessible (HTTP $JS_STATUS)"
else
    error "JavaScript asset not accessible (HTTP $JS_STATUS)"
fi

# Test CSS asset
echo "Testing CSS asset:"
CSS_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "https://test.shopnirvanaorganics.com/admin/assets/$MISSING_CSS" 2>/dev/null || echo "000")
if [ "$CSS_STATUS" = "200" ]; then
    success "CSS asset accessible (HTTP $CSS_STATUS)"
else
    error "CSS asset not accessible (HTTP $CSS_STATUS)"
fi

# Step 9: Final verification and summary
log "Step 9: Final verification completed!"

echo ""
echo "📋 FIX SUMMARY"
echo "=============="

if [ "$JS_STATUS" = "200" ] && [ "$CSS_STATUS" = "200" ]; then
    success "✅ All admin assets are now accessible"
    success "✅ 404 errors should be resolved"
else
    error "❌ Some assets are still not accessible"
    echo ""
    echo "🔧 Additional troubleshooting may be needed:"
    echo "1. Check Nginx configuration for /admin/ location"
    echo "2. Verify admin server static file serving"
    echo "3. Check server logs for errors"
fi

echo ""
echo "🔍 VERIFICATION STEPS"
echo "===================="
echo "1. Open browser: https://test.shopnirvanaorganics.com/admin/"
echo "2. Check browser console for 404 errors"
echo "3. Verify admin panel loads completely"
echo "4. Test admin functionality"

echo ""
echo "📊 ASSET URLS"
echo "============="
echo "JavaScript: https://test.shopnirvanaorganics.com/admin/assets/$MISSING_JS"
echo "CSS: https://test.shopnirvanaorganics.com/admin/assets/$MISSING_CSS"

echo ""
success "🎉 Admin 404 assets fix completed!"
