#!/usr/bin/env node

/**
 * Test script to verify frontend path configuration
 * This script tests the environment-aware frontend path resolution
 */

const path = require('path');

console.log('🧪 Testing Frontend Path Configuration');
console.log('=====================================');

// Test different NODE_ENV values
const testEnvironments = ['test', 'production', 'development'];

testEnvironments.forEach(env => {
  console.log(`\n📍 Testing NODE_ENV = "${env}"`);
  
  // Simulate the logic from admin-server.js
  const adminFrontendPath = env === 'test'
    ? '/var/www/nirvana-frontend-test/admin'
    : env === 'production'
    ? '/var/www/nirvana-frontend/admin'
    : path.join(__dirname, '../dist-admin');
  
  const adminHtmlPath = env === 'test'
    ? '/var/www/nirvana-frontend-test/admin/admin.html'
    : env === 'production'
    ? '/var/www/nirvana-frontend/admin/admin.html'
    : path.join(__dirname, '../dist-admin/index.html');
  
  // Simulate the logic from index.js
  const mainFrontendPath = env === 'test'
    ? '/var/www/nirvana-frontend-test/main'
    : env === 'production'
    ? '/var/www/nirvana-frontend/main'
    : path.join(__dirname, '../dist');
  
  const mainHtmlPath = env === 'test'
    ? '/var/www/nirvana-frontend-test/main/index.html'
    : env === 'production'
    ? '/var/www/nirvana-frontend/main/index.html'
    : path.join(__dirname, '../dist/index.html');
  
  console.log(`  Admin Frontend Path: ${adminFrontendPath}`);
  console.log(`  Admin HTML Path:     ${adminHtmlPath}`);
  console.log(`  Main Frontend Path:  ${mainFrontendPath}`);
  console.log(`  Main HTML Path:      ${mainHtmlPath}`);
});

console.log('\n✅ Path configuration test completed');
console.log('\n📋 Expected behavior for test environment:');
console.log('  - Admin server should serve from: /var/www/nirvana-frontend-test/admin/');
console.log('  - Admin HTML should be:           /var/www/nirvana-frontend-test/admin/admin.html');
console.log('  - Main server should serve from:  /var/www/nirvana-frontend-test/main/');
console.log('  - Main HTML should be:            /var/www/nirvana-frontend-test/main/index.html');

console.log('\n🔧 To fix ENOENT errors:');
console.log('  1. Ensure NODE_ENV=test is set in environment files');
console.log('  2. Deploy frontend builds to /var/www/nirvana-frontend-test/');
console.log('  3. Restart PM2 processes after deployment');
