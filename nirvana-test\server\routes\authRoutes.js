// server/routes/authRoutes.js

const express = require('express');
const router = express.Router(); // Create a new router for authentication and user-related routes

// Import controllers and middleware
// Ensure these paths are correct relative to server/routes/
// authController.js is in server/controllers/, so we go up one level (../) then into controllers/
const authController = require('../controllers/authController');
// auth.js (middleware) is in server/middleware/, so we go up one level (../) then into middleware/
const { authenticate } = require('../middleware/auth'); // Assuming 'authenticate' is a named export from auth.js

// --- Public Authentication Routes ---
// These routes typically do not require authentication middleware
router.post('/register', authController.register);
router.post('/login', authController.login); // Legacy login handler (redirects to loginUser/loginAdmin)
router.post('/login/user', authController.loginUser); // Specific user login
router.post('/login/admin', authController.loginAdmin); // Specific admin login
router.post('/refresh-token', authController.refreshToken);
router.post('/verify-email', authController.verifyEmail);
router.post('/password/request-reset', authController.requestPasswordReset);
router.post('/password/reset', authController.resetPassword);
router.post('/google-login', authController.googleLogin);

// --- Protected User Profile & Data Routes ---
// These routes require the 'authenticate' middleware to ensure the user is logged in
router.get('/profile', authenticate, authController.getProfile);
router.put('/profile', authenticate, authController.updateProfile);
router.post('/change-password', authenticate, authController.changePassword);
router.delete('/delete-account', authenticate, authController.deleteAccount);
router.post('/logout', authenticate, authController.logout); // Logout can be POST for consistency

// User-specific Order History and Addresses
router.get('/orders', authenticate, authController.getOrderHistory);
router.get('/orders/:orderId', authenticate, authController.getOrderDetails);
router.put('/addresses', authenticate, authController.updateAddress);
router.get('/addresses', authenticate, authController.getAddresses);


// Export the router instance to be used by the main Express application (index.js)
module.exports = router;
