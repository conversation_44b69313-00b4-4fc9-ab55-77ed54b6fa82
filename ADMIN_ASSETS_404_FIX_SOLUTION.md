# Admin Assets 404 Fix - Complete Solution

## Problem Summary
The admin frontend assets are returning HTTP 404 errors despite being successfully built and deployed to the correct file system locations. Additionally, there's a database error with missing 'is_active' column in the products table.

**Current Issues:**
- ❌ JavaScript asset returns HTTP 404: https://test.shopnirvanaorganics.com/admin/assets/admin-DitisM-I.js
- ❌ CSS asset returns HTTP 404: https://test.shopnirvanaorganics.com/admin/assets/admin-TY7ZtfqV.css
- ❌ Database error: "Unknown column 'p.is_active' in 'WHERE'" affecting products table queries
- ✅ admin.html is accessible (HTTP 200)

**File System Verification:**
The assets exist on the server with correct permissions:
- `/var/www/nirvana-frontend-test/admin/assets/admin-DitisM-I.js` (852,407 bytes)
- `/var/www/nirvana-frontend-test/admin/assets/admin-TY7ZtfqV.css` (76,442 bytes)

## Root Cause Analysis

### 1. Nginx Location Block Priority Issue
The original Nginx configuration had a location block conflict. The regex location `~* \.(js|css|...)$` was matching admin assets before the specific `/admin/assets/` location block could handle them, causing the assets to be served from the wrong directory.

### 2. Database Schema Inconsistency
The Product model was missing the `isActive` field definition, even though the database migration includes it and queries reference `p.is_active`.

## Solution Implementation

### Option 1: Comprehensive Fix (Recommended)
Run the all-in-one fix script that addresses both issues:

```bash
# Navigate to the project directory
cd /var/www/nirvana-backend-test

# Make the comprehensive fix script executable
sudo chmod +x comprehensive-admin-fix.sh

# Run the comprehensive fix as root
sudo ./comprehensive-admin-fix.sh
```

### Option 2: Individual Fixes
If you prefer to run fixes separately:

#### Step 1: Fix Nginx Configuration
```bash
# Deploy updated Nginx configuration
sudo ./nirvana-test/deploy-nginx.sh

# Or run the specific admin assets fix
sudo chmod +x nirvana-test/fix-nginx-admin-assets.sh
sudo ./nirvana-test/fix-nginx-admin-assets.sh
```

#### Step 2: Fix Database Schema
```bash
# Fix the missing is_active column in products table
sudo chmod +x nirvana-test/fix-products-is-active-column.js
sudo -u Nirvana node nirvana-test/fix-products-is-active-column.js
```

#### Step 3: Verify All Fixes
```bash
# Run comprehensive tests
sudo chmod +x nirvana-test/test-admin-assets-fix.sh
./nirvana-test/test-admin-assets-fix.sh
```

## Manual Verification Steps

### 1. Test Asset URLs Directly
```bash
# Test JavaScript asset
curl -I https://test.shopnirvanaorganics.com/admin/assets/admin-DitisM-I.js

# Test CSS asset
curl -I https://test.shopnirvanaorganics.com/admin/assets/admin-TY7ZtfqV.css

# Both should return HTTP 200
```

### 2. Browser Testing
1. Open: https://test.shopnirvanaorganics.com/admin/
2. Open browser developer tools (F12)
3. Check Network tab for any 404 errors
4. Verify admin panel loads with proper styling
5. Test admin functionality

### 3. Check Nginx Configuration
```bash
# Verify the admin assets location block exists
sudo grep -A 10 "location /admin/assets/" /etc/nginx/sites-available/nirvana-organics-test

# Test Nginx configuration
sudo nginx -t

# Check Nginx status
sudo systemctl status nginx
```

## Expected Nginx Configuration
The correct Nginx configuration should include this location block:

```nginx
# Admin assets - specific handling (must come before general static assets)
location /admin/assets/ {
    alias /var/www/nirvana-frontend-test/admin/assets/;
    expires 1h;
    add_header Cache-Control "public";
    add_header Vary "Accept-Encoding";

    # CORS for fonts and assets
    add_header Access-Control-Allow-Origin "*";
    add_header Access-Control-Allow-Methods "GET, OPTIONS";
    add_header Access-Control-Allow-Headers "Origin, X-Requested-With, Content-Type, Accept";

    # Security headers for admin assets
    add_header X-Environment "TEST-ADMIN-ASSETS" always;
}
```

## Troubleshooting

### If Assets Still Return 404
1. **Check file permissions:**
   ```bash
   sudo ls -la /var/www/nirvana-frontend-test/admin/assets/
   sudo chown -R Nirvana:Nirvana /var/www/nirvana-frontend-test/
   sudo chmod -R 755 /var/www/nirvana-frontend-test/
   ```

2. **Check Nginx error logs:**
   ```bash
   sudo tail -f /var/log/nginx/nirvana-test-main-error.log
   ```

3. **Verify Nginx configuration is active:**
   ```bash
   sudo ls -la /etc/nginx/sites-enabled/nirvana-organics-test
   ```

4. **Reload Nginx:**
   ```bash
   sudo systemctl reload nginx
   ```

### If Admin Panel Doesn't Load Properly
1. **Verify admin.html references correct assets:**
   ```bash
   curl -s https://test.shopnirvanaorganics.com/admin/ | grep -E "(admin-DitisM-I\.js|admin-TY7ZtfqV\.css)"
   ```

2. **Check admin server is running:**
   ```bash
   pm2 list | grep admin
   pm2 logs nirvana-backend-admin-test --lines 10
   ```

## Success Criteria
After implementing the fix, you should see:
- ✅ JavaScript asset returns HTTP 200: https://test.shopnirvanaorganics.com/admin/assets/admin-DitisM-I.js
- ✅ CSS asset returns HTTP 200: https://test.shopnirvanaorganics.com/admin/assets/admin-TY7ZtfqV.css
- ✅ Admin panel loads completely with styling and functionality
- ✅ No 404 errors in browser console when accessing admin panel

## Files Created/Modified
1. `nirvana-test/fix-nginx-admin-assets.sh` - Comprehensive fix script
2. `nirvana-test/test-admin-assets-fix.sh` - Testing and verification script
3. `vite.admin.config.ts` - Fixed syntax errors (already completed)
4. `ADMIN_ASSETS_404_FIX_SOLUTION.md` - This documentation

## Next Steps
1. Execute the solution steps on the server
2. Verify all tests pass
3. Confirm admin panel functionality
4. Monitor for any remaining issues

The solution addresses the core issue of Nginx not properly serving admin assets by ensuring the correct configuration is deployed and active.
