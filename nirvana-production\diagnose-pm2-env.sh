#!/bin/bash

# PM2 Environment Diagnostic Script for Nirvana Organics (PRODUCTION Environment)
# Run this script on your server to diagnose PM2 environment issues

echo "🔍 Diagnosing PM2 Environment Configuration (PRODUCTION)..."
echo "==========================================================="

# Environment-specific variables
DEPLOY_DIR="/var/www/nirvana-backend-production"
DEPLOY_USER="Nirvana"
ENVIRONMENT="production"
ENV_FILE=".env.production"
ADMIN_ENV_FILE=".env.admin"
MAIN_PROCESS="nirvana-backend-main-production"
ADMIN_PROCESS="nirvana-backend-admin-production"

# Check if we're running as the correct user
echo "Current user: $(whoami)"
echo "Nirvana user processes:"
ps aux | grep -E "(pm2|node)" | grep Nirvana | head -5
echo ""

# Check PM2 ecosystem configuration
echo "📋 PM2 Ecosystem Configuration:"
echo "================================"
if [ -f "$DEPLOY_DIR/ecosystem.config.js" ]; then
    cat $DEPLOY_DIR/ecosystem.config.js
else
    echo "❌ ecosystem.config.js not found!"
fi
echo ""

# Check PM2 process details
echo "📊 PM2 Process Details:"
echo "======================="
sudo -u $DEPLOY_USER pm2 list
echo ""

echo "🔧 Main Process Environment Variables:"
echo "====================================="
sudo -u Nirvana pm2 show nirvana-backend-main-test | grep -A 50 "env:"
echo ""

echo "🔧 Admin Process Environment Variables:"
echo "======================================"
sudo -u Nirvana pm2 show nirvana-backend-admin-test | grep -A 50 "env:"
echo ""

# Check environment files
echo "📁 Environment Files Check:"
echo "==========================="
echo "Main .env.test exists: $([ -f /var/www/nirvana-backend-test/.env.test ] && echo 'YES' || echo 'NO')"
echo "Admin .env.admin.test exists: $([ -f /var/www/nirvana-backend-test/.env.admin.test ] && echo 'YES' || echo 'NO')"
echo ""

if [ -f "/var/www/nirvana-backend-test/.env.test" ]; then
    echo "NODE_ENV in .env.test: $(grep NODE_ENV /var/www/nirvana-backend-test/.env.test)"
    echo "EMAIL_USER in .env.test: $(grep EMAIL_USER /var/www/nirvana-backend-test/.env.test | head -1)"
    echo "EMAIL_ORDERS_USER in .env.test: $(grep EMAIL_ORDERS_USER /var/www/nirvana-backend-test/.env.test)"
fi
echo ""

if [ -f "/var/www/nirvana-backend-test/.env.admin.test" ]; then
    echo "NODE_ENV in .env.admin.test: $(grep NODE_ENV /var/www/nirvana-backend-test/.env.admin.test)"
    echo "EMAIL_USER in .env.admin.test: $(grep EMAIL_USER /var/www/nirvana-backend-test/.env.admin.test | head -1)"
    echo "EMAIL_ORDERS_USER in .env.admin.test: $(grep EMAIL_ORDERS_USER /var/www/nirvana-backend-test/.env.admin.test)"
fi
echo ""

# Test manual environment loading
echo "🧪 Manual Environment Loading Test:"
echo "==================================="
cd /var/www/nirvana-backend-test

echo "Testing .env.test loading:"
NODE_ENV=test node -e "
require('dotenv').config({ path: '.env.test' });
console.log('NODE_ENV:', process.env.NODE_ENV);
console.log('DB_HOST:', process.env.DB_HOST || 'UNDEFINED');
console.log('DB_PORT:', process.env.DB_PORT || 'UNDEFINED');
console.log('EMAIL_USER:', process.env.EMAIL_USER || 'UNDEFINED');
console.log('EMAIL_ORDERS_USER:', process.env.EMAIL_ORDERS_USER || 'UNDEFINED');
console.log('EMAIL_SUPPORT_USER:', process.env.EMAIL_SUPPORT_USER || 'UNDEFINED');
console.log('SQUARE_ACCESS_TOKEN:', process.env.SQUARE_ACCESS_TOKEN ? 'CONFIGURED' : 'UNDEFINED');
" 2>/dev/null || echo "❌ Failed to test .env.test"

echo ""
echo "Testing .env.admin.test loading:"
NODE_ENV=test node -e "
require('dotenv').config({ path: '.env.admin.test' });
console.log('NODE_ENV:', process.env.NODE_ENV);
console.log('DB_HOST:', process.env.DB_HOST || 'UNDEFINED');
console.log('DB_PORT:', process.env.DB_PORT || 'UNDEFINED');
console.log('EMAIL_USER:', process.env.EMAIL_USER || 'UNDEFINED');
console.log('EMAIL_ORDERS_USER:', process.env.EMAIL_ORDERS_USER || 'UNDEFINED');
console.log('EMAIL_SUPPORT_USER:', process.env.EMAIL_SUPPORT_USER || 'UNDEFINED');
console.log('SQUARE_ACCESS_TOKEN:', process.env.SQUARE_ACCESS_TOKEN ? 'CONFIGURED' : 'UNDEFINED');
" 2>/dev/null || echo "❌ Failed to test .env.admin.test"

echo ""
echo "📋 Recent PM2 Logs (Main):"
echo "=========================="
sudo -u Nirvana pm2 logs nirvana-backend-main-test --lines 10 | grep -E "(Loading environment|NODE_ENV|EMAIL_|undefined)" || echo "No relevant log entries found"

echo ""
echo "📋 Recent PM2 Logs (Admin):"
echo "==========================="
sudo -u Nirvana pm2 logs nirvana-backend-admin-test --lines 10 | grep -E "(Loading environment|NODE_ENV|EMAIL_|undefined)" || echo "No relevant log entries found"

echo ""
echo "🔧 Recommended Actions:"
echo "======================"
echo "1. If environment files are missing or incorrect, run: ./fix-env-files.sh"
echo "2. If PM2 is overriding env vars, check ecosystem.config.test.js"
echo "3. If NODE_ENV is wrong, verify PM2 process environment"
echo "4. If still failing, try: sudo -u Nirvana pm2 delete all && pm2 start ecosystem.config.test.js"
