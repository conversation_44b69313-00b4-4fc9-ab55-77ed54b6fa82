'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    // Create social_media_accounts table
    await queryInterface.createTable('social_media_accounts', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true
      },
      platform: {
        type: Sequelize.ENUM('facebook', 'twitter', 'instagram', 'linkedin', 'youtube', 'pinterest', 'tiktok'),
        allowNull: false,
        field: 'platform'
      },
      account_id: {
        type: Sequelize.STRING,
        allowNull: false,
        field: 'account_id'
      },
      account_name: {
        type: Sequelize.STRING,
        allowNull: false,
        field: 'account_name'
      },
      account_handle: {
        type: Sequelize.STRING,
        allowNull: true,
        field: 'account_handle'
      },
      profile_picture: {
        type: Sequelize.TEXT,
        allowNull: true,
        field: 'profile_picture'
      },
      access_token: {
        type: Sequelize.TEXT,
        allowNull: false,
        field: 'access_token'
      },
      refresh_token: {
        type: Sequelize.TEXT,
        allowNull: true,
        field: 'refresh_token'
      },
      token_expires_at: {
        type: Sequelize.DATE,
        allowNull: true,
        field: 'token_expires_at'
      },
      is_active: {
        type: Sequelize.BOOLEAN,
        defaultValue: true,
        field: 'is_active'
      },
      is_verified: {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
        field: 'is_verified'
      },
      followers_count: {
        type: Sequelize.INTEGER,
        defaultValue: 0,
        field: 'followers_count'
      },
      following_count: {
        type: Sequelize.INTEGER,
        defaultValue: 0,
        field: 'following_count'
      },
      posts_count: {
        type: Sequelize.INTEGER,
        defaultValue: 0,
        field: 'posts_count'
      },
      platform_data: {
        type: Sequelize.JSON,
        allowNull: true,
        field: 'platform_data'
      },
      permissions: {
        type: Sequelize.JSON,
        allowNull: true,
        field: 'permissions'
      },
      last_sync_at: {
        type: Sequelize.DATE,
        allowNull: true,
        field: 'last_sync_at'
      },
      sync_status: {
        type: Sequelize.ENUM('pending', 'syncing', 'completed', 'failed'),
        defaultValue: 'pending',
        field: 'sync_status'
      },
      last_error: {
        type: Sequelize.TEXT,
        allowNull: true,
        field: 'last_error'
      },
      error_count: {
        type: Sequelize.INTEGER,
        defaultValue: 0,
        field: 'error_count'
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      }
    });

    // Create social_media_posts table
    await queryInterface.createTable('social_media_posts', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true
      },
      content: {
        type: Sequelize.TEXT,
        allowNull: false,
        field: 'content'
      },
      media_urls: {
        type: Sequelize.JSON,
        allowNull: true,
        field: 'media_urls'
      },
      media_type: {
        type: Sequelize.ENUM('none', 'image', 'video', 'carousel', 'story'),
        defaultValue: 'none',
        field: 'media_type'
      },
      platforms: {
        type: Sequelize.JSON,
        allowNull: false,
        field: 'platforms'
      },
      scheduled_at: {
        type: Sequelize.DATE,
        allowNull: true,
        field: 'scheduled_at'
      },
      published_at: {
        type: Sequelize.DATE,
        allowNull: true,
        field: 'published_at'
      },
      status: {
        type: Sequelize.ENUM('draft', 'scheduled', 'publishing', 'published', 'failed', 'cancelled'),
        defaultValue: 'draft',
        field: 'status'
      },
      platform_post_ids: {
        type: Sequelize.JSON,
        allowNull: true,
        field: 'platform_post_ids'
      },
      total_likes: {
        type: Sequelize.INTEGER,
        defaultValue: 0,
        field: 'total_likes'
      },
      total_comments: {
        type: Sequelize.INTEGER,
        defaultValue: 0,
        field: 'total_comments'
      },
      total_shares: {
        type: Sequelize.INTEGER,
        defaultValue: 0,
        field: 'total_shares'
      },
      total_reach: {
        type: Sequelize.INTEGER,
        defaultValue: 0,
        field: 'total_reach'
      },
      total_impressions: {
        type: Sequelize.INTEGER,
        defaultValue: 0,
        field: 'total_impressions'
      },
      platform_metrics: {
        type: Sequelize.JSON,
        allowNull: true,
        field: 'platform_metrics'
      },
      hashtags: {
        type: Sequelize.JSON,
        allowNull: true,
        field: 'hashtags'
      },
      mentions: {
        type: Sequelize.JSON,
        allowNull: true,
        field: 'mentions'
      },
      campaign_id: {
        type: Sequelize.STRING,
        allowNull: true,
        field: 'campaign_id'
      },
      category: {
        type: Sequelize.STRING,
        allowNull: true,
        field: 'category'
      },
      tags: {
        type: Sequelize.JSON,
        allowNull: true,
        field: 'tags'
      },
      last_error: {
        type: Sequelize.TEXT,
        allowNull: true,
        field: 'last_error'
      },
      retry_count: {
        type: Sequelize.INTEGER,
        defaultValue: 0,
        field: 'retry_count'
      },
      max_retries: {
        type: Sequelize.INTEGER,
        defaultValue: 3,
        field: 'max_retries'
      },
      requires_approval: {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
        field: 'requires_approval'
      },
      approved_by: {
        type: Sequelize.UUID,
        allowNull: true,
        field: 'approved_by',
        references: {
          model: 'users',
          key: 'id'
        }
      },
      approved_at: {
        type: Sequelize.DATE,
        allowNull: true,
        field: 'approved_at'
      },
      created_by: {
        type: Sequelize.UUID,
        allowNull: false,
        field: 'created_by',
        references: {
          model: 'users',
          key: 'id'
        }
      },
      last_metrics_update: {
        type: Sequelize.DATE,
        allowNull: true,
        field: 'last_metrics_update'
      },
      settings: {
        type: Sequelize.JSON,
        allowNull: true,
        field: 'settings'
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      }
    });

    // Create social_media_analytics table
    await queryInterface.createTable('social_media_analytics', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true
      },
      account_id: {
        type: Sequelize.UUID,
        allowNull: false,
        field: 'account_id',
        references: {
          model: 'social_media_accounts',
          key: 'id'
        }
      },
      date: {
        type: Sequelize.DATEONLY,
        allowNull: false,
        field: 'date'
      },
      period: {
        type: Sequelize.ENUM('daily', 'weekly', 'monthly'),
        allowNull: false,
        field: 'period'
      },
      followers_count: {
        type: Sequelize.INTEGER,
        defaultValue: 0,
        field: 'followers_count'
      },
      followers_growth: {
        type: Sequelize.INTEGER,
        defaultValue: 0,
        field: 'followers_growth'
      },
      following_count: {
        type: Sequelize.INTEGER,
        defaultValue: 0,
        field: 'following_count'
      },
      total_likes: {
        type: Sequelize.INTEGER,
        defaultValue: 0,
        field: 'total_likes'
      },
      total_comments: {
        type: Sequelize.INTEGER,
        defaultValue: 0,
        field: 'total_comments'
      },
      total_shares: {
        type: Sequelize.INTEGER,
        defaultValue: 0,
        field: 'total_shares'
      },
      total_saves: {
        type: Sequelize.INTEGER,
        defaultValue: 0,
        field: 'total_saves'
      },
      total_reach: {
        type: Sequelize.INTEGER,
        defaultValue: 0,
        field: 'total_reach'
      },
      total_impressions: {
        type: Sequelize.INTEGER,
        defaultValue: 0,
        field: 'total_impressions'
      },
      unique_reach: {
        type: Sequelize.INTEGER,
        defaultValue: 0,
        field: 'unique_reach'
      },
      posts_count: {
        type: Sequelize.INTEGER,
        defaultValue: 0,
        field: 'posts_count'
      },
      stories_count: {
        type: Sequelize.INTEGER,
        defaultValue: 0,
        field: 'stories_count'
      },
      videos_count: {
        type: Sequelize.INTEGER,
        defaultValue: 0,
        field: 'videos_count'
      },
      engagement_rate: {
        type: Sequelize.DECIMAL(5, 2),
        defaultValue: 0.00,
        field: 'engagement_rate'
      },
      like_rate: {
        type: Sequelize.DECIMAL(5, 2),
        defaultValue: 0.00,
        field: 'like_rate'
      },
      comment_rate: {
        type: Sequelize.DECIMAL(5, 2),
        defaultValue: 0.00,
        field: 'comment_rate'
      },
      share_rate: {
        type: Sequelize.DECIMAL(5, 2),
        defaultValue: 0.00,
        field: 'share_rate'
      },
      audience_demographics: {
        type: Sequelize.JSON,
        allowNull: true,
        field: 'audience_demographics'
      },
      top_posts: {
        type: Sequelize.JSON,
        allowNull: true,
        field: 'top_posts'
      },
      top_hashtags: {
        type: Sequelize.JSON,
        allowNull: true,
        field: 'top_hashtags'
      },
      best_posting_times: {
        type: Sequelize.JSON,
        allowNull: true,
        field: 'best_posting_times'
      },
      platform_specific_metrics: {
        type: Sequelize.JSON,
        allowNull: true,
        field: 'platform_specific_metrics'
      },
      website_clicks: {
        type: Sequelize.INTEGER,
        defaultValue: 0,
        field: 'website_clicks'
      },
      profile_views: {
        type: Sequelize.INTEGER,
        defaultValue: 0,
        field: 'profile_views'
      },
      competitor_metrics: {
        type: Sequelize.JSON,
        allowNull: true,
        field: 'competitor_metrics'
      },
      data_completeness: {
        type: Sequelize.DECIMAL(3, 2),
        defaultValue: 1.00,
        field: 'data_completeness'
      },
      last_updated: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
        field: 'last_updated'
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      }
    });

    // Add indexes
    await queryInterface.addIndex('social_media_accounts', ['platform', 'account_id'], {
      unique: true,
      name: 'social_media_accounts_platform_account_id_unique'
    });
    await queryInterface.addIndex('social_media_accounts', ['platform']);
    await queryInterface.addIndex('social_media_accounts', ['is_active']);
    await queryInterface.addIndex('social_media_accounts', ['last_sync_at']);

    await queryInterface.addIndex('social_media_posts', ['status']);
    await queryInterface.addIndex('social_media_posts', ['scheduled_at']);
    await queryInterface.addIndex('social_media_posts', ['published_at']);
    await queryInterface.addIndex('social_media_posts', ['created_by']);
    await queryInterface.addIndex('social_media_posts', ['campaign_id']);
    await queryInterface.addIndex('social_media_posts', ['category']);
    await queryInterface.addIndex('social_media_posts', ['created_at']);

    await queryInterface.addIndex('social_media_analytics', ['account_id', 'date', 'period'], {
      unique: true,
      name: 'social_media_analytics_account_date_period_unique'
    });
    await queryInterface.addIndex('social_media_analytics', ['account_id']);
    await queryInterface.addIndex('social_media_analytics', ['date']);
    await queryInterface.addIndex('social_media_analytics', ['period']);
    await queryInterface.addIndex('social_media_analytics', ['engagement_rate']);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('social_media_analytics');
    await queryInterface.dropTable('social_media_posts');
    await queryInterface.dropTable('social_media_accounts');
  }
};
