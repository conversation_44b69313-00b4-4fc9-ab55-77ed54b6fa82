#!/bin/bash

# Comprehensive PM2 Environment Loading Fix for Nirvana Organics
# Addresses the core issue: PM2 processes not loading environment variables

echo "🔧 Fixing PM2 Environment Variable Loading Issues (PRODUCTION Environment)"
echo "=========================================================================="

# Check if running as root or with sudo
if [[ $EUID -ne 0 ]]; then
   echo "❌ This script must be run as root or with sudo"
   exit 1
fi

# Set variables for PRODUCTION environment
DEPLOY_DIR="/var/www/nirvana-backend-production"
DEPLOY_USER="Nirvana"
ENVIRONMENT="production"
ENV_FILE=".env.production"
ADMIN_ENV_FILE=".env.admin"
MAIN_PROCESS="nirvana-backend-main-production"
ADMIN_PROCESS="nirvana-backend-admin-production"

echo "📍 Working directory: $DEPLOY_DIR"
echo "👤 User: $DEPLOY_USER"
echo "🌍 Environment: $ENVIRONMENT"
echo "📋 Main Process: $MAIN_PROCESS"
echo "📋 Admin Process: $ADMIN_PROCESS"
echo ""

# Step 1: Diagnose current PM2 state
echo "🔍 Diagnosing current PM2 state..."
echo "Current PM2 processes for user $DEPLOY_USER:"
sudo -u $DEPLOY_USER pm2 list 2>/dev/null || echo "No PM2 processes found or PM2 not initialized"
echo ""

# Step 2: Stop all PM2 processes
echo "🛑 Stopping PM2 processes for $ENVIRONMENT environment..."
sudo -u $DEPLOY_USER pm2 stop $MAIN_PROCESS $ADMIN_PROCESS 2>/dev/null || echo "Processes not running"
sudo -u $DEPLOY_USER pm2 delete $MAIN_PROCESS $ADMIN_PROCESS 2>/dev/null || echo "No processes to delete"
sleep 2

# Step 3: Check and fix environment files
echo "📁 Checking environment files..."
echo "📝 Creating/updating $ENV_FILE..."
cat > "$DEPLOY_DIR/$ENV_FILE" << 'EOF'
# Nirvana Organics - Main Server Production Configuration
NODE_ENV=production
PORT=5000
FRONTEND_URL=https://shopnirvanaorganics.com
BACKEND_URL=https://shopnirvanaorganics.com

# Database Configuration (Production)
DB_HOST=srv1921.hstgr.io
DB_PORT=3306
DB_NAME=u106832845_nirvana
DB_USER=u106832845_root
DB_PASSWORD=Hrishikesh@0912
DB_DIALECT=mysql

# Security Configuration (Production)
JWT_SECRET=production-jwt-secret-key-change-this-to-a-secure-random-string-minimum-64-characters
JWT_EXPIRES_IN=24h
BCRYPT_ROUNDS=12

# CORS Configuration (Production)
CORS_ORIGIN=https://shopnirvanaorganics.com
CORS_CREDENTIALS=true

# Session Configuration (Production)
SESSION_SECRET=production-session-secret-change-this-to-a-secure-random-string-minimum-64-characters
SESSION_MAX_AGE=86400000

# Rate Limiting (Production)
RATE_LIMIT_WINDOW_MS=900000
MAIN_RATE_LIMIT_MAX_REQUESTS=500

# Email Configuration (Production)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
SMTP_SECURE=false
EMAIL_USER=<EMAIL>
EMAIL_PASS=aowu yxxq xeyj qbpe
EMAIL_FROM=<EMAIL>
EMAIL_FROM_NAME=Shop Nirvana Organics

# Email Addresses (Production Environment)
EMAIL_ORDERS=<EMAIL>
EMAIL_ORDERS_USER=<EMAIL>
EMAIL_ORDERS_PASS=wiba ieco dxth xdyn
EMAIL_ORDERS_HOST=smtp.gmail.com
EMAIL_ORDERS_PORT=587

EMAIL_SUPPORT=<EMAIL>
EMAIL_SUPPORT_USER=<EMAIL>
EMAIL_SUPPORT_PASS=gqfh cyls ryuv yind
EMAIL_SUPPORT_HOST=smtp.gmail.com
EMAIL_SUPPORT_PORT=587

EMAIL_CUSTOMER_SERVICE=<EMAIL>
EMAIL_NO_REPLY=<EMAIL>
EMAIL_ADMIN=<EMAIL>

# VAPID Configuration (Production)
VAPID_PUBLIC_KEY=BGSeufHC3qXpetuNrZmPuQAp34DAHFUm3l6ZWoqGYKLY252IwUpPiZe9PjcVlsueX3JTJ8sBhZiJ49rnZuI73kQ
VAPID_PRIVATE_KEY=FZ4eME5YyL0IIF4SZsSW0_PZ8ISMM5mtfGFKPF7a3AQ
VAPID_EMAIL=mailto:<EMAIL>

# Payment Configuration (Production)
SQUARE_ENVIRONMENT=production
SQUARE_APPLICATION_ID=your-production-square-application-id
SQUARE_ACCESS_TOKEN=your-production-square-access-token

# Google OAuth Configuration (Production)
GOOGLE_CLIENT_ID=your-production-google-client-id
GOOGLE_CLIENT_SECRET=your-production-google-client-secret
GOOGLE_REDIRECT_URI=https://shopnirvanaorganics.com/auth/google/callback

# Square OAuth Configuration (Production)
SQUARE_OAUTH_CLIENT_ID=your-production-square-oauth-client-id
SQUARE_OAUTH_CLIENT_SECRET=your-production-square-oauth-client-secret
SQUARE_OAUTH_REDIRECT_URI=https://shopnirvanaorganics.com/auth/square/callback

# File Upload Configuration (Production)
UPLOAD_PATH=./public/uploads
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=jpg,jpeg,png,gif,pdf,doc,docx

# Cache Configuration (Production)
CACHE_TTL=3600
REDIS_URL=redis://localhost:6379

# Logging Configuration (Production)
LOG_LEVEL=info
LOG_FILE=./logs/app.log

# Performance Monitoring (Production)
ENABLE_PERFORMANCE_MONITORING=true
PERFORMANCE_LOG_THRESHOLD=2000

# Feature Flags (Production)
ENABLE_EMAIL_VERIFICATION=true
ENABLE_SMS_NOTIFICATIONS=false
ENABLE_PUSH_NOTIFICATIONS=true
ENABLE_ANALYTICS=true

# API Configuration (Production)
API_VERSION=v1
API_RATE_LIMIT=500
API_TIMEOUT=30000

# Security Headers (Production)
ENABLE_HELMET=true
ENABLE_RATE_LIMITING=true
ENABLE_CORS=true

# Production Environment Specific
DEBUG_MODE=false
ENABLE_SWAGGER=false
ENABLE_MORGAN_LOGGING=false
TEST_MODE=false
MOCK_EXTERNAL_APIS=false
DISABLE_EMAIL_SENDING=false
EOF

echo "📝 Creating/updating $ADMIN_ENV_FILE..."
cat > "$DEPLOY_DIR/$ADMIN_ENV_FILE" << 'EOF'
# Nirvana Organics - Admin Server Production Configuration
NODE_ENV=production
PORT=3001
FRONTEND_URL=https://shopnirvanaorganics.com/admin
BACKEND_URL=https://shopnirvanaorganics.com

# Database Configuration (Production)
DB_HOST=srv1921.hstgr.io
DB_PORT=3306
DB_NAME=u106832845_nirvana
DB_USER=u106832845_root
DB_PASSWORD=Hrishikesh@0912
DB_DIALECT=mysql

# Security Configuration (Production)
JWT_SECRET=production-admin-jwt-secret-change-this-to-a-secure-random-string-minimum-64-characters
JWT_EXPIRES_IN=24h
BCRYPT_ROUNDS=12

# CORS Configuration (Production)
CORS_ORIGIN=https://shopnirvanaorganics.com
CORS_CREDENTIALS=true

# Session Configuration (Production)
SESSION_SECRET=production-admin-session-secret-change-this-to-a-secure-random-string-minimum-64-characters
SESSION_MAX_AGE=86400000

# Rate Limiting (Admin - More Restrictive)
RATE_LIMIT_WINDOW_MS=900000
ADMIN_RATE_LIMIT_MAX_REQUESTS=200

# Admin Security (Production)
ADMIN_SECURITY_MODE=true
ADMIN_IP_WHITELIST=

# Email Configuration (Production admin)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
SMTP_SECURE=false
EMAIL_USER=<EMAIL>
EMAIL_PASS=aowu yxxq xeyj qbpe
EMAIL_FROM=<EMAIL>
EMAIL_FROM_NAME=Shop Nirvana Organics

# Email Addresses (Production Admin Environment)
EMAIL_ORDERS=<EMAIL>
EMAIL_ORDERS_USER=<EMAIL>
EMAIL_ORDERS_PASS=wiba ieco dxth xdyn
EMAIL_ORDERS_HOST=smtp.gmail.com
EMAIL_ORDERS_PORT=587

EMAIL_SUPPORT=<EMAIL>
EMAIL_SUPPORT_USER=<EMAIL>
EMAIL_SUPPORT_PASS=gqfh cyls ryuv yind
EMAIL_SUPPORT_HOST=smtp.gmail.com
EMAIL_SUPPORT_PORT=587

EMAIL_CUSTOMER_SERVICE=<EMAIL>
EMAIL_NO_REPLY=<EMAIL>
EMAIL_ADMIN=<EMAIL>

# VAPID Configuration (Production Admin)
VAPID_PUBLIC_KEY=BGSeufHC3qXpetuNrZmPuQAp34DAHFUm3l6ZWoqGYKLY252IwUpPiZe9PjcVlsueX3JTJ8sBhZiJ49rnZuI73kQ
VAPID_PRIVATE_KEY=FZ4eME5YyL0IIF4SZsSW0_PZ8ISMM5mtfGFKPF7a3AQ
VAPID_EMAIL=mailto:<EMAIL>

# Payment Configuration (Production)
SQUARE_ENVIRONMENT=production
SQUARE_APPLICATION_ID=your-production-square-application-id
SQUARE_ACCESS_TOKEN=your-production-square-access-token

# Google OAuth Configuration (Production Admin)
GOOGLE_CLIENT_ID=your-production-google-client-id
GOOGLE_CLIENT_SECRET=your-production-google-client-secret
GOOGLE_REDIRECT_URI=https://shopnirvanaorganics.com/admin/auth/google/callback

# Square OAuth Configuration (Production Admin)
SQUARE_OAUTH_CLIENT_ID=your-production-square-oauth-client-id
SQUARE_OAUTH_CLIENT_SECRET=your-production-square-oauth-client-secret
SQUARE_OAUTH_REDIRECT_URI=https://shopnirvanaorganics.com/admin/auth/square/callback

# File Upload Configuration (Production Admin)
UPLOAD_PATH=./public/uploads
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=jpg,jpeg,png,gif,pdf,doc,docx

# Cache Configuration (Production Admin)
CACHE_TTL=3600
REDIS_URL=redis://localhost:6379

# Logging Configuration (Production Admin)
LOG_LEVEL=info
LOG_FILE=./logs/admin.log

# Performance Monitoring (Production Admin)
ENABLE_PERFORMANCE_MONITORING=true
PERFORMANCE_LOG_THRESHOLD=2000

# Feature Flags (Production Admin)
ENABLE_EMAIL_VERIFICATION=true
ENABLE_SMS_NOTIFICATIONS=false
ENABLE_PUSH_NOTIFICATIONS=true
ENABLE_ANALYTICS=true

# API Configuration (Production Admin)
API_VERSION=v1
API_RATE_LIMIT=200
API_TIMEOUT=30000

# Security Headers (Production Admin)
ENABLE_HELMET=true
ENABLE_RATE_LIMITING=true
ENABLE_CORS=true

# Production Environment Specific (Admin)
DEBUG_MODE=false
ENABLE_SWAGGER=false
ENABLE_MORGAN_LOGGING=false
TEST_MODE=false
MOCK_EXTERNAL_APIS=false
DISABLE_EMAIL_SENDING=false
ADMIN_PANEL_MODE=true
EOF

# Step 4: Set proper permissions
echo "🔒 Setting file permissions..."
chown $DEPLOY_USER:$DEPLOY_USER "$DEPLOY_DIR/$ENV_FILE"
chown $DEPLOY_USER:$DEPLOY_USER "$DEPLOY_DIR/$ADMIN_ENV_FILE"
chmod 600 "$DEPLOY_DIR/$ENV_FILE"
chmod 600 "$DEPLOY_DIR/$ADMIN_ENV_FILE"

# Step 5: Create a simple PM2 ecosystem config that properly loads env files
echo "📝 Creating PM2 ecosystem configuration..."
cat > "$DEPLOY_DIR/ecosystem.config.js" << EOF
module.exports = {
  apps: [
    {
      name: '$MAIN_PROCESS',
      script: './server/index.js',
      cwd: '$DEPLOY_DIR',
      env: {
        NODE_ENV: '$ENVIRONMENT'
      },
      env_file: '$DEPLOY_DIR/$ENV_FILE',
      instances: 1,
      exec_mode: 'fork',
      watch: false,
      max_memory_restart: '1G',
      error_file: '/home/<USER>/.pm2/logs/$MAIN_PROCESS-error.log',
      out_file: '/home/<USER>/.pm2/logs/$MAIN_PROCESS-out.log',
      log_file: '/home/<USER>/.pm2/logs/$MAIN_PROCESS.log',
      time: true
    },
    {
      name: '$ADMIN_PROCESS',
      script: './server/admin-server.js',
      cwd: '$DEPLOY_DIR',
      env: {
        NODE_ENV: '$ENVIRONMENT'
      },
      env_file: '$DEPLOY_DIR/$ADMIN_ENV_FILE',
      instances: 1,
      exec_mode: 'fork',
      watch: false,
      max_memory_restart: '1G',
      error_file: '/home/<USER>/.pm2/logs/$ADMIN_PROCESS-error.log',
      out_file: '/home/<USER>/.pm2/logs/$ADMIN_PROCESS-out.log',
      log_file: '/home/<USER>/.pm2/logs/$ADMIN_PROCESS.log',
      time: true
    }
  ]
};
EOF

chown $DEPLOY_USER:$DEPLOY_USER "$DEPLOY_DIR/ecosystem.config.js"

# Step 6: Test environment loading manually
echo "🧪 Testing environment file loading..."
cd "$DEPLOY_DIR"

echo "Testing $ENV_FILE loading:"
sudo -u $DEPLOY_USER NODE_ENV=$ENVIRONMENT node -e "
require('dotenv').config({ path: '$ENV_FILE' });
console.log('NODE_ENV:', process.env.NODE_ENV);
console.log('DB_HOST:', process.env.DB_HOST);
console.log('DB_PORT:', process.env.DB_PORT);
console.log('SQUARE_ACCESS_TOKEN:', process.env.SQUARE_ACCESS_TOKEN ? 'CONFIGURED' : 'MISSING');
console.log('EMAIL_USER:', process.env.EMAIL_USER);
" 2>/dev/null || echo "❌ Failed to test environment loading"

echo ""

# Step 7: Start PM2 processes with explicit environment loading
echo "🚀 Starting PM2 processes..."
cd "$DEPLOY_DIR"

# Start main server
echo "Starting main server ($MAIN_PROCESS)..."
sudo -u $DEPLOY_USER pm2 start ecosystem.config.js --only $MAIN_PROCESS

# Start admin server
echo "Starting admin server ($ADMIN_PROCESS)..."
sudo -u $DEPLOY_USER pm2 start ecosystem.config.js --only $ADMIN_PROCESS

# Step 8: Wait and check status
echo "⏳ Waiting for processes to start..."
sleep 5

echo "📊 PM2 Status:"
sudo -u $DEPLOY_USER pm2 status

echo ""
echo "✅ PM2 Environment Loading Fix Complete!"
echo ""
echo "🔍 Verification commands:"
echo "sudo -u $DEPLOY_USER pm2 logs $MAIN_PROCESS --lines 20"
echo "sudo -u $DEPLOY_USER pm2 logs $ADMIN_PROCESS --lines 20"
echo "./verify-fix.sh"
