// Import models from database.js instead of individual files
const { models, sequelize } = require('./database');

// Import new models
const DataEnvironment = require('./DataEnvironment');
const MockData = require('./MockData');

// Extract individual models for backward compatibility
const User = models.User;
const Role = models.Role;
const Address = models.Address;
const Category = models.Category;
const Product = models.Product;
const Cart = models.Cart;
const CartItem = models.CartItem;
const Order = models.Order;
const OrderItem = models.OrderItem;
const Review = models.Review;
const Newsletter = models.Newsletter;
// const PushSubscription = models.PushSubscription;
// const Notification = models.Notification;
// const EmailCampaign = models.EmailCampaign;
// const Banner = models.Banner;
const Coupon = models.Coupon;
const CouponUsage = models.CouponUsage;
const EmailLog = models.EmailLog;
const SystemSetting = models.SystemSetting;
const Wishlist = models.Wishlist;
const AuditLog = models.AuditLog;

// Define associations

// Role associations
User.belongsTo(Role, { foreignKey: 'roleId', as: 'Role' });
Role.hasMany(User, { foreignKey: 'roleId', as: 'users' });

User.hasMany(Address, { foreignKey: 'userId', as: 'addresses' });
Address.belongsTo(User, { foreignKey: 'userId' });

User.hasOne(Cart, { foreignKey: 'userId' });
Cart.belongsTo(User, { foreignKey: 'userId' });

User.hasMany(Order, { foreignKey: 'userId', as: 'orders' });
Order.belongsTo(User, { foreignKey: 'userId', as: 'user' });

Order.hasMany(OrderItem, { foreignKey: 'orderId', as: 'items' });
OrderItem.belongsTo(Order, { foreignKey: 'orderId', as: 'order' });

Product.hasMany(OrderItem, { foreignKey: 'productId', as: 'orderItems' });
OrderItem.belongsTo(Product, { foreignKey: 'productId', as: 'product' });

Order.hasMany(CouponUsage, { foreignKey: 'orderId', as: 'couponUsages' });
CouponUsage.belongsTo(Order, { foreignKey: 'orderId', as: 'order' });

Coupon.hasMany(CouponUsage, { foreignKey: 'couponId', as: 'usages' });
CouponUsage.belongsTo(Coupon, { foreignKey: 'couponId', as: 'coupon' });

User.hasMany(CouponUsage, { foreignKey: 'userId', as: 'couponUsages' });
CouponUsage.belongsTo(User, { foreignKey: 'userId', as: 'user' });

// Cart associations
User.hasOne(Cart, { foreignKey: 'userId', as: 'cart' });
Cart.belongsTo(User, { foreignKey: 'userId', as: 'user' });

Cart.hasMany(CartItem, { foreignKey: 'cartId', as: 'items' });
CartItem.belongsTo(Cart, { foreignKey: 'cartId', as: 'cart' });

Product.hasMany(CartItem, { foreignKey: 'productId', as: 'cartItems' });
CartItem.belongsTo(Product, { foreignKey: 'productId', as: 'product' });

User.hasMany(Review, { foreignKey: 'userId', as: 'reviews' });
Review.belongsTo(User, { foreignKey: 'userId' });

Category.hasMany(Product, { foreignKey: 'categoryId', as: 'products' });
Product.belongsTo(Category, { foreignKey: 'categoryId', as: 'category' });

Category.hasMany(Product, { foreignKey: 'subcategoryId', as: 'subcategoryProducts' });
Product.belongsTo(Category, { foreignKey: 'subcategoryId', as: 'subcategory' });

Product.hasMany(Review, { foreignKey: 'productId', as: 'reviews' });
Review.belongsTo(Product, { foreignKey: 'productId' });

Order.hasMany(Review, { foreignKey: 'orderId', as: 'reviews' });
Review.belongsTo(Order, { foreignKey: 'orderId' });

// Newsletter associations
User.hasOne(Newsletter, { foreignKey: 'userId', as: 'newsletter' });
Newsletter.belongsTo(User, { foreignKey: 'userId', as: 'user' });

// Push subscription associations
// User.hasMany(PushSubscription, { foreignKey: 'userId', as: 'pushSubscriptions' });
// PushSubscription.belongsTo(User, { foreignKey: 'userId', as: 'user' });

// Notification associations
// User.hasMany(Notification, { foreignKey: 'userId', as: 'notifications' });
// Notification.belongsTo(User, { foreignKey: 'userId', as: 'user' });

// Email campaign associations
// User.hasMany(EmailCampaign, { foreignKey: 'createdBy', as: 'emailCampaigns' });
// EmailCampaign.belongsTo(User, { foreignKey: 'createdBy', as: 'creator' });

// Banner associations
// User.hasMany(Banner, { foreignKey: 'createdBy', as: 'createdBanners' });
// Banner.belongsTo(User, { foreignKey: 'createdBy', as: 'creator' });

// User.hasMany(Banner, { foreignKey: 'updatedBy', as: 'updatedBanners' });
// Banner.belongsTo(User, { foreignKey: 'updatedBy', as: 'updater' });

// Coupon associations
User.hasMany(Coupon, { foreignKey: 'createdBy', as: 'createdCoupons' });
Coupon.belongsTo(User, { foreignKey: 'createdBy', as: 'creator' });

// Wishlist associations
User.hasMany(Wishlist, { foreignKey: 'userId', as: 'wishlist' });
Wishlist.belongsTo(User, { foreignKey: 'userId', as: 'user' });
Product.hasMany(Wishlist, { foreignKey: 'productId', as: 'wishlists' });
Wishlist.belongsTo(Product, { foreignKey: 'productId', as: 'product' });

// AuditLog associations
User.hasMany(AuditLog, { foreignKey: 'userId', as: 'auditLogs' });
AuditLog.belongsTo(User, { foreignKey: 'userId', as: 'user' });

// Data Environment associations
User.hasMany(DataEnvironment, { foreignKey: 'userId', as: 'dataEnvironments' });
DataEnvironment.belongsTo(User, { foreignKey: 'userId', as: 'user' });

// Mock Data associations
User.hasMany(MockData, { foreignKey: 'createdBy', as: 'mockDataEntries' });
MockData.belongsTo(User, { foreignKey: 'createdBy', as: 'creator' });

module.exports = {
  sequelize,
  User,
  Role,
  Address,
  Category,
  Product,
  Cart,
  CartItem,
  Order,
  OrderItem,
  Review,
  Newsletter,
  // PushSubscription,
  // Notification,
  // EmailCampaign,
  // Banner,
  Coupon,
  CouponUsage,
  EmailLog,
  SystemSetting,
  Wishlist,
  AuditLog,
  DataEnvironment,
  MockData
};
