const { User, Notification, PushSubscription } = require('../models');
const webpush = require('web-push');
const { Op } = require('sequelize');

// Configure web-push (only if VAPID keys are provided)
if (process.env.VAPID_PUBLIC_KEY && process.env.VAPID_PRIVATE_KEY) {
  webpush.setVapidDetails(
    process.env.VAPID_EMAIL || 'mailto:<EMAIL>',
    process.env.VAPID_PUBLIC_KEY,
    process.env.VAPID_PRIVATE_KEY
  );
  console.log('✅ Web push notifications configured');
} else {
  console.log('⚠️ Web push notifications disabled - VAPID keys not configured');
}

// Subscribe user to push notifications
const subscribeToPush = async (req, res) => {
  try {
    const { subscription } = req.body;
    const userId = req.user.id;

    // Save or update subscription
    await PushSubscription.upsert({
      userId,
      endpoint: subscription.endpoint,
      p256dh: subscription.keys.p256dh,
      auth: subscription.keys.auth,
      isActive: true
    });

    res.json({
      success: true,
      message: 'Successfully subscribed to push notifications'
    });

  } catch (error) {
    console.error('Push subscription error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to subscribe to push notifications',
      error: error.message
    });
  }
};

// Unsubscribe user from push notifications
const unsubscribeFromPush = async (req, res) => {
  try {
    const userId = req.user.id;

    await PushSubscription.update(
      { isActive: false },
      { where: { userId } }
    );

    res.json({
      success: true,
      message: 'Successfully unsubscribed from push notifications'
    });

  } catch (error) {
    console.error('Push unsubscription error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to unsubscribe from push notifications',
      error: error.message
    });
  }
};

// Send push notification to specific users (Admin only)
const sendPushNotification = async (req, res) => {
  try {
    const { title, body, icon, url, userIds, segment } = req.body;

    let targetUsers = [];

    if (userIds && userIds.length > 0) {
      // Send to specific users
      targetUsers = userIds;
    } else if (segment) {
      // Send to user segment
      const users = await getUsersBySegment(segment);
      targetUsers = users.map(user => user.id);
    } else {
      // Send to all subscribed users
      const subscriptions = await PushSubscription.findAll({
        where: { isActive: true },
        attributes: ['userId']
      });
      targetUsers = subscriptions.map(sub => sub.userId);
    }

    // Get active subscriptions for target users
    const subscriptions = await PushSubscription.findAll({
      where: {
        userId: { [Op.in]: targetUsers },
        isActive: true
      }
    });

    const payload = JSON.stringify({
      title,
      body,
      icon: icon || '/images/logo-icon.png',
      url: url || '/',
      badge: '/images/badge-icon.png'
    });

    const sendPromises = subscriptions.map(async (subscription) => {
      try {
        const pushSubscription = {
          endpoint: subscription.endpoint,
          keys: {
            p256dh: subscription.p256dh,
            auth: subscription.auth
          }
        };

        await webpush.sendNotification(pushSubscription, payload);
        
        // Log successful notification
        await Notification.create({
          userId: subscription.userId,
          title,
          body,
          type: 'push',
          status: 'sent',
          sentAt: new Date()
        });

        return { userId: subscription.userId, status: 'sent' };
      } catch (error) {
        console.error(`Failed to send notification to user ${subscription.userId}:`, error);
        
        // If subscription is invalid, deactivate it
        if (error.statusCode === 410) {
          await subscription.update({ isActive: false });
        }

        await Notification.create({
          userId: subscription.userId,
          title,
          body,
          type: 'push',
          status: 'failed',
          error: error.message,
          sentAt: new Date()
        });

        return { userId: subscription.userId, status: 'failed', error: error.message };
      }
    });

    const results = await Promise.all(sendPromises);
    
    const successCount = results.filter(r => r.status === 'sent').length;
    const failureCount = results.filter(r => r.status === 'failed').length;

    res.json({
      success: true,
      message: `Push notification sent to ${successCount} users, ${failureCount} failed`,
      data: {
        totalSent: successCount,
        totalFailed: failureCount,
        results
      }
    });

  } catch (error) {
    console.error('Send push notification error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to send push notification',
      error: error.message
    });
  }
};

// Get notification history (Admin only)
const getNotificationHistory = async (req, res) => {
  try {
    const { page = 1, limit = 20, type = 'all', status = 'all' } = req.query;
    const offset = (page - 1) * limit;

    let whereClause = {};
    
    if (type !== 'all') {
      whereClause.type = type;
    }
    
    if (status !== 'all') {
      whereClause.status = status;
    }

    const notifications = await Notification.findAndCountAll({
      where: whereClause,
      include: [
        {
          model: User,
          attributes: ['id', 'firstName', 'lastName', 'email']
        }
      ],
      order: [['sentAt', 'DESC']],
      limit: parseInt(limit),
      offset: parseInt(offset)
    });

    res.json({
      success: true,
      data: {
        notifications: notifications.rows,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(notifications.count / limit),
          totalNotifications: notifications.count,
          limit: parseInt(limit)
        }
      }
    });

  } catch (error) {
    console.error('Get notification history error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch notification history',
      error: error.message
    });
  }
};

// Get push notification statistics (Admin only)
const getPushStats = async (req, res) => {
  try {
    const totalSubscriptions = await PushSubscription.count({
      where: { isActive: true }
    });

    const totalSent = await Notification.count({
      where: { type: 'push', status: 'sent' }
    });

    const totalFailed = await Notification.count({
      where: { type: 'push', status: 'failed' }
    });

    // Get recent notification performance
    const recentNotifications = await Notification.findAll({
      attributes: [
        'title',
        [sequelize.fn('COUNT', sequelize.col('id')), 'totalSent'],
        [sequelize.fn('SUM', sequelize.literal("CASE WHEN status = 'sent' THEN 1 ELSE 0 END")), 'successful'],
        'sentAt'
      ],
      where: {
        type: 'push',
        sentAt: {
          [Op.gte]: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // Last 30 days
        }
      },
      group: ['title', 'sentAt'],
      order: [['sentAt', 'DESC']],
      limit: 10
    });

    res.json({
      success: true,
      data: {
        totalSubscriptions,
        totalSent,
        totalFailed,
        successRate: totalSent > 0 ? ((totalSent / (totalSent + totalFailed)) * 100).toFixed(2) : 0,
        recentNotifications
      }
    });

  } catch (error) {
    console.error('Get push stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch push notification statistics',
      error: error.message
    });
  }
};

// Helper function to get users by segment
const getUsersBySegment = async (segment) => {
  const customers = await User.findAll({
    where: { role: 'customer' },
    include: [
      {
        model: Order,
        attributes: [
          [sequelize.fn('COUNT', sequelize.col('Orders.id')), 'orderCount'],
          [sequelize.fn('SUM', sequelize.col('Orders.total')), 'totalSpent']
        ],
        required: false
      }
    ],
    group: ['User.id']
  });

  return customers.filter(customer => {
    const orderCount = parseInt(customer.Orders?.[0]?.dataValues?.orderCount || 0);
    const totalSpent = parseFloat(customer.Orders?.[0]?.dataValues?.totalSpent || 0);
    
    switch (segment) {
      case 'new':
        return orderCount === 0;
      case 'returning':
        return orderCount > 0 && orderCount <= 5;
      case 'loyal':
        return orderCount > 5;
      case 'high_value':
        return totalSpent > 500;
      default:
        return true;
    }
  });
};

module.exports = {
  subscribeToPush,
  unsubscribeFromPush,
  sendPushNotification,
  getNotificationHistory,
  getPushStats,
  getUsersBySegment
};
