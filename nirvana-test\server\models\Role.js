const { DataTypes } = require('sequelize');
const { sequelize } = require('./database');

const Role = sequelize.define('Role', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  name: {
    type: DataTypes.STRING(50),
    allowNull: false,
    unique: true,
    validate: {
      len: [1, 50],
      isIn: [['admin', 'manager', 'customer']]
    }
  },
  displayName: {
    type: DataTypes.STRING(100),
    allowNull: false,
    field: 'display_name',
    validate: {
      len: [1, 100]
    }
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  permissions: {
    type: DataTypes.JSON,
    allowNull: false,
    defaultValue: {},
    comment: 'JSON object containing role permissions'
  },
  isActive: {
    type: DataTypes.BOOLEAN,
    defaultValue: true,
    field: 'is_active'
  },
  isSystemRole: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    field: 'is_system_role',
    comment: 'System roles cannot be deleted or have their core permissions modified'
  },
  priority: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    comment: 'Higher priority roles have more access (admin=100, manager=50, customer=10)'
  }
}, {
  tableName: 'roles',
  indexes: [
    {
      fields: ['name'],
      unique: true
    },
    {
      fields: ['is_active']
    },
    {
      fields: ['priority']
    }
  ]
});

// Static method to get default permissions for each role
Role.getDefaultPermissions = function(roleName) {
  const permissions = {
    admin: {
      // Product permissions
      products: {
        view: true,
        create: true,
        edit: true,
        delete: true,
        manageImages: true,
        manageCategories: true,
        manageInventory: true
      },
      // User management permissions
      users: {
        view: true,
        create: true,
        edit: true,
        delete: true,
        inviteManagers: true,
        viewCustomers: true
      },
      // Order permissions
      orders: {
        view: true,
        edit: true,
        delete: true,
        manage: true,
        refund: true,
        cancel: true
      },
      // Analytics and reporting
      analytics: {
        view: true,
        export: true,
        viewReports: true
      },
      // System permissions
      system: {
        viewLogs: true,
        viewAuditLogs: true,
        accessSettings: true,
        manageRoles: true,
        systemBackup: true
      },
      // Category and inventory permissions
      categories: {
        view: true,
        create: true,
        edit: true,
        delete: true
      },
      inventory: {
        view: true,
        edit: true,
        manage: true
      }
    },
    manager: {
      // Product permissions (read-only)
      products: {
        view: true,
        create: false,
        edit: false,
        delete: false,
        manageImages: false,
        manageCategories: false,
        manageInventory: false
      },
      // User management permissions (limited)
      users: {
        view: true,
        create: false,
        edit: false,
        delete: false,
        inviteManagers: false,
        viewCustomers: true
      },
      // Order permissions (full management)
      orders: {
        view: true,
        edit: true,
        delete: false,
        manage: true,
        refund: true,
        cancel: true
      },
      // Analytics and reporting (limited)
      analytics: {
        view: true,
        export: false,
        viewReports: true
      },
      // System permissions (very limited)
      system: {
        viewLogs: false,
        viewAuditLogs: false,
        accessSettings: false,
        manageRoles: false,
        systemBackup: false
      },
      // Category and inventory permissions (read-only)
      categories: {
        view: true,
        create: false,
        edit: false,
        delete: false
      },
      inventory: {
        view: true,
        edit: false,
        manage: false
      }
    },
    customer: {
      // Product permissions (view only)
      products: {
        view: true,
        create: false,
        edit: false,
        delete: false,
        manageImages: false,
        manageCategories: false,
        manageInventory: false
      },
      // User management permissions (none)
      users: {
        view: false,
        create: false,
        edit: false,
        delete: false,
        inviteManagers: false,
        viewCustomers: false
      },
      // Order permissions (own orders only)
      orders: {
        view: true, // Own orders only
        edit: false,
        delete: false,
        manage: false,
        refund: false,
        cancel: false
      },
      // Analytics and reporting (none)
      analytics: {
        view: false,
        export: false,
        viewReports: false
      },
      // System permissions (none)
      system: {
        viewLogs: false,
        viewAuditLogs: false,
        accessSettings: false,
        manageRoles: false,
        systemBackup: false
      },
      // Category and inventory permissions (view only)
      categories: {
        view: true,
        create: false,
        edit: false,
        delete: false
      },
      inventory: {
        view: false,
        edit: false,
        manage: false
      }
    }
  };

  return permissions[roleName] || permissions.customer;
};

// Instance method to check if role has specific permission
Role.prototype.hasPermission = function(category, action) {
  if (!this.permissions || !this.permissions[category]) {
    return false;
  }
  return this.permissions[category][action] === true;
};

// Static method to seed default roles
Role.seedDefaultRoles = async function() {
  const defaultRoles = [
    {
      name: 'admin',
      displayName: 'Administrator',
      description: 'Full system access with all permissions',
      permissions: this.getDefaultPermissions('admin'),
      isSystemRole: true,
      priority: 100
    },
    {
      name: 'manager',
      displayName: 'Manager',
      description: 'Limited access for order management and customer service',
      permissions: this.getDefaultPermissions('manager'),
      isSystemRole: true,
      priority: 50
    },
    {
      name: 'customer',
      displayName: 'Customer',
      description: 'Standard customer access for shopping and account management',
      permissions: this.getDefaultPermissions('customer'),
      isSystemRole: true,
      priority: 10
    }
  ];

  for (const roleData of defaultRoles) {
    await this.findOrCreate({
      where: { name: roleData.name },
      defaults: roleData
    });
  }
};

// Static method to get role by name
Role.getByName = async function(roleName) {
  return await this.findOne({
    where: {
      name: roleName,
      isActive: true
    }
  });
};

// Static method to get all active roles
Role.getActiveRoles = async function() {
  return await this.findAll({
    where: { isActive: true },
    order: [['priority', 'DESC']]
  });
};

module.exports = Role;
