#!/bin/bash
# Make script executable: chmod +x comprehensive-admin-fix.sh

# Nirvana Organics - Comprehensive Admin Fix
# Fixes both Nginx admin assets 404 errors and database schema issues

set -e

echo "🔧 Nirvana Organics - Comprehensive Admin Fix"
echo "=============================================="

# Color codes
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}✅ $1${NC}"
}

warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if running as root for Nginx operations
if [[ $EUID -ne 0 ]]; then
   error "This script must be run as root (use sudo)"
   echo "Please run: sudo $0"
   exit 1
fi

# Configuration variables
NGINX_SITE_NAME="nirvana-organics-test"
NGINX_CONFIG_FILE="nirvana-test/nginx-site.conf"
SITES_AVAILABLE="/etc/nginx/sites-available"
SITES_ENABLED="/etc/nginx/sites-enabled"
TARGET_ADMIN="/var/www/nirvana-frontend-test/admin"
MISSING_JS="admin-DitisM-I.js"
MISSING_CSS="admin-TY7ZtfqV.css"

echo ""
echo "🎯 COMPREHENSIVE FIX PLAN"
echo "========================="
echo "1. Fix Nginx location block priority for admin assets"
echo "2. Deploy updated Nginx configuration"
echo "3. Fix database schema (add missing is_active column to products)"
echo "4. Restart services"
echo "5. Test all fixes"

# Step 1: Verify admin assets exist
log "Step 1: Verifying admin assets exist..."

if [ -f "$TARGET_ADMIN/assets/$MISSING_JS" ] && [ -f "$TARGET_ADMIN/assets/$MISSING_CSS" ]; then
    success "Admin assets exist on filesystem"
else
    error "Admin assets missing - please run asset deployment first"
    echo "Run: sudo -u Nirvana ./nirvana-test/fix-admin-404-assets.sh"
    exit 1
fi

# Step 2: Deploy updated Nginx configuration
log "Step 2: Deploying updated Nginx configuration..."

if [ -f "$NGINX_CONFIG_FILE" ]; then
    # Backup current configuration
    if [ -f "$SITES_AVAILABLE/$NGINX_SITE_NAME" ]; then
        cp "$SITES_AVAILABLE/$NGINX_SITE_NAME" "$SITES_AVAILABLE/$NGINX_SITE_NAME.backup.$(date +%Y%m%d_%H%M%S)"
        success "Current Nginx configuration backed up"
    fi
    
    # Copy new configuration
    cp "$NGINX_CONFIG_FILE" "$SITES_AVAILABLE/$NGINX_SITE_NAME"
    chmod 644 "$SITES_AVAILABLE/$NGINX_SITE_NAME"
    chown root:root "$SITES_AVAILABLE/$NGINX_SITE_NAME"
    
    # Ensure site is enabled
    if [[ -L "$SITES_ENABLED/$NGINX_SITE_NAME" ]]; then
        rm "$SITES_ENABLED/$NGINX_SITE_NAME"
    fi
    ln -s "$SITES_AVAILABLE/$NGINX_SITE_NAME" "$SITES_ENABLED/$NGINX_SITE_NAME"
    
    success "Updated Nginx configuration deployed"
else
    error "Nginx configuration file not found: $NGINX_CONFIG_FILE"
    exit 1
fi

# Step 3: Test Nginx configuration
log "Step 3: Testing Nginx configuration..."

nginx -t
if [[ $? -eq 0 ]]; then
    success "Nginx configuration test passed"
else
    error "Nginx configuration test failed"
    exit 1
fi

# Step 4: Reload Nginx
log "Step 4: Reloading Nginx..."

systemctl reload nginx
if [[ $? -eq 0 ]]; then
    success "Nginx reloaded successfully"
else
    error "Failed to reload Nginx"
    exit 1
fi

# Wait for reload to complete
sleep 2

# Step 5: Fix database schema
log "Step 5: Fixing database schema..."

# Change to the correct user for database operations
sudo -u Nirvana node nirvana-test/fix-products-is-active-column.js
if [[ $? -eq 0 ]]; then
    success "Database schema fixed"
else
    warning "Database schema fix encountered issues (may already be correct)"
fi

# Step 6: Restart backend services
log "Step 6: Restarting backend services..."

sudo -u Nirvana pm2 restart nirvana-backend-main-test 2>/dev/null || warning "Main backend restart failed"
sudo -u Nirvana pm2 restart nirvana-backend-admin-test 2>/dev/null || warning "Admin backend restart failed"

# Wait for services to start
sleep 5

# Check service status
if sudo -u Nirvana pm2 list | grep -q "nirvana-backend.*online"; then
    success "Backend services are running"
else
    warning "Some backend services may not be running properly"
fi

# Step 7: Test fixes
log "Step 7: Testing all fixes..."

echo ""
echo "🌐 HTTP Tests:"

# Test admin.html
echo "Testing admin.html:"
ADMIN_HTML_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "https://test.shopnirvanaorganics.com/admin/" 2>/dev/null || echo "000")
if [ "$ADMIN_HTML_STATUS" = "200" ]; then
    success "admin.html accessible (HTTP $ADMIN_HTML_STATUS)"
else
    error "admin.html not accessible (HTTP $ADMIN_HTML_STATUS)"
fi

# Test JavaScript asset
echo "Testing JavaScript asset:"
JS_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "https://test.shopnirvanaorganics.com/admin/assets/$MISSING_JS" 2>/dev/null || echo "000")
if [ "$JS_STATUS" = "200" ]; then
    success "JavaScript asset accessible (HTTP $JS_STATUS)"
else
    error "JavaScript asset not accessible (HTTP $JS_STATUS)"
fi

# Test CSS asset
echo "Testing CSS asset:"
CSS_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "https://test.shopnirvanaorganics.com/admin/assets/$MISSING_CSS" 2>/dev/null || echo "000")
if [ "$CSS_STATUS" = "200" ]; then
    success "CSS asset accessible (HTTP $CSS_STATUS)"
else
    error "CSS asset not accessible (HTTP $CSS_STATUS)"
fi

# Test database query
echo "Testing database query:"
DB_TEST_RESULT=$(sudo -u Nirvana node -e "
const { Sequelize } = require('sequelize');
require('dotenv').config({ path: './nirvana-test/.env.test' });
const sequelize = new Sequelize(process.env.DB_NAME, process.env.DB_USER, process.env.DB_PASSWORD, {
  host: process.env.DB_HOST,
  dialect: 'mysql',
  logging: false
});
sequelize.query('SELECT COUNT(*) as count FROM products WHERE is_active = 1')
  .then(([results]) => console.log('SUCCESS:' + results[0].count))
  .catch(err => console.log('ERROR:' + err.message))
  .finally(() => sequelize.close());
" 2>/dev/null || echo "ERROR:Connection failed")

if [[ "$DB_TEST_RESULT" == SUCCESS:* ]]; then
    PRODUCT_COUNT=$(echo "$DB_TEST_RESULT" | cut -d: -f2)
    success "Database query successful - found $PRODUCT_COUNT active products"
else
    error "Database query failed: $(echo "$DB_TEST_RESULT" | cut -d: -f2)"
fi

# Step 8: Final summary
log "Step 8: Final summary"

echo ""
echo "📋 COMPREHENSIVE FIX SUMMARY"
echo "============================"

FIXES_SUCCESSFUL=0
TOTAL_FIXES=4

if [ "$JS_STATUS" = "200" ] && [ "$CSS_STATUS" = "200" ]; then
    success "✅ Admin assets are now accessible"
    ((FIXES_SUCCESSFUL++))
else
    error "❌ Admin assets still have issues"
fi

if [ "$ADMIN_HTML_STATUS" = "200" ]; then
    success "✅ Admin panel HTML is accessible"
    ((FIXES_SUCCESSFUL++))
else
    error "❌ Admin panel HTML has issues"
fi

if [[ "$DB_TEST_RESULT" == SUCCESS:* ]]; then
    success "✅ Database schema is fixed"
    ((FIXES_SUCCESSFUL++))
else
    error "❌ Database schema still has issues"
fi

if sudo -u Nirvana pm2 list | grep -q "nirvana-backend.*online"; then
    success "✅ Backend services are running"
    ((FIXES_SUCCESSFUL++))
else
    error "❌ Backend services have issues"
fi

echo ""
echo "📊 RESULTS: $FIXES_SUCCESSFUL/$TOTAL_FIXES fixes successful"

if [ $FIXES_SUCCESSFUL -eq $TOTAL_FIXES ]; then
    success "🎉 ALL FIXES SUCCESSFUL!"
    echo ""
    echo "✅ Admin assets HTTP 404 errors should be resolved"
    echo "✅ Database 'is_active' column errors should be resolved"
    echo "✅ Admin panel should load completely with styling and functionality"
else
    error "❌ Some fixes failed - manual intervention may be required"
fi

echo ""
echo "🔍 VERIFICATION STEPS"
echo "===================="
echo "1. Open browser: https://test.shopnirvanaorganics.com/admin/"
echo "2. Check browser console for 404 errors (should be resolved)"
echo "3. Verify admin panel loads with proper styling"
echo "4. Test admin functionality (login, product management, etc.)"

echo ""
echo "📊 ASSET URLS"
echo "============="
echo "JavaScript: https://test.shopnirvanaorganics.com/admin/assets/$MISSING_JS"
echo "CSS: https://test.shopnirvanaorganics.com/admin/assets/$MISSING_CSS"

echo ""
success "🎉 Comprehensive admin fix completed!"
