<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />

    <!-- Primary Meta Tags -->
    <title>Nirvana Organics - Premium Hemp-Derived Cannabis Products | Lab-Tested & Legal</title>
    <meta name="title" content="Nirvana Organics - Premium Hemp-Derived Cannabis Products | Lab-Tested & Legal" />
    <meta name="description" content="Discover premium hemp-derived cannabis products including flowers, chocolates, pre-rolls, diamond sauce, and vapes. Lab-tested, legal, and delivered nationwide with free shipping over $100." />
    <meta name="keywords" content="hemp products, cannabis products, CBD, THC-A, Delta-8, Delta-9, hemp flowers, cannabis chocolates, pre-rolls, diamond sauce, vapes, legal cannabis, lab tested, organic hemp" />
    <meta name="author" content="Nirvana Organics" />
    <meta name="robots" content="index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1" />
    <meta name="googlebot" content="index, follow" />
    <meta name="bingbot" content="index, follow" />

    <!-- Viewport and Compatibility -->
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=yes, viewport-fit=cover" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="format-detection" content="telephone=no" />

    <!-- Favicon and Icons -->
    <link rel="icon" type="image/png" href="/Nirvana_logo.png" />

    <!-- Apple Touch Icons -->
    <link rel="apple-touch-icon" href="/Nirvana_logo.png" />

    <!-- Android/PWA Icons -->
    <link rel="icon" type="image/png" sizes="192x192" href="/Nirvana_logo.png" />
    <link rel="icon" type="image/png" sizes="512x512" href="/Nirvana_logo.png" />

    <!-- Microsoft Tiles -->
    <meta name="msapplication-TileImage" content="/Nirvana_logo.png" />
    <meta name="msapplication-config" content="/browserconfig.xml" />

    <!-- Web App Manifest -->
    <link rel="manifest" href="/site.webmanifest" />

    <!-- Theme and App Configuration -->
    <meta name="theme-color" content="#16a34a" />
    <meta name="msapplication-TileColor" content="#16a34a" />
    <meta name="mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="default" />
    <meta name="apple-mobile-web-app-title" content="Nirvana Organics" />

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://nirvanaorganics.com/" />
    <meta property="og:title" content="Nirvana Organics - Premium Hemp-Derived Cannabis Products" />
    <meta property="og:description" content="Discover premium hemp-derived cannabis products including flowers, chocolates, pre-rolls, diamond sauce, and vapes. Lab-tested, legal, and delivered nationwide." />
    <meta property="og:image" content="https://nirvanaorganics.com/images/og-image.jpg" />
    <meta property="og:image:width" content="1200" />
    <meta property="og:image:height" content="630" />
    <meta property="og:site_name" content="Nirvana Organics" />
    <meta property="og:locale" content="en_US" />

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:url" content="https://nirvanaorganics.com/" />
    <meta property="twitter:title" content="Nirvana Organics - Premium Hemp-Derived Cannabis Products" />
    <meta property="twitter:description" content="Discover premium hemp-derived cannabis products including flowers, chocolates, pre-rolls, diamond sauce, and vapes. Lab-tested, legal, and delivered nationwide." />
    <meta property="twitter:image" content="https://nirvanaorganics.com/images/og-image.jpg" />
    <meta property="twitter:site" content="@nirvanaorganics" />
    <meta property="twitter:creator" content="@nirvanaorganics" />

    <!-- Canonical URL -->
    <link rel="canonical" href="https://nirvanaorganics.com/" />

    <!-- Additional SEO Meta Tags -->
    <meta name="language" content="English" />
    <meta name="revisit-after" content="7 days" />
    <meta name="distribution" content="global" />
    <meta name="rating" content="general" />
    <meta name="copyright" content="© 2024 Nirvana Organics. All rights reserved." />
    <meta name="publisher" content="Nirvana Organics" />
    <meta name="geo.region" content="US" />
    <meta name="geo.placename" content="Denver, Colorado" />
    <meta name="geo.position" content="39.7392;-104.9903" />
    <meta name="ICBM" content="39.7392, -104.9903" />
    
    <!-- Preconnect to External Domains for Performance -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link rel="preconnect" href="https://www.google-analytics.com" />
    <link rel="dns-prefetch" href="https://connect.squareup.com" />

    <!-- Preload critical fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet" />

    <!-- Structured Data - Organization -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "Organization",
      "name": "Nirvana Organics",
      "url": "https://nirvanaorganics.com",
      "logo": "https://nirvanaorganics.com/Nirvana_logo.png",
      "description": "Premium hemp-derived cannabis products including flowers, chocolates, pre-rolls, diamond sauce, and vapes. Lab-tested, legal, and delivered nationwide.",
      "address": {
        "@type": "PostalAddress",
        "streetAddress": "123 Wellness Way",
        "addressLocality": "Denver",
        "addressRegion": "CO",
        "postalCode": "80202",
        "addressCountry": "US"
      },
      "contactPoint": {
        "@type": "ContactPoint",
        "telephone": "******-123-4567",
        "contactType": "customer service",
        "email": "<EMAIL>",
        "availableLanguage": "English",
        "areaServed": "US"
      },
      "sameAs": [
        "https://facebook.com/nirvanaorganics",
        "https://instagram.com/nirvanaorganics",
        "https://twitter.com/nirvanaorganics"
      ],
      "foundingDate": "2020",
      "numberOfEmployees": "10-50",
      "slogan": "Premium Hemp-Derived Cannabis Products"
    }
    </script>

    <!-- Structured Data - Website -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "WebSite",
      "name": "Nirvana Organics",
      "url": "https://nirvanaorganics.com",
      "description": "Premium hemp-derived cannabis products including flowers, chocolates, pre-rolls, diamond sauce, and vapes.",
      "publisher": {
        "@type": "Organization",
        "name": "Nirvana Organics"
      },
      "potentialAction": {
        "@type": "SearchAction",
        "target": "https://nirvanaorganics.com/shop?search={search_term_string}",
        "query-input": "required name=search_term_string"
      }
    }
    </script>

    <!-- Structured Data - Store -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "Store",
      "name": "Nirvana Organics",
      "description": "Premium hemp-derived cannabis products including flowers, chocolates, pre-rolls, diamond sauce, and vapes.",
      "url": "https://nirvanaorganics.com",
      "image": "https://nirvanaorganics.com/images/hero-banner-1.jpg",
      "priceRange": "$10-$200",
      "paymentAccepted": ["Credit Card", "PayPal"],
      "currenciesAccepted": "USD",
      "address": {
        "@type": "PostalAddress",
        "streetAddress": "123 Wellness Way",
        "addressLocality": "Denver",
        "addressRegion": "CO",
        "postalCode": "80202",
        "addressCountry": "US"
      },
      "hasOfferCatalog": {
        "@type": "OfferCatalog",
        "name": "Hemp Products",
        "itemListElement": [
          {
            "@type": "Offer",
            "itemOffered": {
              "@type": "Product",
              "name": "Hemp Flowers",
              "category": "Cannabis Products"
            }
          },
          {
            "@type": "Offer",
            "itemOffered": {
              "@type": "Product",
              "name": "Cannabis Chocolates",
              "category": "Edibles"
            }
          },
          {
            "@type": "Offer",
            "itemOffered": {
              "@type": "Product",
              "name": "Pre-Rolls",
              "category": "Cannabis Products"
            }
          },
          {
            "@type": "Offer",
            "itemOffered": {
              "@type": "Product",
              "name": "Diamond Sauce",
              "category": "Concentrates"
            }
          },
          {
            "@type": "Offer",
            "itemOffered": {
              "@type": "Product",
              "name": "Vapes",
              "category": "Vaporizers"
            }
          }
        ]
      }
    }
    </script>
    
    <!-- Critical CSS to prevent FOUC -->
    <style>
      /* Critical CSS to prevent flickering */
      html {
        font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        text-rendering: optimizeLegibility;
      }
      
      /* Show content immediately, prevent loading issues */
      html, body, #root {
        visibility: visible !important;
        opacity: 1 !important;
      }
      
      /* Disable transitions during page load */
      .preload * {
        transition: none !important;
        animation: none !important;
      }
      
      /* Prevent layout shift */
      body {
        margin: 0;
        padding: 0;
        min-height: 100vh;
        background-color: #f9fafb;
        color: #374151;
        line-height: 1.6;
        font-family: inherit;
      }
      
      /* Loading state for root */
      #root {
        min-height: 100vh;
      }
      
      /* Prevent image flickering */
      img {
        max-width: 100%;
        height: auto;
        display: block;
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
      }
    </style>
    <script type="module" crossorigin src="/assets/index-CbyAb8ew.js"></script>
    <link rel="modulepreload" crossorigin href="/assets/vendor-DavUf6mE.js">
    <link rel="modulepreload" crossorigin href="/assets/router-HnPUWn0z.js">
    <link rel="modulepreload" crossorigin href="/assets/redux-fT99MgG6.js">
    <link rel="stylesheet" crossorigin href="/assets/index-B4AiK1X4.css">
  </head>
  <body>
    <div id="root"></div>
    
    <!-- Enhanced loading completion handling -->
    <script>
      // Comprehensive loading completion handler
      function markAsLoaded() {
        document.documentElement.classList.add('loaded');
        document.body.classList.add('loaded');

        // Stop any loading indicators
        if (window.stop) {
          try {
            window.stop();
          } catch (e) {
            // Ignore errors in some browsers
          }
        }

        // Dispatch custom loaded event
        window.dispatchEvent(new CustomEvent('app-loaded', {
          detail: { timestamp: Date.now() }
        }));
      }

      // Multiple event listeners to ensure loading completion
      window.addEventListener('load', markAsLoaded);
      window.addEventListener('DOMContentLoaded', markAsLoaded);

      // React app loaded event
      window.addEventListener('load', function() {
        markAsLoaded();
      });

      // Fallback timeout to force completion
      setTimeout(markAsLoaded, 2000);

      // Additional fallback for immediate completion
      if (document.readyState === 'complete') {
        setTimeout(markAsLoaded, 100);
      }
    </script>
  </body>
</html>
