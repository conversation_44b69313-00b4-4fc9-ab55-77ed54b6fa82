#!/usr/bin/env node
/**
 * Nirvana Organics - Comprehensive Database Management Script
 * Unified script for all database operations: connection testing, schema initialization,
 * table verification, environment debugging, and troubleshooting
 */

const path = require('path');
const fs = require('fs');

// Color codes for console output
const colors = {
  reset: '\033[0m',
  red: '\033[0;31m',
  green: '\033[0;32m',
  yellow: '\033[1;33m',
  blue: '\033[0;34m',
  cyan: '\033[0;36m',
  white: '\033[1;37m'
};

// Logging functions
const log = (message) => console.log(`${colors.blue}[${new Date().toISOString()}]${colors.reset} ${message}`);
const success = (message) => console.log(`${colors.green}✅ ${message}${colors.reset}`);
const warning = (message) => console.log(`${colors.yellow}⚠️  ${message}${colors.reset}`);
const error = (message) => console.log(`${colors.red}❌ ${message}${colors.reset}`);
const info = (message) => console.log(`${colors.cyan}ℹ️  ${message}${colors.reset}`);

class DatabaseManager {
  constructor() {
    this.sequelize = null;
    this.models = null;
    this.environment = process.env.NODE_ENV || 'development';
    this.envFile = this.getEnvironmentFile();
    this.loadEnvironment();
  }

  getEnvironmentFile() {
    switch (this.environment) {
      case 'test': return '.env.test';
      case 'production': return '.env.production';
      case 'admin': return '.env.admin';
      default: return '.env';
    }
  }

  loadEnvironment() {
    const envPath = path.join(__dirname, this.envFile);
    
    if (!fs.existsSync(envPath)) {
      error(`Environment file not found: ${this.envFile}`);
      process.exit(1);
    }

    require('dotenv').config({ path: envPath });
    
    log(`Environment loaded: ${this.environment}`);
    log(`Environment file: ${this.envFile}`);
    log(`Database host: ${process.env.DB_HOST}`);
    log(`Database name: ${process.env.DB_NAME}`);
    log(`Database user: ${process.env.DB_USER}`);
  }

  async initializeSequelize() {
    if (!this.sequelize) {
      try {
        const { sequelize, models } = require('./server/models/database');
        this.sequelize = sequelize;
        this.models = models;
      } catch (err) {
        error(`Failed to load database models: ${err.message}`);
        throw err;
      }
    }
  }

  async testConnection() {
    log('Testing database connection...');
    
    try {
      await this.initializeSequelize();
      await this.sequelize.authenticate();
      success('Database connection successful');
      
      // Test basic query
      const [results] = await this.sequelize.query('SELECT 1 as test');
      if (results[0].test === 1) {
        success('Database query test passed');
      }
      
      return true;
    } catch (err) {
      error(`Database connection failed: ${err.message}`);
      
      // Provide specific troubleshooting based on error type
      if (err.message.includes('ECONNREFUSED')) {
        if (err.message.includes('::1:3306')) {
          error('IPv6 localhost connection detected!');
          console.log('');
          console.log('🔧 IPv6 Localhost Fix:');
          console.log('  This error occurs when the database connection defaults to IPv6 localhost');
          console.log('  instead of using the configured database host.');
          console.log('');
          console.log('  Solutions:');
          console.log('  1. Verify DB_HOST is set correctly in environment file');
          console.log('  2. Check that environment variables are loading properly');
          console.log('  3. Ensure no localhost/127.0.0.1 references in config');
        } else {
          console.log('');
          console.log('🔧 Connection Refused Troubleshooting:');
          console.log(`  - Database host: ${process.env.DB_HOST}`);
          console.log(`  - Database port: ${process.env.DB_PORT || 3306}`);
          console.log('  - Check if database server is running');
          console.log('  - Verify firewall settings');
          console.log('  - Test with: ping ' + process.env.DB_HOST);
        }
      } else if (err.message.includes('Access denied')) {
        console.log('');
        console.log('🔧 Access Denied Troubleshooting:');
        console.log(`  - Database user: ${process.env.DB_USER}`);
        console.log('  - Check database password');
        console.log('  - Verify user permissions');
        console.log('  - Test with: mysql -h ' + process.env.DB_HOST + ' -u ' + process.env.DB_USER + ' -p');
      } else if (err.message.includes('Unknown database')) {
        console.log('');
        console.log('🔧 Unknown Database Troubleshooting:');
        console.log(`  - Database name: ${process.env.DB_NAME}`);
        console.log('  - Create database if it doesn\'t exist');
        console.log('  - Check database name spelling');
      }
      
      return false;
    }
  }

  async checkTables() {
    log('Checking database tables...');
    
    try {
      await this.initializeSequelize();
      const queryInterface = this.sequelize.getQueryInterface();
      const tables = await queryInterface.showAllTables();
      
      console.log('');
      console.log(`📊 Found ${tables.length} tables in database:`);
      
      if (tables.length === 0) {
        error('No tables found in database!');
        console.log('');
        console.log('🔧 Database schema needs to be initialized.');
        console.log('   Run: node database-manager.js init');
        return { success: false, tables: [], missing: [] };
      }
      
      // List all tables with record counts
      for (let i = 0; i < tables.length; i++) {
        const table = tables[i];
        try {
          const [results] = await this.sequelize.query(`SELECT COUNT(*) as count FROM \`${table}\``);
          const count = results[0].count;
          console.log(`${i + 1}. ${table} (${count} records)`);
        } catch (err) {
          console.log(`${i + 1}. ${table} (error counting records)`);
        }
      }
      
      // Check for required tables
      const requiredTables = ['Products', 'Categories', 'Users', 'Orders', 'OrderItems', 'Carts', 'CartItems'];
      const missingTables = requiredTables.filter(table => !tables.includes(table));
      
      console.log('');
      console.log('🔍 Required Tables Check:');
      requiredTables.forEach(table => {
        if (tables.includes(table)) {
          success(`${table} - EXISTS`);
        } else {
          error(`${table} - MISSING`);
        }
      });
      
      if (missingTables.length > 0) {
        console.log('');
        error(`Missing ${missingTables.length} required tables: ${missingTables.join(', ')}`);
        console.log('🔧 To fix this, run: node database-manager.js init');
        return { success: false, tables, missing: missingTables };
      }
      
      success('All required tables are present');
      return { success: true, tables, missing: [] };
      
    } catch (err) {
      error(`Table check failed: ${err.message}`);
      return { success: false, tables: [], missing: [], error: err.message };
    }
  }

  async testProductsTable() {
    log('Testing Products table functionality...');
    
    try {
      await this.initializeSequelize();
      
      // Test basic Products table query
      const [countResults] = await this.sequelize.query('SELECT COUNT(*) as count FROM Products');
      const productCount = countResults[0].count;
      console.log(`📊 Products table contains ${productCount} records`);
      
      // Test the specific JOIN query that was failing in the logs
      const [joinResults] = await this.sequelize.query(`
        SELECT p.id, p.name, p.price, c.name as category_name 
        FROM Products p 
        LEFT JOIN Categories c ON p.category_id = c.id 
        WHERE p.is_active = 1 
        ORDER BY p.sales_count DESC 
        LIMIT 5
      `);
      
      success(`Products JOIN query successful - returned ${joinResults.length} results`);
      
      if (joinResults.length > 0) {
        console.log('📋 Sample products:');
        joinResults.forEach((product, index) => {
          console.log(`  ${index + 1}. ${product.name} - $${product.price} (Category: ${product.category_name || 'None'})`);
        });
      } else {
        info('No products found (table is empty but structure is correct)');
      }
      
      return true;
      
    } catch (err) {
      error(`Products table test failed: ${err.message}`);
      console.log('');
      console.log('🔧 This is the same error your application servers are experiencing.');
      console.log('   The Products table structure may be incorrect or missing.');
      console.log('   Run: node database-manager.js init');
      return false;
    }
  }

  async runMigrations() {
    log('Running database migrations...');
    
    try {
      await this.initializeSequelize();
      const { Sequelize } = require('sequelize');
      const queryInterface = this.sequelize.getQueryInterface();
      
      // List of migration files in order
      const migrations = [
        '001-initial-schema.js',
        '001-add-role-based-access-control.js',
        '002-add-social-authentication.js',
        '003-add-square-authentication.js',
        '20240729000001-create-data-environment-tables.js',
        '20241201000000-create-social-media-tables.js'
      ];
      
      console.log(`📋 Processing ${migrations.length} migration files...`);
      
      for (const migrationFile of migrations) {
        const migrationPath = path.join(__dirname, 'server/migrations', migrationFile);
        
        try {
          log(`Running migration: ${migrationFile}`);
          
          if (!fs.existsSync(migrationPath)) {
            warning(`Migration file not found: ${migrationFile}, skipping...`);
            continue;
          }
          
          const migration = require(migrationPath);
          
          if (migration.up && typeof migration.up === 'function') {
            await migration.up(queryInterface, Sequelize);
            success(`Migration completed: ${migrationFile}`);
          } else {
            warning(`Migration has no 'up' function: ${migrationFile}, skipping...`);
          }
          
        } catch (err) {
          if (err.original && err.original.code === 'ER_TABLE_EXISTS_ERROR') {
            info(`Tables already exist for migration: ${migrationFile}, skipping...`);
          } else {
            warning(`Migration failed: ${migrationFile} - ${err.message}`);
          }
        }
      }
      
      success('All migrations processed');
      return true;
      
    } catch (err) {
      error(`Migration process failed: ${err.message}`);
      return false;
    }
  }

  async initializeDatabase() {
    log('Initializing database schema...');
    
    try {
      await this.initializeSequelize();
      
      // Step 1: Run migrations
      console.log('');
      console.log('Step 1: Running migrations...');
      await this.runMigrations();
      
      // Step 2: Synchronize models
      console.log('');
      console.log('Step 2: Synchronizing database models...');
      const { initializeDatabase } = require('./server/models/database');
      const initSuccess = await initializeDatabase();
      
      if (!initSuccess) {
        throw new Error('Database model synchronization failed');
      }
      
      success('Database schema initialized successfully');
      return true;
      
    } catch (err) {
      error(`Database initialization failed: ${err.message}`);
      return false;
    }
  }

  async seedBasicData() {
    log('Seeding basic data...');
    
    try {
      await this.initializeSequelize();
      
      // Check if basic data already exists
      const [roleCount] = await this.sequelize.query('SELECT COUNT(*) as count FROM roles');
      const [categoryCount] = await this.sequelize.query('SELECT COUNT(*) as count FROM Categories');
      
      if (roleCount[0].count > 0 && categoryCount[0].count > 0) {
        info('Basic data already exists, skipping seeding');
        return true;
      }
      
      // Seed basic roles
      if (roleCount[0].count === 0) {
        log('Seeding basic roles...');
        await this.sequelize.query(`
          INSERT INTO roles (name, description, permissions, createdAt, updatedAt) VALUES
          ('admin', 'Administrator with full access', '["all"]', NOW(), NOW()),
          ('customer', 'Regular customer', '["view_products", "place_orders"]', NOW(), NOW()),
          ('manager', 'Store manager', '["manage_products", "view_orders"]', NOW(), NOW())
        `);
        success('Basic roles seeded');
      }
      
      // Seed basic categories
      if (categoryCount[0].count === 0) {
        log('Seeding basic categories...');
        await this.sequelize.query(`
          INSERT INTO Categories (name, description, slug, is_active, createdAt, updatedAt) VALUES
          ('Organic Foods', 'Fresh organic food products', 'organic-foods', 1, NOW(), NOW()),
          ('Supplements', 'Natural health supplements', 'supplements', 1, NOW(), NOW()),
          ('Personal Care', 'Organic personal care products', 'personal-care', 1, NOW(), NOW()),
          ('Home & Garden', 'Organic home and garden products', 'home-garden', 1, NOW(), NOW())
        `);
        success('Basic categories seeded');
      }
      
      success('Basic data seeding completed');
      return true;
      
    } catch (err) {
      error(`Data seeding failed: ${err.message}`);
      return false;
    }
  }

  async debugEnvironment() {
    console.log('');
    console.log('🔧 Environment Debug Information');
    console.log('================================');
    
    // Environment variables
    console.log('📋 Environment Variables:');
    console.log(`  NODE_ENV: ${process.env.NODE_ENV || '[NOT SET]'}`);
    console.log(`  Environment file: ${this.envFile}`);
    console.log(`  DB_HOST: ${process.env.DB_HOST || '[NOT SET]'}`);
    console.log(`  DB_PORT: ${process.env.DB_PORT || '[NOT SET]'}`);
    console.log(`  DB_NAME: ${process.env.DB_NAME || '[NOT SET]'}`);
    console.log(`  DB_USER: ${process.env.DB_USER || '[NOT SET]'}`);
    console.log(`  DB_PASSWORD: ${process.env.DB_PASSWORD ? '[SET]' : '[NOT SET]'}`);
    
    // Environment file check
    console.log('');
    console.log('📁 Environment Files:');
    const envFiles = ['.env', '.env.test', '.env.production', '.env.admin'];
    envFiles.forEach(file => {
      const filePath = path.join(__dirname, file);
      if (fs.existsSync(filePath)) {
        const stats = fs.statSync(filePath);
        console.log(`  ✅ ${file} (${stats.size} bytes, modified: ${stats.mtime.toISOString()})`);
      } else {
        console.log(`  ❌ ${file} (missing)`);
      }
    });
    
    // IPv6 localhost detection
    console.log('');
    console.log('🔍 IPv6 Localhost Detection:');
    if (process.env.DB_HOST === 'localhost' || process.env.DB_HOST === '127.0.0.1') {
      warning('DB_HOST is set to localhost - this may cause IPv6 issues');
      console.log('  Recommended: Use specific hostname instead of localhost');
    } else if (!process.env.DB_HOST) {
      error('DB_HOST is not set - this will default to localhost');
    } else {
      success(`DB_HOST is properly configured: ${process.env.DB_HOST}`);
    }
    
    // Database models check
    console.log('');
    console.log('📦 Database Models:');
    try {
      await this.initializeSequelize();
      const modelNames = Object.keys(this.models);
      console.log(`  ✅ ${modelNames.length} models loaded: ${modelNames.join(', ')}`);
    } catch (err) {
      error(`Failed to load models: ${err.message}`);
    }
  }

  showUsage() {
    console.log('');
    console.log(`${colors.white}Nirvana Organics - Database Manager${colors.reset}`);
    console.log('=====================================');
    console.log('');
    console.log('Usage: node database-manager.js <command>');
    console.log('');
    console.log('Commands:');
    console.log('  check    - Check current database state and table structure');
    console.log('  test     - Test database connection and functionality');
    console.log('  init     - Initialize database schema and run migrations');
    console.log('  seed     - Seed basic data (roles, categories)');
    console.log('  fix      - Comprehensive database fix (init + seed + test)');
    console.log('  debug    - Debug environment variables and configuration');
    console.log('  help     - Show this help message');
    console.log('');
    console.log('Examples:');
    console.log('  node database-manager.js check');
    console.log('  node database-manager.js init');
    console.log('  node database-manager.js fix');
    console.log('  NODE_ENV=production node database-manager.js check');
    console.log('');
  }

  async executeCommand(command) {
    console.log('');
    console.log(`${colors.white}🚀 Nirvana Organics - Database Manager${colors.reset}`);
    console.log('==========================================');
    console.log(`Environment: ${this.environment}`);
    console.log(`Command: ${command}`);
    console.log('');

    try {
      switch (command) {
        case 'check':
          return await this.commandCheck();
        case 'test':
          return await this.commandTest();
        case 'init':
          return await this.commandInit();
        case 'seed':
          return await this.commandSeed();
        case 'fix':
          return await this.commandFix();
        case 'debug':
          return await this.commandDebug();
        case 'help':
        case '--help':
        case '-h':
          this.showUsage();
          return true;
        default:
          error(`Unknown command: ${command}`);
          this.showUsage();
          return false;
      }
    } catch (err) {
      error(`Command execution failed: ${err.message}`);
      console.log('');
      console.log('🔧 Troubleshooting:');
      console.log('  1. Check your environment configuration');
      console.log('  2. Verify database server is accessible');
      console.log('  3. Run: node database-manager.js debug');
      console.log('  4. Check server logs for additional details');
      return false;
    }
  }

  async commandCheck() {
    log('Executing database check...');

    // Step 1: Test connection
    const connectionOk = await this.testConnection();
    if (!connectionOk) {
      return false;
    }

    console.log('');

    // Step 2: Check tables
    const tableCheck = await this.checkTables();
    if (!tableCheck.success) {
      return false;
    }

    console.log('');

    // Step 3: Test Products table if it exists
    if (tableCheck.tables.includes('Products')) {
      const productsOk = await this.testProductsTable();
      if (!productsOk) {
        return false;
      }
    }

    console.log('');
    success('🎉 Database check completed successfully!');
    console.log('');
    console.log('📋 Summary:');
    console.log(`  - Database connection: Working`);
    console.log(`  - Tables found: ${tableCheck.tables.length}`);
    console.log(`  - Required tables: Present`);
    console.log(`  - Products functionality: Working`);

    return true;
  }

  async commandTest() {
    log('Executing database functionality test...');

    const connectionOk = await this.testConnection();
    if (!connectionOk) {
      return false;
    }

    console.log('');
    const productsOk = await this.testProductsTable();

    console.log('');
    if (connectionOk && productsOk) {
      success('🎉 Database functionality test passed!');
      return true;
    } else {
      error('❌ Database functionality test failed');
      return false;
    }
  }

  async commandInit() {
    log('Executing database initialization...');

    // Step 1: Test connection
    const connectionOk = await this.testConnection();
    if (!connectionOk) {
      error('Cannot initialize database - connection failed');
      return false;
    }

    console.log('');

    // Step 2: Initialize schema
    const initOk = await this.initializeDatabase();
    if (!initOk) {
      return false;
    }

    console.log('');

    // Step 3: Verify tables
    const tableCheck = await this.checkTables();
    if (!tableCheck.success) {
      error('Database initialization completed but tables are missing');
      return false;
    }

    console.log('');
    success('🎉 Database initialization completed successfully!');
    console.log('');
    console.log('📋 Next steps:');
    console.log('  1. Run: node database-manager.js seed (to add basic data)');
    console.log('  2. Restart your application servers');
    console.log('  3. Test your application functionality');

    return true;
  }

  async commandSeed() {
    log('Executing database seeding...');

    const connectionOk = await this.testConnection();
    if (!connectionOk) {
      return false;
    }

    console.log('');
    const seedOk = await this.seedBasicData();

    console.log('');
    if (seedOk) {
      success('🎉 Database seeding completed successfully!');
      return true;
    } else {
      error('❌ Database seeding failed');
      return false;
    }
  }

  async commandFix() {
    log('Executing comprehensive database fix...');

    console.log('');
    console.log('This will perform the following operations:');
    console.log('1. Test database connection');
    console.log('2. Initialize database schema');
    console.log('3. Verify all tables exist');
    console.log('4. Test Products table functionality');
    console.log('5. Seed basic data');
    console.log('');

    // Step 1: Connection test
    log('Step 1: Testing database connection...');
    const connectionOk = await this.testConnection();
    if (!connectionOk) {
      error('Comprehensive fix failed - cannot connect to database');
      return false;
    }

    console.log('');

    // Step 2: Initialize database
    log('Step 2: Initializing database schema...');
    const initOk = await this.initializeDatabase();
    if (!initOk) {
      error('Comprehensive fix failed - database initialization failed');
      return false;
    }

    console.log('');

    // Step 3: Verify tables
    log('Step 3: Verifying database tables...');
    const tableCheck = await this.checkTables();
    if (!tableCheck.success) {
      error('Comprehensive fix failed - required tables are missing');
      return false;
    }

    console.log('');

    // Step 4: Test Products functionality
    log('Step 4: Testing Products table functionality...');
    const productsOk = await this.testProductsTable();
    if (!productsOk) {
      error('Comprehensive fix failed - Products table is not working');
      return false;
    }

    console.log('');

    // Step 5: Seed basic data
    log('Step 5: Seeding basic data...');
    const seedOk = await this.seedBasicData();
    if (!seedOk) {
      warning('Basic data seeding failed, but database structure is ready');
    }

    console.log('');
    success('🎉 COMPREHENSIVE DATABASE FIX COMPLETED SUCCESSFULLY!');
    console.log('');
    console.log('✅ Summary:');
    console.log('  - Database connection: Working');
    console.log('  - Schema initialization: Complete');
    console.log('  - Required tables: Present');
    console.log('  - Products functionality: Working');
    console.log('  - Basic data: Seeded');
    console.log('');
    console.log('🚀 Next steps:');
    console.log('  1. Restart your application servers');
    console.log('  2. Test your application endpoints');
    console.log('  3. Monitor logs for any remaining issues');
    console.log('  4. Add additional test data if needed');

    return true;
  }

  async commandDebug() {
    log('Executing environment debug...');

    await this.debugEnvironment();

    console.log('');
    log('Testing database connection with current configuration...');
    const connectionOk = await this.testConnection();

    console.log('');
    if (connectionOk) {
      success('🎉 Environment debug completed - database connection is working');
    } else {
      error('❌ Environment debug completed - database connection failed');
      console.log('');
      console.log('🔧 Recommended actions:');
      console.log('  1. Fix environment variable configuration');
      console.log('  2. Verify database server accessibility');
      console.log('  3. Check database credentials');
      console.log('  4. Run: node database-manager.js fix');
    }

    return connectionOk;
  }
}

// Main execution
async function main() {
  const args = process.argv.slice(2);
  const command = args[0];

  if (!command) {
    const manager = new DatabaseManager();
    manager.showUsage();
    process.exit(1);
  }

  const manager = new DatabaseManager();
  const success = await manager.executeCommand(command);

  process.exit(success ? 0 : 1);
}

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason) => {
  error(`Unhandled Rejection: ${reason}`);
  process.exit(1);
});

// Handle uncaught exceptions
process.on('uncaughtException', (err) => {
  error(`Uncaught Exception: ${err.message}`);
  process.exit(1);
});

// Run the main function
if (require.main === module) {
  main();
}

module.exports = DatabaseManager;
