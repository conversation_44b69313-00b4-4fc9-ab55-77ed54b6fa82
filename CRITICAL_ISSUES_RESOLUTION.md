# Critical Issues Resolution Guide

## 🚨 Current Issues Identified

### Issue 1: Persistent ENOENT Errors (Admin Server)
**Problem:** Admin server still trying to access `/var/www/nirvana-backend-test/dist-admin/index.html`
**Root Cause:** Updated server configurations not deployed or PM2 processes not restarted properly

### Issue 2: Database Connection Error (IPv6 Localhost)
**Problem:** `ConnectionRefusedError: connect ECONNREFUSED ::1:3306`
**Root Cause:** Environment variables not loading correctly, causing database connection to default to IPv6 localhost instead of configured host

### Issue 3: Admin Frontend Assets 404 Errors
**Problem:** `GET /admin/assets/admin-DitisM-I.js 404 (Not Found)` and `GET /admin/assets/admin-TY7ZtfqV.css 404 (Not Found)`
**Root Cause:** Admin frontend build files not deployed to `/var/www/nirvana-frontend-test/admin/` directory

### Issue 4: Database Table Missing (Main Server)
**Problem:** `Table 'u106832845_nirvana.Products' doesn't exist`
**Root Cause:** Database schema not initialized in test environment

## ✅ Resolution Steps

### IMMEDIATE ACTION REQUIRED

**Step 1: Deploy Updated Server Configurations**
```bash
# Run as Nirvana user
sudo -u Nirvana bash

# Navigate to test deployment directory
cd /var/www/nirvana-backend-test

# Make scripts executable
chmod +x deploy-and-fix.sh
chmod +x restart-services.sh
chmod +x verify-database.js

# Run comprehensive deployment fix
./deploy-and-fix.sh
```

**Step 2: Test Database Connection**
```bash
# Debug environment variables
node debug-env.js

# Test database connection specifically
node test-db-connection.js

# If connection works, verify database schema
node verify-database.js
```

**Step 2b: Fix Environment Loading (if connection fails)**
```bash
# If IPv6 localhost error persists, check environment loading:
cd /var/www/nirvana-backend-test
ls -la .env*
cat .env.test | grep DB_HOST

# Should show: DB_HOST=srv1921.hstgr.io
# If not, copy from project directory:
cp /path/to/project/nirvana-test/.env.test .
cp /path/to/project/nirvana-test/.env.admin.test .
```

**Step 3: Fix Admin 404 Assets**
```bash
# Diagnose 404 asset issues
./diagnose-404-assets.sh

# Fix admin frontend asset deployment
./fix-admin-404-assets.sh
```

**Step 4: Fix Database Tables**
```bash
# Check current database state
./check-database-tables.js

# Initialize database schema if tables are missing
./fix-database-tables.sh
```

**Step 5: Restart Services (Alternative Method)**
```bash
# If deploy-and-fix.sh encounters issues, use this:
./restart-services.sh
```

### VERIFICATION COMMANDS

**Check PM2 Status:**
```bash
pm2 status
pm2 logs nirvana-backend-admin-test --lines 20
pm2 logs nirvana-backend-main-test --lines 20
```

**Test Health Endpoints:**
```bash
curl -v https://test.shopnirvanaorganics.com/api/health
curl -v https://test.shopnirvanaorganics.com/admin/api/health
```

**Verify Frontend Paths:**
```bash
# Check if frontend directories exist
ls -la /var/www/nirvana-frontend-test/
ls -la /var/www/nirvana-frontend-test/main/
ls -la /var/www/nirvana-frontend-test/admin/
```

## 🔧 Manual Troubleshooting

### If ENOENT Errors Persist

**Check Server Configuration:**
```bash
cd /var/www/nirvana-backend-test
grep -n "NODE_ENV === 'test'" server/admin-server.js
grep -n "nirvana-frontend-test" server/admin-server.js
```

**Expected Output:**
- Should find environment-aware path logic
- Should reference `/var/www/nirvana-frontend-test/admin`

**If Not Found:**
```bash
# Copy updated files manually
cp /path/to/project/nirvana-test/server/admin-server.js server/
cp /path/to/project/nirvana-test/server/index.js server/
```

### If Admin 404 Asset Errors Persist

**Check Asset Deployment:**
```bash
cd /var/www/nirvana-backend-test
./diagnose-404-assets.sh
```

**Expected Findings:**
- Assets should exist in `dist-admin/assets/` (source)
- Assets should exist in `/var/www/nirvana-frontend-test/admin/assets/` (deployed)
- Files should have proper permissions (644)

**Manual Asset Deployment:**
```bash
# If assets missing from deployment directory:
sudo mkdir -p /var/www/nirvana-frontend-test/admin/assets
sudo chown -R Nirvana:Nirvana /var/www/nirvana-frontend-test/

# Copy from project directory:
cp -r /path/to/project/nirvana-test/dist-admin/* /var/www/nirvana-frontend-test/admin/

# Set permissions:
chmod -R 644 /var/www/nirvana-frontend-test/admin/*
chmod -R 755 /var/www/nirvana-frontend-test/admin/*/
```

**Test Asset Accessibility:**
```bash
# Test HTTP responses:
curl -I https://test.shopnirvanaorganics.com/admin/assets/admin-DitisM-I.js
curl -I https://test.shopnirvanaorganics.com/admin/assets/admin-TY7ZtfqV.css

# Should return HTTP 200, not 404
```

### If IPv6 Localhost Connection Errors Persist

**Debug Environment Variable Loading:**
```bash
cd /var/www/nirvana-backend-test
node debug-env.js
```

**Expected Output:**
- Should show DB_HOST=srv1921.hstgr.io
- Should NOT show IPv6 localhost (::1) references

**Fix Environment Loading:**
```bash
# Ensure correct environment files exist
ls -la .env.test .env.admin.test

# Check file contents
head -20 .env.test | grep -E "(NODE_ENV|DB_HOST|DB_NAME)"

# If files are missing or incorrect, copy from project:
cp /path/to/project/nirvana-test/.env.test .
cp /path/to/project/nirvana-test/.env.admin.test .
```

### If Database Table Errors Persist

**Check Database Tables:**
```bash
cd /var/www/nirvana-backend-test
node check-database-tables.js
```

**Expected Output:**
- Should show all required tables (Products, Categories, Users, Orders, etc.)
- Products table should be queryable with JOIN operations
- No "Table doesn't exist" errors

**Initialize Database Schema:**
```bash
# If tables are missing, run full initialization:
node initialize-database.js

# Or use the comprehensive fix script:
./fix-database-tables.sh
```

**Manual Database Verification:**
```bash
# Test direct database connection:
mysql -h srv1921.hstgr.io -u u106832845_root -p u106832845_nirvana -e "SHOW TABLES;"

# Test specific Products query:
mysql -h srv1921.hstgr.io -u u106832845_root -p u106832845_nirvana -e "
SELECT p.id, p.name, c.name as category_name
FROM Products p
LEFT JOIN Categories c ON p.category_id = c.id
LIMIT 5;"
```

### If Database Connection Errors Persist

**Test Application Database Connection:**
```bash
cd /var/www/nirvana-backend-test
node test-db-connection.js
```

**Manual Table Creation:**
```bash
node -e "
const { sequelize } = require('./server/models/database');
sequelize.sync({ force: false, alter: true }).then(() => {
  console.log('✅ Tables synchronized');
  process.exit(0);
}).catch(err => {
  console.error('❌ Sync failed:', err);
  process.exit(1);
});
"
```

## 🎯 Expected Results After Fix

### ✅ Success Indicators

1. **PM2 Status:**
   ```
   nirvana-backend-main-test  │ online │ 0
   nirvana-backend-admin-test │ online │ 0
   ```

2. **Health Endpoints:**
   ```bash
   curl https://test.shopnirvanaorganics.com/api/health
   # Should return: {"status":"healthy","timestamp":"..."}
   
   curl https://test.shopnirvanaorganics.com/admin/api/health  
   # Should return: {"status":"healthy","timestamp":"..."}
   ```

3. **No ENOENT Errors in Logs:**
   ```bash
   pm2 logs nirvana-backend-admin-test --lines 10
   # Should NOT show: "ENOENT: no such file or directory, stat '/var/www/nirvana-backend-test/dist-admin/index.html'"
   ```

4. **No Database Table Errors:**
   ```bash
   pm2 logs nirvana-backend-main-test --lines 10
   # Should NOT show: "Table 'u106832845_nirvana.Products' doesn't exist"
   ```

### ❌ If Issues Persist

**Contact Information:**
- Check PM2 logs for specific error messages
- Verify file permissions: `ls -la /var/www/nirvana-backend-test/server/`
- Ensure all environment files are present and correct
- Verify database connectivity and credentials

**Emergency Rollback:**
```bash
# If critical issues persist, rollback to previous working state
pm2 stop all
# Restore from backup if available
# Contact system administrator
```

## 📋 Post-Resolution Tasks

1. **Deploy Frontend Builds:**
   - Build and deploy main frontend to `/var/www/nirvana-frontend-test/main/`
   - Build and deploy admin frontend to `/var/www/nirvana-frontend-test/admin/`

2. **Monitor Services:**
   - Set up monitoring for PM2 processes
   - Configure log rotation
   - Test all application functionality

3. **Documentation Update:**
   - Update deployment procedures
   - Document any additional configuration changes
   - Create monitoring alerts for similar issues

## 🚀 Prevention Measures

1. **Always test configuration changes in development first**
2. **Use the provided deployment scripts for consistency**
3. **Verify database schema before deploying**
4. **Monitor PM2 logs after any deployment**
5. **Maintain backup of working configurations**
