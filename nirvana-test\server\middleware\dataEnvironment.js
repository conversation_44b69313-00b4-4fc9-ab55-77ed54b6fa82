const dataEnvironmentService = require('../services/dataEnvironmentService');

/**
 * Middleware to inject data environment context into requests
 * This middleware checks the current data mode for admin users and
 * adds the context to the request object
 */
const injectDataEnvironment = async (req, res, next) => {
  try {
    // Only apply to authenticated admin users
    if (!req.user || (req.user.Role?.name !== 'admin' && req.user.Role?.name !== 'manager')) {
      return next();
    }

    const userId = req.user.id;
    const sessionId = req.headers['x-session-id'] || null;
    
    // Get current data mode
    const mode = await dataEnvironmentService.getCurrentMode(userId, sessionId);
    
    // Add data environment context to request
    req.dataEnvironment = {
      mode,
      userId,
      sessionId,
      isMockMode: mode === 'mock',
      isRealMode: mode === 'real'
    };

    next();
  } catch (error) {
    console.error('Error in data environment middleware:', error);
    // Don't fail the request, just continue without data environment context
    next();
  }
};

/**
 * Middleware to enforce data environment mode for specific routes
 * This can be used to restrict certain operations to specific modes
 */
const requireDataMode = (requiredMode) => {
  return async (req, res, next) => {
    try {
      if (!req.dataEnvironment) {
        return res.status(500).json({
          success: false,
          message: 'Data environment context not available'
        });
      }

      if (req.dataEnvironment.mode !== requiredMode) {
        return res.status(403).json({
          success: false,
          message: `This operation requires ${requiredMode} data mode. Current mode: ${req.dataEnvironment.mode}`
        });
      }

      next();
    } catch (error) {
      console.error('Error in require data mode middleware:', error);
      res.status(500).json({
        success: false,
        message: 'Error checking data mode requirements'
      });
    }
  };
};

/**
 * Middleware to add data mode indicator to response headers
 */
const addDataModeHeaders = (req, res, next) => {
  if (req.dataEnvironment) {
    res.set({
      'X-Data-Mode': req.dataEnvironment.mode,
      'X-Is-Mock-Data': req.dataEnvironment.isMockMode.toString(),
      'X-Session-Id': req.dataEnvironment.sessionId || 'none'
    });
  }
  next();
};

/**
 * Helper function to get data with environment context
 * This can be used in controllers to automatically get the right data
 */
const getDataWithContext = async (req, entityType, options = {}) => {
  if (!req.dataEnvironment) {
    throw new Error('Data environment context not available');
  }

  return await dataEnvironmentService.getData(
    req.dataEnvironment.userId,
    entityType,
    {
      ...options,
      sessionId: req.dataEnvironment.sessionId
    }
  );
};

/**
 * Middleware to log data environment operations for audit purposes
 */
const logDataEnvironmentOperation = (operation) => {
  return (req, res, next) => {
    if (req.dataEnvironment) {
      console.log(`Data Environment Operation: ${operation}`, {
        userId: req.dataEnvironment.userId,
        mode: req.dataEnvironment.mode,
        sessionId: req.dataEnvironment.sessionId,
        timestamp: new Date().toISOString(),
        userAgent: req.get('User-Agent'),
        ip: req.ip
      });
    }
    next();
  };
};

/**
 * Middleware to validate admin access for data environment operations
 */
const requireAdminForDataEnvironment = (req, res, next) => {
  if (!req.user) {
    return res.status(401).json({
      success: false,
      message: 'Authentication required'
    });
  }

  if (req.user.Role?.name !== 'admin' && req.user.Role?.name !== 'manager') {
    return res.status(403).json({
      success: false,
      message: 'Admin or manager privileges required for data environment operations'
    });
  }

  next();
};

/**
 * Middleware to prevent destructive operations in production mode
 */
const preventDestructiveInProduction = (req, res, next) => {
  const isProduction = process.env.NODE_ENV === 'production';
  const isDestructiveOperation = ['DELETE', 'PUT'].includes(req.method) || 
                                req.path.includes('/clear') || 
                                req.path.includes('/delete');

  if (isProduction && isDestructiveOperation && req.dataEnvironment?.isRealMode) {
    return res.status(403).json({
      success: false,
      message: 'Destructive operations on real data are not allowed in production environment'
    });
  }

  next();
};

module.exports = {
  injectDataEnvironment,
  requireDataMode,
  addDataModeHeaders,
  getDataWithContext,
  logDataEnvironmentOperation,
  requireAdminForDataEnvironment,
  preventDestructiveInProduction
};
