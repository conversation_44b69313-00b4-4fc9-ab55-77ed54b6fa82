const { Sequelize, DataTypes } = require('sequelize');
const path = require('path');

// Load environment variables based on NODE_ENV
const envFile = process.env.NODE_ENV === 'test'
  ? '.env.test'
  : process.env.NODE_ENV === 'production'
  ? '.env.production'
  : '.env';

require('dotenv').config({ path: path.join(__dirname, '../../', envFile) });

// Debug logging for database connection
console.log(`🔧 Database Configuration (NODE_ENV: ${process.env.NODE_ENV}):`);
console.log(`  Loading env file: ${envFile}`);
console.log(`  DB_HOST: ${process.env.DB_HOST}`);
console.log(`  DB_NAME: ${process.env.DB_NAME}`);
console.log(`  DB_USER: ${process.env.DB_USER}`);
console.log(`  DB_PORT: ${process.env.DB_PORT}`);

const defineCampaignModels = require('./campaignModels');

// Create Sequelize instance for Hostinger MariaDB
const sequelize = new Sequelize(
  process.env.DB_NAME,
  process.env.DB_USER,
  process.env.DB_PASSWORD,
  {
    host: process.env.DB_HOST,
    port: process.env.DB_PORT || 3306,
    dialect: 'mysql',
    timezone: '+00:00',
    logging: false,
    pool: {
      max: 10,
      min: 0,
      acquire: 30000,
      idle: 10000
    },
    dialectOptions: {
      charset: 'utf8mb4',
      supportBigNumbers: true,
      bigNumberStrings: true
    },
    define: {
      charset: 'utf8mb4',
      collate: 'utf8mb4_unicode_ci',
      timestamps: true,
      underscored: false
    }
  }
);

// Define all models
const models = {};

// User Model
models.User = sequelize.define('User', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  firstName: {
    type: DataTypes.STRING,
    allowNull: false,
    validate: { len: [2, 50], notEmpty: true }
  },
  lastName: {
    type: DataTypes.STRING,
    allowNull: false,
    validate: { len: [2, 50], notEmpty: true }
  },
  email: {
    type: DataTypes.STRING,
    allowNull: false,
    unique: true,
    validate: { isEmail: true, notEmpty: true }
  },
  password: {
    type: DataTypes.STRING,
    allowNull: false,
    validate: { len: [8, 255], notEmpty: true }
  },
  phone: {
    type: DataTypes.STRING,
    allowNull: true,
    validate: {
      is: {
        args: /^[\+]?[1-9][\d]{0,15}$/,
        msg: 'Please provide a valid phone number'
      }
    }
  },
  dateOfBirth: {
    type: DataTypes.DATEONLY,
    allowNull: true
  },
  roleId: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: { model: 'roles', key: 'id' },
    defaultValue: null
  },
  isEmailVerified: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  emailVerificationToken: {
    type: DataTypes.STRING,
    allowNull: true
  },
  emailVerificationExpires: {
    type: DataTypes.DATE,
    allowNull: true
  },
  passwordResetToken: {
    type: DataTypes.STRING,
    allowNull: true
  },
  passwordResetExpires: {
    type: DataTypes.DATE,
    allowNull: true
  },
  isActive: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  },
  lastLoginAt: {
    type: DataTypes.DATE,
    allowNull: true
  },
  loginAttempts: {
    type: DataTypes.INTEGER,
    defaultValue: 0
  },
  lockUntil: {
    type: DataTypes.DATE,
    allowNull: true
  },
  preferences: {
    type: DataTypes.JSON,
    defaultValue: {}
  },
  // OAuth fields
  googleId: {
    type: DataTypes.STRING,
    allowNull: true,
    unique: true
  },
  facebookId: {
    type: DataTypes.STRING,
    allowNull: true,
    unique: true
  },
  profilePicture: {
    type: DataTypes.STRING,
    allowNull: true
  },
  authProvider: {
    type: DataTypes.ENUM('local', 'google', 'facebook', 'both'),
    defaultValue: 'local'
  },
  // Additional customer data fields
  gender: {
    type: DataTypes.ENUM('he', 'she', 'they'),
    allowNull: true
  },
  membershipType: {
    type: DataTypes.ENUM('first-time', 'regular', 'premium'),
    defaultValue: 'first-time'
  },
  trafficSource: {
    type: DataTypes.ENUM('organic', 'direct', 'social-media', 'referral', 'paid'),
    allowNull: true
  }
});

// Add instance methods to User model
models.User.prototype.comparePassword = async function(candidatePassword) {
  const bcrypt = require('bcryptjs');
  return await bcrypt.compare(candidatePassword, this.password);
};

// Role Model
models.Role = sequelize.define('Role', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  name: {
    type: DataTypes.STRING(50),
    allowNull: false,
    unique: true,
    validate: {
      len: [1, 50],
      isIn: [['admin', 'manager', 'customer']]
    }
  },
  displayName: {
    type: DataTypes.STRING(100),
    allowNull: false,
    field: 'display_name',
    validate: {
      len: [1, 100]
    }
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  permissions: {
    type: DataTypes.JSON,
    allowNull: false,
    defaultValue: {},
    comment: 'JSON object containing role permissions'
  },
  isActive: {
    type: DataTypes.BOOLEAN,
    defaultValue: true,
    field: 'is_active'
  },
  isSystemRole: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    field: 'is_system_role',
    comment: 'System roles cannot be deleted or have their core permissions modified'
  },
  priority: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    comment: 'Higher priority roles have more access (admin=100, manager=50, customer=10)'
  }
}, {
  tableName: 'roles',
  indexes: [
    {
      fields: ['name'],
      unique: true
    },
    {
      fields: ['is_active']
    },
    {
      fields: ['priority']
    }
  ]
});

// Address Model
models.Address = sequelize.define('Address', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  userId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: { model: 'Users', key: 'id' }
  },
  type: {
    type: DataTypes.ENUM('billing', 'shipping'),
    allowNull: false
  },
  firstName: {
    type: DataTypes.STRING,
    allowNull: false
  },
  lastName: {
    type: DataTypes.STRING,
    allowNull: false
  },
  company: {
    type: DataTypes.STRING,
    allowNull: true
  },
  address1: {
    type: DataTypes.STRING,
    allowNull: false
  },
  address2: {
    type: DataTypes.STRING,
    allowNull: true
  },
  city: {
    type: DataTypes.STRING,
    allowNull: false
  },
  state: {
    type: DataTypes.STRING,
    allowNull: false
  },
  zipCode: {
    type: DataTypes.STRING,
    allowNull: false
  },
  country: {
    type: DataTypes.STRING,
    allowNull: false,
    defaultValue: 'US'
  },
  phone: {
    type: DataTypes.STRING,
    allowNull: true
  },
  isDefault: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  }
});

// Category Model
models.Category = sequelize.define('Category', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  name: {
    type: DataTypes.STRING,
    allowNull: false,
    unique: true
  },
  slug: {
    type: DataTypes.STRING,
    allowNull: false,
    unique: true
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  image: {
    type: DataTypes.STRING,
    allowNull: true
  },
  parentId: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: { model: 'Categories', key: 'id' }
  },
  isActive: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  },
  sortOrder: {
    type: DataTypes.INTEGER,
    defaultValue: 0
  },
  seoTitle: {
    type: DataTypes.STRING,
    allowNull: true
  },
  seoDescription: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  seoKeywords: {
    type: DataTypes.STRING,
    allowNull: true
  }
});

// Product Model
models.Product = sequelize.define('Product', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  name: {
    type: DataTypes.STRING,
    allowNull: false
  },
  slug: {
    type: DataTypes.STRING,
    allowNull: false,
    unique: true
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  shortDescription: {
    type: DataTypes.STRING(500),
    allowNull: true
  },
  price: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false
  },
  comparePrice: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true
  },
  costPrice: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true
  },
  sku: {
    type: DataTypes.STRING,
    allowNull: true,
    unique: true
  },
  barcode: {
    type: DataTypes.STRING,
    allowNull: true
  },
  trackQuantity: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  },
  quantity: {
    type: DataTypes.INTEGER,
    defaultValue: 0
  },
  lowStockThreshold: {
    type: DataTypes.INTEGER,
    defaultValue: 10
  },
  weight: {
    type: DataTypes.DECIMAL(8, 2),
    allowNull: true
  },
  weightUnit: {
    type: DataTypes.ENUM('g', 'kg', 'oz', 'lb'),
    defaultValue: 'g'
  },
  dimensions: {
    type: DataTypes.JSON,
    allowNull: true
  },
  categoryId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: { model: 'Categories', key: 'id' }
  },
  subcategoryId: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: { model: 'Categories', key: 'id' }
  },
  brand: {
    type: DataTypes.STRING,
    allowNull: true
  },
  vendor: {
    type: DataTypes.STRING,
    allowNull: true
  },
  tags: {
    type: DataTypes.JSON,
    defaultValue: []
  },
  images: {
    type: DataTypes.JSON,
    defaultValue: []
  },
  variants: {
    type: DataTypes.JSON,
    defaultValue: []
  },
  attributes: {
    type: DataTypes.JSON,
    defaultValue: {}
  },
  isActive: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  },
  isFeatured: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  isDigital: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  requiresShipping: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  },
  taxable: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  },
  taxClass: {
    type: DataTypes.STRING,
    allowNull: true
  },
  seoTitle: {
    type: DataTypes.STRING,
    allowNull: true
  },
  seoDescription: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  seoKeywords: {
    type: DataTypes.STRING,
    allowNull: true
  },
  rating: {
    type: DataTypes.DECIMAL(3, 2),
    defaultValue: 0
  },
  reviewCount: {
    type: DataTypes.INTEGER,
    defaultValue: 0
  },
  viewCount: {
    type: DataTypes.INTEGER,
    defaultValue: 0
  },
  salesCount: {
    type: DataTypes.INTEGER,
    defaultValue: 0
  }
});

// Cart Model
models.Cart = sequelize.define('Cart', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  userId: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: { model: 'Users', key: 'id' }
  },
  sessionId: {
    type: DataTypes.STRING,
    allowNull: true
  },
  itemsData: {
    type: DataTypes.JSON,
    defaultValue: []
  },
  subtotal: {
    type: DataTypes.DECIMAL(10, 2),
    defaultValue: 0
  },
  tax: {
    type: DataTypes.DECIMAL(10, 2),
    defaultValue: 0
  },
  shipping: {
    type: DataTypes.DECIMAL(10, 2),
    defaultValue: 0
  },
  total: {
    type: DataTypes.DECIMAL(10, 2),
    defaultValue: 0
  },
  currency: {
    type: DataTypes.STRING(3),
    defaultValue: 'USD'
  },
  expiresAt: {
    type: DataTypes.DATE,
    allowNull: true
  }
});

// Add static methods to Cart model
models.Cart.findOrCreateForUser = async function(userId) {
  let cart = await this.findOne({
    where: { userId }
  });

  if (!cart) {
    cart = await this.create({
      userId
    });
  }

  // Get cart items separately to avoid association issues
  const cartItems = await models.CartItem.findAll({
    where: { cartId: cart.id }
  });

  // Add items to cart object
  cart.dataValues.items = cartItems;

  return cart;
};

// Add instance methods to Cart model
models.Cart.prototype.addItem = async function(productId, variant, quantity, price) {
  // Check if item already exists in cart
  let cartItem = await models.CartItem.findOne({
    where: {
      cartId: this.id,
      productId: productId,
      variantId: variant ? variant.id : null
    }
  });

  if (cartItem) {
    // Update existing item quantity
    cartItem.quantity += quantity;
    cartItem.total = cartItem.quantity * price;
    await cartItem.save();
  } else {
    // Create new cart item
    cartItem = await models.CartItem.create({
      cartId: this.id,
      productId: productId,
      variantId: variant ? variant.id : null,
      quantity: quantity,
      price: price,
      total: quantity * price
    });
  }

  // Refresh the cart items
  const cartItems = await models.CartItem.findAll({
    where: { cartId: this.id }
  });
  this.dataValues.items = cartItems;

  return cartItem;
};

// CartItem Model
models.CartItem = sequelize.define('CartItem', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  cartId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: { model: 'Carts', key: 'id' }
  },
  productId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: { model: 'Products', key: 'id' }
  },
  variantId: {
    type: DataTypes.INTEGER,
    allowNull: true
  },
  quantity: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 1
  },
  price: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false
  },
  total: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false
  },
  attributes: {
    type: DataTypes.JSON,
    defaultValue: {}
  }
});

// Order Model
models.Order = sequelize.define('Order', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  orderNumber: {
    type: DataTypes.STRING,
    allowNull: false,
    unique: true
  },
  userId: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: { model: 'Users', key: 'id' }
  },
  email: {
    type: DataTypes.STRING,
    allowNull: false
  },
  status: {
    type: DataTypes.ENUM('pending', 'processing', 'shipped', 'delivered', 'cancelled', 'refunded'),
    defaultValue: 'pending'
  },
  paymentStatus: {
    type: DataTypes.ENUM('pending', 'paid', 'failed', 'refunded', 'partially_refunded'),
    defaultValue: 'pending'
  },
  fulfillmentStatus: {
    type: DataTypes.ENUM('unfulfilled', 'partial', 'fulfilled'),
    defaultValue: 'unfulfilled'
  },
  subtotal: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false
  },
  tax: {
    type: DataTypes.DECIMAL(10, 2),
    defaultValue: 0
  },
  shipping: {
    type: DataTypes.DECIMAL(10, 2),
    defaultValue: 0
  },
  discount: {
    type: DataTypes.DECIMAL(10, 2),
    defaultValue: 0
  },
  total: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false
  },
  currency: {
    type: DataTypes.STRING(3),
    defaultValue: 'USD'
  },
  billingAddress: {
    type: DataTypes.JSON,
    allowNull: false
  },
  shippingAddress: {
    type: DataTypes.JSON,
    allowNull: false
  },
  paymentMethod: {
    type: DataTypes.STRING,
    allowNull: true
  },
  paymentDetails: {
    type: DataTypes.JSON,
    defaultValue: {}
  },
  shippingMethod: {
    type: DataTypes.STRING,
    allowNull: true
  },
  trackingNumber: {
    type: DataTypes.STRING,
    allowNull: true
  },
  notes: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  tags: {
    type: DataTypes.JSON,
    defaultValue: []
  },
  processedAt: {
    type: DataTypes.DATE,
    allowNull: true
  },
  shippedAt: {
    type: DataTypes.DATE,
    allowNull: true
  },
  deliveredAt: {
    type: DataTypes.DATE,
    allowNull: true
  },
  cancelledAt: {
    type: DataTypes.DATE,
    allowNull: true
  },
  // Enhanced customer data fields
  customerGender: {
    type: DataTypes.ENUM('he', 'she', 'they'),
    allowNull: true
  },
  customerMembershipType: {
    type: DataTypes.ENUM('first-time', 'regular', 'premium'),
    defaultValue: 'first-time'
  },
  trafficSource: {
    type: DataTypes.ENUM('organic', 'direct', 'social-media', 'referral', 'paid'),
    allowNull: true
  },
  referralSource: {
    type: DataTypes.STRING,
    allowNull: true
  },
  // Enhanced shipping fields
  shippingMethod: {
    type: DataTypes.ENUM('regular', 'regular-free', 'express'),
    defaultValue: 'regular'
  },
  shippingCost: {
    type: DataTypes.DECIMAL(10, 2),
    defaultValue: 0
  },
  trackingNumber: {
    type: DataTypes.STRING,
    allowNull: true
  },
  trackingUrl: {
    type: DataTypes.STRING,
    allowNull: true
  },
  estimatedDeliveryDate: {
    type: DataTypes.DATE,
    allowNull: true
  },
  // Customer analytics
  isFirstOrder: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  },
  customerLifetimeValue: {
    type: DataTypes.DECIMAL(10, 2),
    defaultValue: 0
  },
  orderSource: {
    type: DataTypes.ENUM('web', 'mobile', 'phone', 'in-store'),
    defaultValue: 'web'
  }
});

// OrderItem Model
models.OrderItem = sequelize.define('OrderItem', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  orderId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: { model: 'Orders', key: 'id' }
  },
  productId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: { model: 'Products', key: 'id' }
  },
  variantId: {
    type: DataTypes.INTEGER,
    allowNull: true
  },
  name: {
    type: DataTypes.STRING,
    allowNull: false
  },
  sku: {
    type: DataTypes.STRING,
    allowNull: true
  },
  quantity: {
    type: DataTypes.INTEGER,
    allowNull: false
  },
  price: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false
  },
  total: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false
  },
  attributes: {
    type: DataTypes.JSON,
    defaultValue: {}
  },
  fulfillmentStatus: {
    type: DataTypes.ENUM('unfulfilled', 'fulfilled', 'returned'),
    defaultValue: 'unfulfilled'
  }
});

// Review Model
models.Review = sequelize.define('Review', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  userId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: { model: 'Users', key: 'id' }
  },
  productId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: { model: 'Products', key: 'id' }
  },
  orderId: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: { model: 'Orders', key: 'id' }
  },
  rating: {
    type: DataTypes.INTEGER,
    allowNull: false,
    validate: { min: 1, max: 5 }
  },
  title: {
    type: DataTypes.STRING,
    allowNull: true
  },
  comment: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  images: {
    type: DataTypes.JSON,
    defaultValue: []
  },
  isVerifiedPurchase: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  isApproved: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  helpfulCount: {
    type: DataTypes.INTEGER,
    defaultValue: 0
  },
  reportCount: {
    type: DataTypes.INTEGER,
    defaultValue: 0
  }
});

// Define Coupon model inline to avoid circular dependency
models.Coupon = sequelize.define('Coupon', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  code: {
    type: DataTypes.STRING,
    allowNull: false,
    unique: true,
    validate: { len: [3, 50], notEmpty: true }
  },
  name: {
    type: DataTypes.STRING,
    allowNull: false,
    validate: { len: [3, 100], notEmpty: true }
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  type: {
    type: DataTypes.ENUM('percentage', 'fixed_amount', 'free_shipping'),
    allowNull: false,
    defaultValue: 'percentage'
  },
  value: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    validate: { min: 0 }
  },
  minimumOrderAmount: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    field: 'minimum_order_amount',
    validate: { min: 0 }
  },
  maximumDiscountAmount: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    field: 'maximum_discount_amount',
    validate: { min: 0 }
  },
  usageLimit: {
    type: DataTypes.INTEGER,
    allowNull: true,
    field: 'usage_limit',
    validate: { min: 1 }
  },
  usageCount: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    field: 'usage_count',
    validate: { min: 0 }
  },
  userUsageLimit: {
    type: DataTypes.INTEGER,
    allowNull: true,
    field: 'user_usage_limit',
    validate: { min: 1 }
  },
  validFrom: {
    type: DataTypes.DATE,
    allowNull: false,
    field: 'valid_from'
  },
  validUntil: {
    type: DataTypes.DATE,
    allowNull: false,
    field: 'valid_until'
  },
  isActive: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: true,
    field: 'is_active'
  },
  applicableProducts: {
    type: DataTypes.JSON,
    allowNull: true,
    field: 'applicable_products',
    defaultValue: []
  },
  applicableCategories: {
    type: DataTypes.JSON,
    allowNull: true,
    field: 'applicable_categories',
    defaultValue: []
  },
  excludedProducts: {
    type: DataTypes.JSON,
    allowNull: true,
    field: 'excluded_products',
    defaultValue: []
  },
  excludedCategories: {
    type: DataTypes.JSON,
    allowNull: true,
    field: 'excluded_categories',
    defaultValue: []
  },
  firstTimeCustomerOnly: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false,
    field: 'first_time_customer_only'
  },
  stackable: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false
  },
  priority: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    validate: { min: 0, max: 100 }
  },
  // Enhanced referral system fields
  isReferralCoupon: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    field: 'is_referral_coupon'
  },
  referrerReward: {
    type: DataTypes.DECIMAL(10, 2),
    defaultValue: 0,
    field: 'referrer_reward',
    validate: { min: 0 }
  },
  referrerRewardType: {
    type: DataTypes.ENUM('percentage', 'fixed_amount', 'points'),
    defaultValue: 'fixed_amount',
    field: 'referrer_reward_type'
  },
  // Social sharing fields
  socialShareBonus: {
    type: DataTypes.DECIMAL(10, 2),
    defaultValue: 0,
    field: 'social_share_bonus',
    validate: { min: 0 }
  },
  socialPlatforms: {
    type: DataTypes.JSON,
    defaultValue: [],
    field: 'social_platforms'
  },
  // Buy X Get Y specific fields
  buyQuantity: {
    type: DataTypes.INTEGER,
    allowNull: true,
    field: 'buy_quantity',
    validate: { min: 1 }
  },
  getQuantity: {
    type: DataTypes.INTEGER,
    allowNull: true,
    field: 'get_quantity',
    validate: { min: 1 }
  },
  getProductIds: {
    type: DataTypes.JSON,
    defaultValue: [],
    field: 'get_product_ids'
  },
  createdBy: {
    type: DataTypes.INTEGER,
    allowNull: true,
    field: 'created_by'
  }
});

// Coupon Usage Model
models.CouponUsage = sequelize.define('CouponUsage', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  couponId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    field: 'coupon_id'
  },
  userId: {
    type: DataTypes.INTEGER,
    allowNull: true,
    field: 'user_id'
  },
  orderId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    field: 'order_id'
  },
  discountAmount: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    field: 'discount_amount',
    validate: { min: 0 }
  },
  guestEmail: {
    type: DataTypes.STRING,
    allowNull: true,
    field: 'guest_email',
    validate: { isEmail: true }
  }
});

// Referral Model
models.Referral = sequelize.define('Referral', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  referrerId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    field: 'referrer_id'
  },
  refereeId: {
    type: DataTypes.INTEGER,
    allowNull: true,
    field: 'referee_id'
  },
  refereeEmail: {
    type: DataTypes.STRING,
    allowNull: true,
    field: 'referee_email',
    validate: { isEmail: true }
  },
  referralCode: {
    type: DataTypes.STRING,
    allowNull: false,
    unique: true,
    field: 'referral_code',
    validate: { len: [6, 20] }
  },
  status: {
    type: DataTypes.ENUM('pending', 'completed', 'rewarded'),
    defaultValue: 'pending'
  },
  orderId: {
    type: DataTypes.INTEGER,
    allowNull: true,
    field: 'order_id'
  },
  rewardAmount: {
    type: DataTypes.DECIMAL(10, 2),
    defaultValue: 0,
    field: 'reward_amount',
    validate: { min: 0 }
  },
  rewardType: {
    type: DataTypes.ENUM('discount', 'cash', 'points'),
    defaultValue: 'discount',
    field: 'reward_type'
  },
  completedAt: {
    type: DataTypes.DATE,
    allowNull: true,
    field: 'completed_at'
  },
  rewardedAt: {
    type: DataTypes.DATE,
    allowNull: true,
    field: 'rewarded_at'
  }
});

// Social Share Model
models.SocialShare = sequelize.define('SocialShare', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  userId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    field: 'user_id'
  },
  couponId: {
    type: DataTypes.INTEGER,
    allowNull: true,
    field: 'coupon_id'
  },
  platform: {
    type: DataTypes.ENUM('facebook', 'twitter', 'instagram', 'whatsapp', 'email'),
    allowNull: false
  },
  shareUrl: {
    type: DataTypes.STRING,
    allowNull: false,
    field: 'share_url',
    validate: { isUrl: true }
  },
  bonusEarned: {
    type: DataTypes.DECIMAL(10, 2),
    defaultValue: 0,
    field: 'bonus_earned',
    validate: { min: 0 }
  },
  isVerified: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    field: 'is_verified'
  },
  verifiedAt: {
    type: DataTypes.DATE,
    allowNull: true,
    field: 'verified_at'
  }
});

// Define Wishlist model inline to avoid circular dependency
models.Wishlist = sequelize.define('Wishlist', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  userId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    field: 'user_id',
    references: {
      model: 'Users',
      key: 'id'
    }
  },
  productId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    field: 'product_id',
    references: {
      model: 'Products',
      key: 'id'
    }
  },
  variantId: {
    type: DataTypes.INTEGER,
    allowNull: true,
    field: 'variant_id'
  },
  quantity: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 1,
    validate: { min: 1 }
  },
  notes: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  priority: {
    type: DataTypes.ENUM('low', 'medium', 'high'),
    allowNull: false,
    defaultValue: 'medium'
  },
  isPublic: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false,
    field: 'is_public'
  },
  tags: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: []
  }
});

// Define AuditLog model inline to avoid circular dependency
models.AuditLog = sequelize.define('AuditLog', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  userId: {
    type: DataTypes.INTEGER,
    allowNull: true,
    field: 'user_id',
    references: {
      model: 'Users',
      key: 'id'
    }
  },
  action: {
    type: DataTypes.STRING,
    allowNull: false,
    validate: { len: [1, 100], notEmpty: true }
  },
  resource: {
    type: DataTypes.STRING,
    allowNull: false,
    validate: { len: [1, 100], notEmpty: true }
  },
  resourceId: {
    type: DataTypes.INTEGER,
    allowNull: true,
    field: 'resource_id'
  },
  oldValues: {
    type: DataTypes.JSON,
    allowNull: true,
    field: 'old_values'
  },
  newValues: {
    type: DataTypes.JSON,
    allowNull: true,
    field: 'new_values'
  },
  ipAddress: {
    type: DataTypes.STRING,
    allowNull: true,
    field: 'ip_address',
    validate: {
      isIP: true
    }
  },
  userAgent: {
    type: DataTypes.TEXT,
    allowNull: true,
    field: 'user_agent'
  },
  sessionId: {
    type: DataTypes.STRING,
    allowNull: true,
    field: 'session_id'
  },
  severity: {
    type: DataTypes.ENUM('low', 'medium', 'high', 'critical'),
    allowNull: false,
    defaultValue: 'low'
  },
  status: {
    type: DataTypes.ENUM('success', 'failure', 'pending'),
    allowNull: false,
    defaultValue: 'success'
  },
  errorMessage: {
    type: DataTypes.TEXT,
    allowNull: true,
    field: 'error_message'
  },
  metadata: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: {}
  }
});

// Newsletter Model
models.Newsletter = sequelize.define('Newsletter', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  email: {
    type: DataTypes.STRING,
    allowNull: false,
    unique: true,
    validate: { isEmail: true }
  },
  userId: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: { model: 'Users', key: 'id' }
  },
  isSubscribed: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  },
  subscribedAt: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  unsubscribedAt: {
    type: DataTypes.DATE,
    allowNull: true
  },
  preferences: {
    type: DataTypes.JSON,
    defaultValue: {}
  }
});

// Email Log Model for tracking email delivery
models.EmailLog = sequelize.define('EmailLog', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  orderId: {
    type: DataTypes.INTEGER,
    allowNull: true,
    field: 'order_id'
  },
  emailType: {
    type: DataTypes.ENUM('order-confirmation', 'admin-notification', 'status-update', 'marketing', 'system'),
    allowNull: false,
    field: 'email_type'
  },
  recipient: {
    type: DataTypes.STRING,
    allowNull: false
  },
  subject: {
    type: DataTypes.STRING,
    allowNull: true
  },
  status: {
    type: DataTypes.ENUM('queued', 'sending', 'sent', 'failed', 'retry'),
    defaultValue: 'queued'
  },
  messageId: {
    type: DataTypes.STRING,
    allowNull: true,
    field: 'message_id'
  },
  errorMessage: {
    type: DataTypes.TEXT,
    allowNull: true,
    field: 'error_message'
  },
  attempts: {
    type: DataTypes.INTEGER,
    defaultValue: 0
  },
  metadata: {
    type: DataTypes.JSON,
    defaultValue: {}
  },
  sentAt: {
    type: DataTypes.DATE,
    allowNull: true,
    field: 'sent_at'
  }
});

// System Settings Model for admin email configuration
models.SystemSetting = sequelize.define('SystemSetting', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  key: {
    type: DataTypes.STRING,
    allowNull: false,
    unique: true
  },
  value: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  category: {
    type: DataTypes.STRING,
    defaultValue: 'general'
  },
  isActive: {
    type: DataTypes.BOOLEAN,
    defaultValue: true,
    field: 'is_active'
  }
});

// Add Campaign Models
defineCampaignModels(sequelize, models);

// Define Model Associations
const defineAssociations = () => {
  // User associations
  models.User.hasMany(models.Address, { foreignKey: 'userId', as: 'addresses' });
  models.User.hasMany(models.Order, { foreignKey: 'userId', as: 'orders' });
  models.User.hasMany(models.Review, { foreignKey: 'userId', as: 'reviews' });
  models.User.hasMany(models.Wishlist, { foreignKey: 'userId', as: 'wishlist' });
  models.User.hasOne(models.Cart, { foreignKey: 'userId', as: 'cart' });
  models.User.hasOne(models.Newsletter, { foreignKey: 'userId', as: 'newsletter' });

  // Address associations
  models.Address.belongsTo(models.User, { foreignKey: 'userId', as: 'user' });

  // Category associations
  models.Category.hasMany(models.Product, { foreignKey: 'categoryId', as: 'products' });
  models.Category.hasMany(models.Product, { foreignKey: 'subcategoryId', as: 'subcategoryProducts' });
  models.Category.hasMany(models.Category, { foreignKey: 'parentId', as: 'children' });
  models.Category.belongsTo(models.Category, { foreignKey: 'parentId', as: 'parent' });

  // Product associations
  models.Product.belongsTo(models.Category, { foreignKey: 'categoryId', as: 'category' });
  models.Product.belongsTo(models.Category, { foreignKey: 'subcategoryId', as: 'subcategory' });
  models.Product.hasMany(models.Review, { foreignKey: 'productId', as: 'reviews' });
  models.Product.hasMany(models.CartItem, { foreignKey: 'productId', as: 'cartItems' });
  models.Product.hasMany(models.OrderItem, { foreignKey: 'productId', as: 'orderItems' });
  models.Product.hasMany(models.Wishlist, { foreignKey: 'productId', as: 'wishlists' });

  // Cart associations
  models.Cart.belongsTo(models.User, { foreignKey: 'userId', as: 'user' });
  models.Cart.hasMany(models.CartItem, { foreignKey: 'cartId', as: 'items' });

  // CartItem associations
  models.CartItem.belongsTo(models.Cart, { foreignKey: 'cartId', as: 'cart' });
  models.CartItem.belongsTo(models.Product, { foreignKey: 'productId', as: 'product' });

  // Order associations
  models.Order.belongsTo(models.User, { foreignKey: 'userId', as: 'user' });
  models.Order.hasMany(models.OrderItem, { foreignKey: 'orderId', as: 'items' });
  models.Order.hasMany(models.Review, { foreignKey: 'orderId', as: 'reviews' });

  // OrderItem associations
  models.OrderItem.belongsTo(models.Order, { foreignKey: 'orderId', as: 'order' });
  models.OrderItem.belongsTo(models.Product, { foreignKey: 'productId', as: 'product' });

  // Review associations
  models.Review.belongsTo(models.User, { foreignKey: 'userId', as: 'user' });
  models.Review.belongsTo(models.Product, { foreignKey: 'productId', as: 'product' });
  models.Review.belongsTo(models.Order, { foreignKey: 'orderId', as: 'order' });

  // Wishlist associations
  models.Wishlist.belongsTo(models.User, { foreignKey: 'userId', as: 'user' });
  models.Wishlist.belongsTo(models.Product, { foreignKey: 'productId', as: 'product' });

  // Newsletter associations
  models.Newsletter.belongsTo(models.User, { foreignKey: 'userId', as: 'user' });

  // Coupon associations
  models.User.hasMany(models.Coupon, { foreignKey: 'createdBy', as: 'createdCoupons' });
  models.Coupon.belongsTo(models.User, { foreignKey: 'createdBy', as: 'creator' });
  models.Coupon.hasMany(models.CouponUsage, { foreignKey: 'couponId', as: 'usages' });
  models.Coupon.hasMany(models.SocialShare, { foreignKey: 'couponId', as: 'socialShares' });

  // CouponUsage associations
  models.CouponUsage.belongsTo(models.Coupon, { foreignKey: 'couponId', as: 'coupon' });
  models.CouponUsage.belongsTo(models.User, { foreignKey: 'userId', as: 'user' });
  models.CouponUsage.belongsTo(models.Order, { foreignKey: 'orderId', as: 'order' });
  models.User.hasMany(models.CouponUsage, { foreignKey: 'userId', as: 'couponUsages' });
  models.Order.hasMany(models.CouponUsage, { foreignKey: 'orderId', as: 'couponUsages' });

  // Referral associations
  models.User.hasMany(models.Referral, { foreignKey: 'referrerId', as: 'referralsMade' });
  models.User.hasMany(models.Referral, { foreignKey: 'refereeId', as: 'referralsReceived' });
  models.Referral.belongsTo(models.User, { foreignKey: 'referrerId', as: 'referrer' });
  models.Referral.belongsTo(models.User, { foreignKey: 'refereeId', as: 'referee' });
  models.Referral.belongsTo(models.Order, { foreignKey: 'orderId', as: 'order' });
  models.Order.hasOne(models.Referral, { foreignKey: 'orderId', as: 'referral' });

  // SocialShare associations
  models.User.hasMany(models.SocialShare, { foreignKey: 'userId', as: 'socialShares' });
  models.SocialShare.belongsTo(models.User, { foreignKey: 'userId', as: 'user' });
  models.SocialShare.belongsTo(models.Coupon, { foreignKey: 'couponId', as: 'coupon' });

  // AuditLog associations
  models.User.hasMany(models.AuditLog, { foreignKey: 'userId', as: 'auditLogs' });
  models.AuditLog.belongsTo(models.User, { foreignKey: 'userId', as: 'user' });

  // User-Role associations
  models.User.belongsTo(models.Role, { foreignKey: 'roleId', as: 'role' });
  models.Role.hasMany(models.User, { foreignKey: 'roleId', as: 'users' });
};

// Initialize database
const initializeDatabase = async () => {
  try {
    await sequelize.authenticate();
    console.log('✅ Database connection established successfully');

    defineAssociations();
    console.log('✅ Model associations defined');

    await sequelize.sync({ alter: true });
    console.log('✅ Database synchronized');

    return true;
  } catch (error) {
    console.error('❌ Database initialization failed:', error);
    return false;
  }
};

module.exports = {
  sequelize,
  models,
  defineAssociations,
  initializeDatabase
};
