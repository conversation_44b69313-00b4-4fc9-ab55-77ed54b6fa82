{"program": {"fileNames": ["../../node_modules/typescript/lib/lib.es5.d.ts", "../../node_modules/typescript/lib/lib.es2015.d.ts", "../../node_modules/typescript/lib/lib.es2016.d.ts", "../../node_modules/typescript/lib/lib.es2017.d.ts", "../../node_modules/typescript/lib/lib.es2018.d.ts", "../../node_modules/typescript/lib/lib.dom.d.ts", "../../node_modules/typescript/lib/lib.es2015.core.d.ts", "../../node_modules/typescript/lib/lib.es2015.collection.d.ts", "../../node_modules/typescript/lib/lib.es2015.generator.d.ts", "../../node_modules/typescript/lib/lib.es2015.iterable.d.ts", "../../node_modules/typescript/lib/lib.es2015.promise.d.ts", "../../node_modules/typescript/lib/lib.es2015.proxy.d.ts", "../../node_modules/typescript/lib/lib.es2015.reflect.d.ts", "../../node_modules/typescript/lib/lib.es2015.symbol.d.ts", "../../node_modules/typescript/lib/lib.es2015.symbol.wellknown.d.ts", "../../node_modules/typescript/lib/lib.es2016.array.include.d.ts", "../../node_modules/typescript/lib/lib.es2017.object.d.ts", "../../node_modules/typescript/lib/lib.es2017.sharedmemory.d.ts", "../../node_modules/typescript/lib/lib.es2017.string.d.ts", "../../node_modules/typescript/lib/lib.es2017.intl.d.ts", "../../node_modules/typescript/lib/lib.es2017.typedarrays.d.ts", "../../node_modules/typescript/lib/lib.es2018.asyncgenerator.d.ts", "../../node_modules/typescript/lib/lib.es2018.asynciterable.d.ts", "../../node_modules/typescript/lib/lib.es2018.intl.d.ts", "../../node_modules/typescript/lib/lib.es2018.promise.d.ts", "../../node_modules/typescript/lib/lib.es2018.regexp.d.ts", "../../node_modules/typescript/lib/lib.es2020.bigint.d.ts", "../../node_modules/typescript/lib/lib.es2020.intl.d.ts", "../../node_modules/typescript/lib/lib.esnext.intl.d.ts", "../../node_modules/tslib/tslib.d.ts", "../../node_modules/@types/node/assert/strict.d.ts", "../../node_modules/@types/node/globals.d.ts", "../../node_modules/@types/node/async_hooks.d.ts", "../../node_modules/@types/node/buffer.d.ts", "../../node_modules/@types/node/child_process.d.ts", "../../node_modules/@types/node/cluster.d.ts", "../../node_modules/@types/node/console.d.ts", "../../node_modules/@types/node/constants.d.ts", "../../node_modules/@types/node/crypto.d.ts", "../../node_modules/@types/node/dgram.d.ts", "../../node_modules/@types/node/dns.d.ts", "../../node_modules/@types/node/dns/promises.d.ts", "../../node_modules/@types/node/domain.d.ts", "../../node_modules/@types/node/events.d.ts", "../../node_modules/@types/node/fs.d.ts", "../../node_modules/@types/node/fs/promises.d.ts", "../../node_modules/@types/node/http.d.ts", "../../node_modules/@types/node/http2.d.ts", "../../node_modules/@types/node/https.d.ts", "../../node_modules/@types/node/inspector.d.ts", "../../node_modules/@types/node/module.d.ts", "../../node_modules/@types/node/net.d.ts", "../../node_modules/@types/node/os.d.ts", "../../node_modules/@types/node/path.d.ts", "../../node_modules/@types/node/perf_hooks.d.ts", "../../node_modules/@types/node/process.d.ts", "../../node_modules/@types/node/punycode.d.ts", "../../node_modules/@types/node/querystring.d.ts", "../../node_modules/@types/node/readline.d.ts", "../../node_modules/@types/node/repl.d.ts", "../../node_modules/@types/node/stream.d.ts", "../../node_modules/@types/node/stream/promises.d.ts", "../../node_modules/@types/node/string_decoder.d.ts", "../../node_modules/@types/node/timers.d.ts", "../../node_modules/@types/node/timers/promises.d.ts", "../../node_modules/@types/node/tls.d.ts", "../../node_modules/@types/node/trace_events.d.ts", "../../node_modules/@types/node/tty.d.ts", "../../node_modules/@types/node/url.d.ts", "../../node_modules/@types/node/util.d.ts", "../../node_modules/@types/node/v8.d.ts", "../../node_modules/@types/node/vm.d.ts", "../../node_modules/@types/node/worker_threads.d.ts", "../../node_modules/@types/node/zlib.d.ts", "../../node_modules/@types/node/globals.global.d.ts", "../../node_modules/@types/node/wasi.d.ts", "../../node_modules/@types/node/ts3.6/base.d.ts", "../../node_modules/@types/node/assert.d.ts", "../../node_modules/@types/node/base.d.ts", "../../node_modules/@types/node/index.d.ts", "./node_modules/agent-base/dist/helpers.d.ts", "./node_modules/agent-base/dist/index.d.ts", "./node_modules/http-proxy-agent/dist/index.d.ts", "./node_modules/https-proxy-agent/dist/index.d.ts", "./src/proxyAgent.ts", "./src/index.ts", "./src/proxyAgentBrowser.ts", "../../node_modules/@types/babel__generator/node_modules/@babel/types/lib/index.d.ts", "../../node_modules/@types/babel__generator/index.d.ts", "../../node_modules/@babel/types/lib/index.d.ts", "../../node_modules/@babel/parser/typings/babel-parser.d.ts", "../../node_modules/@types/babel__template/node_modules/@babel/types/lib/index.d.ts", "../../node_modules/@types/babel__template/node_modules/@babel/parser/typings/babel-parser.d.ts", "../../node_modules/@types/babel__template/index.d.ts", "../../node_modules/@types/babel__traverse/node_modules/@babel/types/lib/index.d.ts", "../../node_modules/@types/babel__traverse/index.d.ts", "../../node_modules/@types/babel__core/index.d.ts", "../../node_modules/@types/detect-node/index.d.ts", "../../node_modules/@types/graceful-fs/index.d.ts", "../../node_modules/@types/istanbul-lib-coverage/index.d.ts", "../../node_modules/@types/istanbul-lib-report/index.d.ts", "../../node_modules/@types/istanbul-reports/index.d.ts", "../../node_modules/@types/jest/node_modules/jest-diff/build/types.d.ts", "../../node_modules/@types/jest/node_modules/jest-diff/build/index.d.ts", "../../node_modules/@types/jest/index.d.ts", "../../node_modules/@types/lodash/common/common.d.ts", "../../node_modules/@types/lodash/common/array.d.ts", "../../node_modules/@types/lodash/common/collection.d.ts", "../../node_modules/@types/lodash/common/date.d.ts", "../../node_modules/@types/lodash/common/function.d.ts", "../../node_modules/@types/lodash/common/lang.d.ts", "../../node_modules/@types/lodash/common/math.d.ts", "../../node_modules/@types/lodash/common/number.d.ts", "../../node_modules/@types/lodash/common/object.d.ts", "../../node_modules/@types/lodash/common/seq.d.ts", "../../node_modules/@types/lodash/common/string.d.ts", "../../node_modules/@types/lodash/common/util.d.ts", "../../node_modules/@types/lodash/index.d.ts", "../../node_modules/@types/lodash.defaultsdeep/index.d.ts", "../../node_modules/@types/lodash.flatmap/index.d.ts", "../../node_modules/@types/minimatch/index.d.ts", "../../node_modules/@types/minimist/index.d.ts", "../../node_modules/@types/normalize-package-data/index.d.ts", "../../node_modules/@types/parse-json/index.d.ts", "../../node_modules/@types/prettier/index.d.ts", "../../node_modules/@types/stack-utils/index.d.ts", "../../node_modules/@types/xml2js/lib/processors.d.ts", "../../node_modules/@types/xml2js/index.d.ts", "../../node_modules/@types/yargs-parser/index.d.ts", "../../node_modules/@types/yargs/index.d.ts"], "fileInfos": [{"version": "8730f4bf322026ff5229336391a18bcaa1f94d4f82416c8b2f3954e2ccaae2ba", "affectsGlobalScope": true}, "dc47c4fa66b9b9890cf076304de2a9c5201e94b740cffdf09f87296d877d71f6", "7a387c58583dfca701b6c85e0adaf43fb17d590fb16d5b2dc0a2fbd89f35c467", "8a12173c586e95f4433e0c6dc446bc88346be73ffe9ca6eec7aa63c8f3dca7f9", "5f4e733ced4e129482ae2186aae29fde948ab7182844c3a5a51dd346182c7b06", {"version": "3aafcb693fe5b5c3bd277bd4c3a617b53db474fe498fc5df067c5603b1eebde7", "affectsGlobalScope": true}, {"version": "adb996790133eb33b33aadb9c09f15c2c575e71fb57a62de8bf74dbf59ec7dfb", "affectsGlobalScope": true}, {"version": "8cc8c5a3bac513368b0157f3d8b31cfdcfe78b56d3724f30f80ed9715e404af8", "affectsGlobalScope": true}, {"version": "cdccba9a388c2ee3fd6ad4018c640a471a6c060e96f1232062223063b0a5ac6a", "affectsGlobalScope": true}, {"version": "c5c05907c02476e4bde6b7e76a79ffcd948aedd14b6a8f56e4674221b0417398", "affectsGlobalScope": true}, {"version": "5f406584aef28a331c36523df688ca3650288d14f39c5d2e555c95f0d2ff8f6f", "affectsGlobalScope": true}, {"version": "22f230e544b35349cfb3bd9110b6ef37b41c6d6c43c3314a31bd0d9652fcec72", "affectsGlobalScope": true}, {"version": "7ea0b55f6b315cf9ac2ad622b0a7813315bb6e97bf4bb3fbf8f8affbca7dc695", "affectsGlobalScope": true}, {"version": "3013574108c36fd3aaca79764002b3717da09725a36a6fc02eac386593110f93", "affectsGlobalScope": true}, {"version": "eb26de841c52236d8222f87e9e6a235332e0788af8c87a71e9e210314300410a", "affectsGlobalScope": true}, {"version": "3be5a1453daa63e031d266bf342f3943603873d890ab8b9ada95e22389389006", "affectsGlobalScope": true}, {"version": "17bb1fc99591b00515502d264fa55dc8370c45c5298f4a5c2083557dccba5a2a", "affectsGlobalScope": true}, {"version": "7ce9f0bde3307ca1f944119f6365f2d776d281a393b576a18a2f2893a2d75c98", "affectsGlobalScope": true}, {"version": "6a6b173e739a6a99629a8594bfb294cc7329bfb7b227f12e1f7c11bc163b8577", "affectsGlobalScope": true}, {"version": "81cac4cbc92c0c839c70f8ffb94eb61e2d32dc1c3cf6d95844ca099463cf37ea", "affectsGlobalScope": true}, {"version": "b0124885ef82641903d232172577f2ceb5d3e60aed4da1153bab4221e1f6dd4e", "affectsGlobalScope": true}, {"version": "0eb85d6c590b0d577919a79e0084fa1744c1beba6fd0d4e951432fa1ede5510a", "affectsGlobalScope": true}, {"version": "da233fc1c8a377ba9e0bed690a73c290d843c2c3d23a7bd7ec5cd3d7d73ba1e0", "affectsGlobalScope": true}, {"version": "d154ea5bb7f7f9001ed9153e876b2d5b8f5c2bb9ec02b3ae0d239ec769f1f2ae", "affectsGlobalScope": true}, {"version": "bb2d3fb05a1d2ffbca947cc7cbc95d23e1d053d6595391bd325deb265a18d36c", "affectsGlobalScope": true}, {"version": "c80df75850fea5caa2afe43b9949338ce4e2de086f91713e9af1a06f973872b8", "affectsGlobalScope": true}, {"version": "09aa50414b80c023553090e2f53827f007a301bc34b0495bfb2c3c08ab9ad1eb", "affectsGlobalScope": true}, {"version": "2768ef564cfc0689a1b76106c421a2909bdff0acbe87da010785adab80efdd5c", "affectsGlobalScope": true}, {"version": "52d1bb7ab7a3306fd0375c8bff560feed26ed676a5b0457fa8027b563aecb9a4", "affectsGlobalScope": true}, "a6a5253138c5432c68a1510c70fe78a644fe2e632111ba778e1978010d6edfec", "c7bdc99177a2a94d25fb13722adaaf5b3291bf70b4d1b27584ba189dd3889ba3", {"version": "5402314c88d0127f63f94a0272f79e04ea0fc010ff6da6613807504c4163a1ad", "affectsGlobalScope": true}, "d20f08527645f62facb2d66c2b7bd31ea964b59c897d00bddb1efe8c13890b72", "5726b5ce952dc5beaeb08d5f64236632501568a54a390363d2339ba1dc5393b1", "674bedbfd2004e233e2a266a3d2286e524f0d58787a98522d834d6ccda1d215a", "714637d594e1a38a075091fe464ca91c6abc0b154784b4287f6883200e28ccef", {"version": "23edba5f47d3409810c563fe8034ae2c59e718e1ef8570f4152ccdde1915a096", "affectsGlobalScope": true}, "0e9c55f894ca2d9cf63b5b0d43a8cec1772dd560233fd16275bc7a485eb82f83", "4105385afd60b90ebac359d02a186250885839122850d776c0ad0e327c1afb65", "5f0a09de75bd965c21dc6d73671ba88830272f9ed62897bb0aa9754b369b1eed", "12b2ab05a3ededc0e8938f2e16e4e2e30fc82b6d97b414067c26253f26fce69a", "e4ab7077b5adff3156d258a284986dc85e0bcf0fff1670df5c7f83efc25d4cc6", {"version": "06d2be99c3dd2ff52114d02ee443ba486ab482423df1941d3c97d6a92e924d70", "affectsGlobalScope": true}, {"version": "eb15e56611c2a853526a2e202234dd1676bd37cc2fcdbd9843470f7dafb37f15", "affectsGlobalScope": true}, "2e0b4284620082f63b144da8096b207e9e9590c0e599834332b90624db0cc4a8", "c98ce957db9eebd75f53edda3f6893e05ab2d2283b5667b18e31bcdb6427ed10", "37eed30fc8318b8ac76eac6f41d0758a9d0bffd8f3ff353e3ad0f8717dd08d92", "2f69728fd1ca1f381879bbf20a42ae47a8f7286e000afd138c6cf870d90d882f", "1978992206803f5761e99e893d93b25abc818c5fe619674fdf2ae02b29f641ba", "05fbe81f09fc455a2c343d2458d2b3c600c90b92b22926be765ee79326be9466", "8e7d6dae9e19bbe47600dcfd4418db85b30ae7351474ea0aad5e628f9845d340", "2c381d36201776828c67a307ad5fd8cbcf9ecaffb1fc7f77f7ce433d1a632b7f", "32542c4660ecda892a333a533feedba31738ee538ef6a78eb73af647137bc3fc", "0ecacea5047d1a7d350e7049dbd22f26435be5e8736a81a56afec5b3264db1ca", "ffcb4ebde21f83370ed402583888b28651d2eb7f05bfec9482eb46d82adedd7f", {"version": "fcb95c45150c717706119f12f2a3639d51baa041cd5bb441eb8501e04b52c501", "affectsGlobalScope": true}, "a7b43c69f9602d198825e403ee34e5d64f83c48b391b2897e8c0e6f72bca35f8", "f4a3fc4efc6944e7b7bd4ccfa45e0df68b6359808e6cf9d061f04fd964a7b2d3", "73cad675aead7a2c05cf934e7e700c61d84b2037ac1d576c3f751199b25331da", "8c3137ba3583ec18484429ec1c8eff89efdc42730542f157b38b102fdccc0c71", "2b6906b19436e07d874a51a5829d94ab690337c4ee652735ab422a8f102168be", "1b98a8704d0c68520ccb02ac44782b7ffdaab40d23d2fa00e13923b528587f8b", "94ca7beec4e274d32362b54e0133152f7b4be9487db7b005070c03880b6363aa", "911175d5a29fce5f6f471bcab94524474a1f99eec9cb86fe96505a40ce75f972", "8b4f4519834b57645d2483af74d6f5d1675260a5b0e9aa6026f3e021edd2c5e9", "bbf21f210782db4193359010a4710786add43e3b50aa42fc0d371f45b4e4d8d3", "0b7733d83619ac4e3963e2a9f7c75dc1e9af6850cb2354c9554977813092c10a", "3ce933f0c3955f67f67eb7d6b5c83c2c54a18472c1d6f2bb651e51dd40c84837", "631e96db896d645f7132c488ad34a16d71fd2be9f44696f8c98289ee1c8cbfa9", "2c77230d381cba81eb6f87cda2fbfff6c0427c6546c2e2590110effff37c58f7", "da86ee9a2f09a4583db1d5e37815894967e1f694ad9f3c25e84e0e4d40411e14", "141a943e5690105898a67537a470f70b56d0e183441b56051d929e902376b7b2", "0124e458b12ba82b08c87220a1c9d4fb7701dcda8f11e28f7d7266281501bcba", "515ef1d99036ff0dafa5bf738e02222edea94e0d97a0aa0ff277ac5e96b57977", {"version": "2708349d5a11a5c2e5f3a0765259ebe7ee00cdcc8161cb9990cb4910328442a1", "affectsGlobalScope": true}, "780058f4a804c8bdcdd2f60e7af64b2bc57d149c1586ee3db732a84d659a50bf", "210ef68f34baca2a4499c07a51f05d51b4f0ef01d64efea3017cb3bc31c37e33", "19d580a3b42ad5caeaee266ae958260e23f2df0549ee201c886c8bd7a4f01d4e", "e61a21e9418f279bc480394a94d1581b2dee73747adcbdef999b6737e34d721b", "6c9c7e459e013ddf52c70b90f88bbdd925e483ef984d80f9bffb501029974e82", "d159a1bf12aed961258abcb9bc26ceaa615db573662be64f7e9ce136db1fb265", "86cc3effc64d89018e0615a7026f9298c17d162cde9bce65b9c4b5b2aebdb40a", "549f76596f8246ed1f027da416528405517d96103486577f38f59bcddddc3675", "00e87b6148eab98378cb1daf51383be23a0c9ad59f5ed4afdb859212773115d4", {"version": "d570866180f694c6033e37f95e7793f2bd505f8b9bf769d4ed7788d24e94c386", "signature": "1421089b5c58cb1e7bca645a07314fb26e7cb66691744a27214cb551794e0e58"}, {"version": "b9f323e6036d07db2723d7d1869db6d95a1372f2d19ced1d39aaaf8a1416d315", "signature": "1c072940012deb9788eae7663fdc2cb6c09fb6dfe69021cb1b02c8a993e6ab93"}, {"version": "a574d051128f3ccd86be871f5cf2fc390010d258c6925a1310a0b1733e25cc58", "signature": "20d49e4d025b32e663c9caefd2c3a70abbdddfd399f922c5215d330d4299177c"}, "4606c6d854002b409744140a996a49c4a607c1eb0396855529836161892abef5", "b25c5f2970d06c729f464c0aeaa64b1a5b5f1355aa93554bb5f9c199b8624b1e", "d88b3dc8b7055665059ea06ffafce9467fc4bdfa7cb2d7a6f4262556bb482b0d", "32ddc6ad753ae79571bbf28cebff7a383bf7f562ac5ef5d25c94ef7f71609d49", "4606c6d854002b409744140a996a49c4a607c1eb0396855529836161892abef5", "3e6297bcddf37e373d40ddb4c2b9e6fc98901b2a44c01b2d96af142874973c43", "3051751533eee92572241b3cef28333212401408c4e7aa21718714b793c0f4ed", "4606c6d854002b409744140a996a49c4a607c1eb0396855529836161892abef5", "691aea9772797ca98334eb743e7686e29325b02c6931391bcee4cc7bf27a9f3b", "069bebfee29864e3955378107e243508b163e77ab10de6a5ee03ae06939f0bb9", "da3a46e9656ec027161c580cbc053b749827c73679529defeab307fb13a42986", "afe73051ff6a03a9565cbd8ebb0e956ee3df5e913ad5c1ded64218aabfa3dcb5", "de18acda71730bac52f4b256ce7511bb56cc21f6f114c59c46782eff2f632857", "7eb06594824ada538b1d8b48c3925a83e7db792f47a081a62cf3e5c4e23cf0ee", "905c3e8f7ddaa6c391b60c05b2f4c3931d7127ad717a080359db3df510b7bdab", "71ba0678a3c647f5c0706ae975c031ace0d464e60f9ce56eaa7f1678d065aab7", "162c6f2951bd80ae6e16679378f382200b26bba9de8f255af3a2895fbfa670b4", {"version": "375c56b1890858dcdae495877f0ce3b694980adf64762e2fd3fbe78e3eac76ba", "affectsGlobalScope": true}, "3594c022901a1c8993b0f78a3f534cfb81e7b619ed215348f7f6882f3db02abc", "438284c7c455a29b9c0e2d1e72abc62ee93d9a163029ffe918a34c5db3b92da2", "0c75b204aed9cf6ff1c7b4bed87a3ece0d9d6fc857a6350c0c95ed0c38c814e8", "187119ff4f9553676a884e296089e131e8cc01691c546273b1d0089c3533ce42", "c9f396e71966bd3a890d8a36a6a497dbf260e9b868158ea7824d4b5421210afe", "509235563ea2b939e1bbe92aae17e71e6a82ceab8f568b45fb4fce7d72523a32", "9364c7566b0be2f7b70ff5285eb34686f83ccb01bda529b82d23b2a844653bfb", "00baffbe8a2f2e4875367479489b5d43b5fc1429ecb4a4cc98cfc3009095f52a", "c311349ec71bb69399ffc4092853e7d8a86c1ca39ddb4cd129e775c19d985793", "3c92b6dfd43cc1c2485d9eba5ff0b74a19bb8725b692773ef1d66dac48cda4bd", "4908e4c00832b26ce77a629de8501b0e23a903c094f9e79a7fec313a15da796a", "2630a7cbb597e85d713b7ef47f2946d4280d3d4c02733282770741d40672b1a5", {"version": "0714e2046df66c0e93c3330d30dbc0565b3e8cd3ee302cf99e4ede6220e5fec8", "affectsGlobalScope": true}, "9c2adcc35f70903e3fec1fd59960bfeb0431cf68264c1f9835578012199e4686", "062af2e3340d7ede501508fca2e308183bfaf280bffe5a5387395b4190cead81", "8841e2aa774b89bd23302dede20663306dc1b9902431ac64b24be8b8d0e3f649", "e437d83044ba17246a861aa9691aa14223ff4a9d6f338ab1269c41c758586a88", "c9ad058b2cc9ce6dc2ed92960d6d009e8c04bef46d3f5312283debca6869f613", "2b8264b2fefd7367e0f20e2c04eed5d3038831fe00f5efbc110ff0131aab899b", "d88a5e779faf033be3d52142a04fbe1cb96009868e3bbdd296b2bc6c59e06c0e", "c6c4fea9acc55d5e38ff2b70d57ab0b5cdbd08f8bc5d7a226e322cea128c5b57", "c0288f54de6f544706a3150c8b579b1a975870695c4be866f727ece6a16f3976", "ec2c7505ede4bc3ee37cfe431cfd87e545f55f27fe15b0809c1a411765145615", "3bdd93ec24853e61bfa4c63ebaa425ff3e474156e87a47d90122e1d8cc717c1f", "ae271d475b632ce7b03fea6d9cf6da72439e57a109672671cbc79f54e1386938"], "options": {"allowSyntheticDefaultImports": true, "composite": true, "declaration": true, "declarationMap": true, "downlevelIteration": true, "esModuleInterop": true, "experimentalDecorators": true, "importHelpers": true, "jsx": 2, "noUnusedLocals": true, "noUnusedParameters": true, "outDir": "./lib", "rootDir": "./src", "skipLibCheck": true, "strictNullChecks": true, "strictPropertyInitialization": true, "target": 1}, "fileIdsList": [[90], [89, 90, 91, 94, 96], [88], [88, 93], [45, 80], [100], [101], [104], [103], [118], [106, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118], [106, 107, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118], [107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118], [106, 107, 108, 110, 111, 112, 113, 114, 115, 116, 117, 118], [106, 107, 108, 109, 111, 112, 113, 114, 115, 116, 117, 118], [106, 107, 108, 109, 110, 112, 113, 114, 115, 116, 117, 118], [106, 107, 108, 109, 110, 111, 113, 114, 115, 116, 117, 118], [106, 107, 108, 109, 110, 111, 112, 114, 115, 116, 117, 118], [106, 107, 108, 109, 110, 111, 112, 113, 115, 116, 117, 118], [106, 107, 108, 109, 110, 111, 112, 113, 114, 116, 117, 118], [106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 117, 118], [106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 118], [106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117], [78], [33], [77, 78], [34], [35, 44, 45, 52, 61], [35, 36, 44, 52], [70], [38, 39, 45, 53], [39, 61], [40, 41, 44, 52], [41, 42], [43, 44], [44], [44, 45, 46, 61, 69], [45, 46], [47, 52, 61, 69], [44, 45, 47, 48, 52, 61, 66, 69], [47, 49, 66, 69], [79], [44, 50], [51, 69], [41, 44, 52, 61], [53], [54], [33, 55], [68], [57], [58], [44, 59], [59, 60, 70, 72], [44, 61, 62], [61, 62], [63], [64], [64, 65], [52, 66], [67], [31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76], [52, 68], [58, 69], [61, 71], [72], [76], [44, 46, 61, 69, 72, 73], [61, 74], [44, 80, 127], [129], [47, 49, 61, 80], [47, 52, 61, 66, 80, 81], [47, 52, 66, 69, 80, 82], [30, 85], [30, 83, 84, 86], [30, 86], [85], [86]], "referencedMap": [[91, 1], [97, 2], [89, 3], [94, 4], [93, 3], [96, 3], [99, 5], [101, 6], [102, 7], [105, 8], [104, 9], [119, 10], [120, 10], [107, 11], [108, 12], [106, 13], [109, 14], [110, 15], [111, 16], [112, 17], [113, 18], [114, 19], [115, 20], [116, 21], [117, 22], [118, 23], [78, 24], [31, 24], [33, 25], [79, 26], [34, 27], [35, 28], [36, 29], [37, 30], [38, 31], [39, 32], [40, 33], [41, 34], [42, 34], [43, 35], [44, 36], [45, 37], [46, 38], [47, 39], [48, 40], [49, 41], [80, 42], [50, 43], [51, 44], [52, 45], [53, 46], [54, 47], [55, 48], [56, 49], [57, 50], [58, 51], [59, 52], [60, 53], [61, 54], [62, 55], [63, 56], [64, 57], [65, 58], [66, 59], [67, 60], [77, 61], [68, 62], [69, 63], [70, 30], [71, 64], [72, 65], [76, 66], [73, 67], [74, 68], [128, 69], [130, 70], [81, 71], [82, 72], [83, 73], [84, 73], [86, 74], [85, 75], [87, 76]], "exportedModulesMap": [[91, 1], [97, 2], [89, 3], [94, 4], [93, 3], [96, 3], [99, 5], [101, 6], [102, 7], [105, 8], [104, 9], [119, 10], [120, 10], [107, 11], [108, 12], [106, 13], [109, 14], [110, 15], [111, 16], [112, 17], [113, 18], [114, 19], [115, 20], [116, 21], [117, 22], [118, 23], [78, 24], [31, 24], [33, 25], [79, 26], [34, 27], [35, 28], [36, 29], [37, 30], [38, 31], [39, 32], [40, 33], [41, 34], [42, 34], [43, 35], [44, 36], [45, 37], [46, 38], [47, 39], [48, 40], [49, 41], [80, 42], [50, 43], [51, 44], [52, 45], [53, 46], [54, 47], [55, 48], [56, 49], [57, 50], [58, 51], [59, 52], [60, 53], [61, 54], [62, 55], [63, 56], [64, 57], [65, 58], [66, 59], [67, 60], [77, 61], [68, 62], [69, 63], [70, 30], [71, 64], [72, 65], [76, 66], [73, 67], [74, 68], [128, 69], [130, 70], [81, 71], [82, 72], [83, 73], [84, 73], [86, 77], [85, 78], [87, 78]], "semanticDiagnosticsPerFile": [91, 90, 97, 89, 88, 94, 93, 92, 96, 95, 98, 99, 100, 101, 102, 105, 104, 103, 119, 120, 107, 108, 106, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 121, 122, 78, 31, 33, 79, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 32, 75, 47, 48, 49, 80, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 77, 68, 69, 70, 71, 72, 76, 73, 74, 123, 124, 125, 126, 128, 127, 129, 130, 30, 6, 8, 7, 2, 9, 10, 11, 12, 13, 14, 15, 16, 3, 4, 20, 17, 18, 19, 21, 22, 23, 5, 24, 25, 26, 27, 28, 1, 29, 81, 82, 83, 84, 86, 85, 87], "latestChangedDtsFile": "./lib/proxyAgentBrowser.d.ts"}, "version": "4.9.5"}