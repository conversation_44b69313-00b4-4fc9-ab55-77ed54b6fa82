# Nirvana Organics - Comprehensive Test Deployment

## Overview

The `deploy.sh` script is a comprehensive deployment solution that handles the complete setup and configuration of the Nirvana Organics test environment at `test.shopnirvanaorganics.com`.

## What This Script Does

### 🏗️ **Complete Deployment Pipeline**
- **Asset Deployment**: Deploys pre-built frontend assets to `/var/www/nirvana-frontend-test/`
- **Nginx Configuration**: Updates and deploys Nginx configuration with proper location blocks
- **Database Fixes**: Resolves schema issues including the missing `is_active` column in products table
- **Backend Services**: Manages PM2 processes for both main and admin backend services
- **Permissions**: Sets correct file ownership and permissions throughout the system

### 🔧 **Specific Fixes Included**
- **Admin Assets 404 Fix**: Resolves Nginx location block conflicts that caused admin JavaScript and CSS files to return 404 errors
- **Database Schema Fix**: Adds missing `isActive` field to Product model and ensures database compatibility
- **Service Management**: Properly restarts and configures backend services with correct environment variables

### 🧪 **Comprehensive Testing**
- HTTP endpoint testing for all frontend and backend services
- Database connectivity and query testing
- File permission verification
- Asset accessibility verification
- Detailed reporting and logging

## Usage

### Prerequisites
- Must be run as root (uses `sudo`)
- Nirvana user must exist on the system
- **Frontend applications must be built externally** (dist/ and dist-admin/ directories must exist)
- Node.js and npm must be installed (for backend services)
- PM2 must be installed globally
- Nginx must be installed and running
- MySQL/MariaDB must be running with proper credentials

### Running the Deployment

```bash
# Navigate to the nirvana-test directory
cd /var/www/nirvana-backend-test/nirvana-test

# Make the script executable (on Linux/Unix systems)
chmod +x deploy.sh

# Run the comprehensive deployment
sudo ./deploy.sh
```

### What Happens During Deployment

1. **Pre-flight Checks**
   - Verifies root privileges
   - Checks for Nirvana user existence
   - Validates required files are present

2. **Backup Creation**
   - Creates timestamped backup of current deployment
   - Backs up frontend assets and Nginx configuration

3. **Frontend Build Process**
   - Installs npm dependencies
   - Builds main frontend (`npm run build:test`)
   - Builds admin frontend (`npm run build:admin:test`)

4. **Asset Deployment**
   - Deploys main frontend to `/var/www/nirvana-frontend-test/main/`
   - Deploys admin frontend to `/var/www/nirvana-frontend-test/admin/`
   - Sets proper file permissions and ownership

5. **Database Schema Fixes**
   - Runs `fix-products-is-active-column.js` to ensure database compatibility
   - Adds missing columns and indexes as needed

6. **Nginx Configuration**
   - Deploys updated `nginx-site.conf` to sites-available
   - Enables the site configuration
   - Tests configuration syntax
   - Reloads Nginx service

7. **Backend Service Management**
   - Installs backend dependencies
   - Stops existing PM2 processes
   - Starts services using ecosystem configuration
   - Saves PM2 configuration

8. **Comprehensive Testing**
   - Tests all HTTP endpoints (main site, admin panel, assets, APIs)
   - Verifies database connectivity
   - Checks file permissions
   - Generates detailed deployment report

## Output and Logging

### Console Output
The script provides colored, real-time output showing:
- ✅ Successful operations
- ⚠️ Warnings for non-critical issues
- ❌ Errors that require attention
- ℹ️ Informational messages
- 🔧 Step headers for major operations

### Log Files
- **Deployment Log**: `/var/log/deploy/test-YYYYMMDD-HHMMSS.log`
- **Deployment Report**: `/var/log/deploy/deployment-report-YYYYMMDD-HHMMSS.txt`

### Backup Location
- **Backup Directory**: `/var/backups/nirvana-organics-test/backup-YYYYMMDD-HHMMSS/`

## Success Criteria

The deployment is considered successful when all of the following tests pass:

1. **Main Frontend**: `https://test.shopnirvanaorganics.com/` returns HTTP 200
2. **Admin Frontend**: `https://test.shopnirvanaorganics.com/admin/` returns HTTP 200
3. **Admin JavaScript**: `https://test.shopnirvanaorganics.com/admin/assets/admin-*.js` returns HTTP 200
4. **Admin CSS**: `https://test.shopnirvanaorganics.com/admin/assets/admin-*.css` returns HTTP 200
5. **Backend API**: `https://test.shopnirvanaorganics.com/api/health` returns HTTP 200
6. **Database Query**: `SELECT COUNT(*) FROM products WHERE is_active = 1` executes successfully

## Troubleshooting

### Common Issues

**Permission Denied Errors**
```bash
# Ensure you're running as root
sudo ./deploy.sh
```

**Missing Dependencies**
```bash
# Install Node.js dependencies manually
sudo -u Nirvana npm install
```

**Nginx Configuration Errors**
```bash
# Test Nginx configuration
sudo nginx -t

# Check Nginx error logs
sudo tail -f /var/log/nginx/error.log
```

**PM2 Service Issues**
```bash
# Check PM2 status
sudo -u Nirvana pm2 status

# View PM2 logs
sudo -u Nirvana pm2 logs
```

**Database Connection Issues**
```bash
# Test database connection
sudo -u Nirvana node -e "
const { Sequelize } = require('sequelize');
require('dotenv').config({ path: './.env.test' });
const sequelize = new Sequelize(process.env.DB_NAME, process.env.DB_USER, process.env.DB_PASSWORD, {
  host: process.env.DB_HOST,
  dialect: 'mysql'
});
sequelize.authenticate().then(() => console.log('Connected')).catch(console.error);
"
```

### Rollback Procedure

If deployment fails, the script automatically attempts to rollback to the most recent backup. Manual rollback can be performed by:

```bash
# Find the latest backup
ls -la /var/backups/nirvana-organics-test/

# Restore from backup (replace TIMESTAMP with actual backup timestamp)
sudo cp -r /var/backups/nirvana-organics-test/backup-TIMESTAMP/frontend/* /var/www/nirvana-frontend-test/
sudo cp /var/backups/nirvana-organics-test/backup-TIMESTAMP/nginx-config.conf /etc/nginx/sites-available/nirvana-organics-test
sudo nginx -t && sudo systemctl reload nginx
```

## Post-Deployment Verification

After successful deployment, verify the following:

1. **Browse to the main site**: https://test.shopnirvanaorganics.com/
2. **Access the admin panel**: https://test.shopnirvanaorganics.com/admin/
3. **Check browser console**: Should show no 404 errors for admin assets
4. **Test admin functionality**: Login and verify all features work
5. **Monitor backend services**: `sudo -u Nirvana pm2 monit`

## Script Consolidation

This script replaces all previous individual deployment scripts:
- ~~deploy-frontend-assets.sh~~
- ~~fix-nginx-admin-assets.sh~~
- ~~test-admin-assets-fix.sh~~
- ~~deploy-nginx.sh~~
- ~~restart-services.sh~~
- ~~set-permissions.sh~~
- ~~verify-fix.sh~~
- And many others...

All functionality has been consolidated into this single, comprehensive deployment solution.
