const passport = require('passport');
const GoogleStrategy = require('passport-google-oauth20').Strategy;
const OAuth2Strategy = require('passport-oauth2').Strategy;

const LocalStrategy = require('passport-local').Strategy;
const bcrypt = require('bcryptjs');
const { User, Role } = require('../models');

/**
 * Passport Configuration for Nirvana Organics
 * Supports Google OAuth, Square OAuth, and Local authentication
 */

// Serialize user for session
passport.serializeUser((user, done) => {
  done(null, user.id);
});

// Deserialize user from session
passport.deserializeUser(async (id, done) => {
  try {
    const user = await User.findByPk(id, {
      include: [{ model: Role, as: 'Role' }]
    });
    done(null, user);
  } catch (error) {
    done(error, null);
  }
});

// Local Strategy for email/password authentication
passport.use(new LocalStrategy({
  usernameField: 'email',
  passwordField: 'password'
}, async (email, password, done) => {
  try {
    const user = await User.findOne({
      where: { email: email.toLowerCase() },
      include: [{ model: Role, as: 'Role' }]
    });

    if (!user) {
      return done(null, false, { message: 'Invalid email or password' });
    }

    const isValidPassword = await bcrypt.compare(password, user.password);
    if (!isValidPassword) {
      return done(null, false, { message: 'Invalid email or password' });
    }

    if (!user.isVerified) {
      return done(null, false, { message: 'Please verify your email address before logging in' });
    }

    return done(null, user);
  } catch (error) {
    return done(error);
  }
}));

// Google OAuth Strategy
const getCallbackURL = () => {
  // Use environment-specific callback URL
  if (process.env.GOOGLE_OAUTH_CALLBACK_URL) {
    return process.env.GOOGLE_OAUTH_CALLBACK_URL;
  }

  // Default based on environment
  const baseUrl = process.env.BACKEND_URL || 'http://localhost:5000';
  return `${baseUrl}/api/auth/google/callback`;
};

// Only configure Google OAuth if credentials are provided
if (process.env.GOOGLE_CLIENT_ID && process.env.GOOGLE_CLIENT_SECRET) {
  passport.use(new GoogleStrategy({
    clientID: process.env.GOOGLE_CLIENT_ID,
    clientSecret: process.env.GOOGLE_CLIENT_SECRET,
    callbackURL: getCallbackURL()
  }, async (accessToken, refreshToken, profile, done) => {
  try {
    // Check if user already exists with this Google ID
    let user = await User.findOne({
      where: { googleId: profile.id },
      include: [{ model: Role, as: 'Role' }]
    });

    if (user) {
      return done(null, user);
    }

    // Check if user exists with the same email
    const email = profile.emails && profile.emails[0] ? profile.emails[0].value : null;
    if (email) {
      user = await User.findOne({
        where: { email: email.toLowerCase() },
        include: [{ model: Role, as: 'Role' }]
      });

      if (user) {
        // Check if this should be an admin user
        const adminEmails = [
          process.env.ADMIN_EMAIL,
          process.env.ADMIN_EMAIL_SECONDARY
        ].filter(Boolean).map(email => email.toLowerCase());

        const isAdminEmail = adminEmails.includes(email.toLowerCase());

        // If this is an admin email but user doesn't have admin role, upgrade them
        if (isAdminEmail && user.Role && user.Role.name !== 'admin') {
          let adminRole = await Role.findOne({ where: { name: 'admin' } });
          if (!adminRole) {
            // Create admin role if it doesn't exist
            adminRole = await Role.create({
              name: 'admin',
              displayName: 'Administrator',
              description: 'System administrator with full access',
              permissions: JSON.stringify(['*']),
              priority: 100,
              isSystemRole: true
            });
          }
          user.roleId = adminRole.id;
        }

        // Link Google account to existing user
        user.googleId = profile.id;
        user.isVerified = true; // Google accounts are pre-verified
        await user.save();

        // Reload user with updated role information
        const updatedUser = await User.findByPk(user.id, {
          include: [{ model: Role, as: 'Role' }]
        });

        return done(null, updatedUser);
      }
    }

    // Create new user with Google account
    if (!email) {
      return done(new Error('No email provided by Google'), null);
    }

    // Check if this is an admin email
    const adminEmails = [
      process.env.ADMIN_EMAIL,
      process.env.ADMIN_EMAIL_SECONDARY
    ].filter(Boolean).map(email => email.toLowerCase());

    const isAdminEmail = adminEmails.includes(email.toLowerCase());

    // Get appropriate role based on email
    let userRole;
    if (isAdminEmail) {
      userRole = await Role.findOne({ where: { name: 'admin' } });
      if (!userRole) {
        // Create admin role if it doesn't exist
        userRole = await Role.create({
          name: 'admin',
          displayName: 'Administrator',
          description: 'System administrator with full access',
          permissions: JSON.stringify(['*']),
          priority: 100,
          isSystemRole: true
        });
      }
    } else {
      userRole = await Role.findOne({ where: { name: 'customer' } });
      if (!userRole) {
        return done(new Error('Customer role not found'), null);
      }
    }

    const newUser = await User.create({
      firstName: profile.name.givenName || (isAdminEmail ? 'Admin' : 'Google'),
      lastName: profile.name.familyName || (isAdminEmail ? 'User' : 'User'),
      email: email.toLowerCase(),
      googleId: profile.id,
      isVerified: true, // Google accounts are pre-verified
      roleId: userRole.id,
      profilePicture: profile.photos && profile.photos[0] ? profile.photos[0].value : null
    });

    // Fetch the user with role information
    const userWithRole = await User.findByPk(newUser.id, {
      include: [{ model: Role, as: 'Role' }]
    });

    return done(null, userWithRole);
  } catch (error) {
    console.error('Google OAuth error:', error);
    return done(error, null);
  }
  }));
} else {
  console.log('Google OAuth disabled - GOOGLE_CLIENT_ID and GOOGLE_CLIENT_SECRET not configured');
}

// Square OAuth Strategy
const getSquareCallbackURL = () => {
  // Use environment-specific callback URL
  if (process.env.SQUARE_OAUTH_CALLBACK_URL) {
    return process.env.SQUARE_OAUTH_CALLBACK_URL;
  }

  // Default based on environment
  const baseUrl = process.env.BACKEND_URL || 'http://localhost:5000';
  return `${baseUrl}/api/auth/square/callback`;
};

// Only configure Square OAuth if credentials are provided
if (process.env.SQUARE_OAUTH_CLIENT_ID && process.env.SQUARE_OAUTH_CLIENT_SECRET) {
  passport.use('square', new OAuth2Strategy({
    authorizationURL: process.env.SQUARE_ENVIRONMENT === 'production'
      ? 'https://connect.squareup.com/oauth2/authorize'
      : 'https://connect.squareupsandbox.com/oauth2/authorize',
    tokenURL: process.env.SQUARE_ENVIRONMENT === 'production'
      ? 'https://connect.squareup.com/oauth2/token'
      : 'https://connect.squareupsandbox.com/oauth2/token',
    clientID: process.env.SQUARE_OAUTH_CLIENT_ID,
    clientSecret: process.env.SQUARE_OAUTH_CLIENT_SECRET,
    callbackURL: getSquareCallbackURL()
  }, async (accessToken, refreshToken, profile, done) => {
  try {
    // Get merchant profile from Square API
    const axios = require('axios');
    const squareApiUrl = process.env.SQUARE_ENVIRONMENT === 'production'
      ? 'https://connect.squareup.com'
      : 'https://connect.squareupsandbox.com';

    const merchantResponse = await axios.get(`${squareApiUrl}/v2/merchants`, {
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Square-Version': '2023-10-18'
      }
    });

    const merchant = merchantResponse.data.merchant[0];
    if (!merchant) {
      return done(new Error('No merchant data found'), null);
    }

    const squareId = merchant.id;
    const email = merchant.business_name ? `${merchant.business_name.toLowerCase().replace(/\s+/g, '.')}@square.merchant` : null;
    const businessName = merchant.business_name || 'Square Merchant';

    // Check if user already exists with this Square ID
    let user = await User.findOne({
      where: { squareId: squareId },
      include: [{ model: Role, as: 'Role' }]
    });

    if (user) {
      return done(null, user);
    }

    // Check if user exists with the same email (if available)
    if (email) {
      user = await User.findOne({
        where: { email: email.toLowerCase() },
        include: [{ model: Role, as: 'Role' }]
      });

      if (user) {
        // Check if this should be an admin user
        const adminEmails = [
          process.env.ADMIN_EMAIL,
          process.env.ADMIN_EMAIL_SECONDARY
        ].filter(Boolean).map(email => email.toLowerCase());

        const isAdminEmail = adminEmails.includes(email.toLowerCase());

        // If this is an admin email but user doesn't have admin role, upgrade them
        if (isAdminEmail && user.Role && user.Role.name !== 'admin') {
          let adminRole = await Role.findOne({ where: { name: 'admin' } });
          if (!adminRole) {
            // Create admin role if it doesn't exist
            adminRole = await Role.create({
              name: 'admin',
              displayName: 'Administrator',
              description: 'System administrator with full access',
              permissions: JSON.stringify(['*']),
              priority: 100,
              isSystemRole: true
            });
          }
          user.roleId = adminRole.id;
        }

        // Link Square account to existing user
        user.squareId = squareId;
        user.isVerified = true; // Square accounts are pre-verified
        await user.save();

        // Reload user with updated role information
        const updatedUser = await User.findByPk(user.id, {
          include: [{ model: Role, as: 'Role' }]
        });

        return done(null, updatedUser);
      }
    }

    // Create new user with Square account
    if (!email) {
      // Generate a unique email for Square merchants without business email
      const uniqueEmail = `square.merchant.${squareId}@shopnirvanaorganics.com`;
      email = uniqueEmail;
    }

    // Check if this is an admin email
    const adminEmails = [
      process.env.ADMIN_EMAIL,
      process.env.ADMIN_EMAIL_SECONDARY
    ].filter(Boolean).map(email => email.toLowerCase());

    const isAdminEmail = adminEmails.includes(email.toLowerCase());

    // Get appropriate role
    let userRole;
    if (isAdminEmail) {
      userRole = await Role.findOne({ where: { name: 'admin' } });
      if (!userRole) {
        // Create admin role if it doesn't exist
        userRole = await Role.create({
          name: 'admin',
          displayName: 'Administrator',
          description: 'System administrator with full access',
          permissions: JSON.stringify(['*']),
          priority: 100,
          isSystemRole: true
        });
      }
    } else {
      userRole = await Role.findOne({ where: { name: 'customer' } });
      if (!userRole) {
        // Create customer role if it doesn't exist
        userRole = await Role.create({
          name: 'customer',
          displayName: 'Customer',
          description: 'Regular customer account',
          permissions: JSON.stringify(['read:own_profile', 'update:own_profile', 'create:orders']),
          priority: 1,
          isSystemRole: true
        });
      }
    }

    const newUser = await User.create({
      firstName: businessName.split(' ')[0] || (isAdminEmail ? 'Admin' : 'Square'),
      lastName: businessName.split(' ').slice(1).join(' ') || (isAdminEmail ? 'User' : 'Merchant'),
      email: email.toLowerCase(),
      squareId: squareId,
      isVerified: true, // Square accounts are pre-verified
      roleId: userRole.id,
      profilePicture: null // Square doesn't provide profile pictures
    });

    // Fetch the user with role information
    const userWithRole = await User.findByPk(newUser.id, {
      include: [{ model: Role, as: 'Role' }]
    });

    return done(null, userWithRole);
  } catch (error) {
    console.error('Square OAuth error:', error);
    return done(error, null);
  }
  }));
} else {
  console.log('Square OAuth disabled - SQUARE_OAUTH_CLIENT_ID and SQUARE_OAUTH_CLIENT_SECRET not configured');
}

module.exports = passport;
