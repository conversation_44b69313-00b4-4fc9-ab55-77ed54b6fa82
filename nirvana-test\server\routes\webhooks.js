const express = require('express');
const router = express.Router();
const SquareService = require('../services/squareService');
const webhookController = require('../controllers/webhookController');
const whatsappService = require('../services/whatsappService');
const { authenticate, requireAdmin } = require('../middleware/auth');

// Square webhook endpoint
router.post('/square', express.raw({ type: 'application/json' }), async (req, res) => {
  try {
    const signature = req.headers['x-square-signature'];
    const body = req.body;

    // Verify webhook signature
    if (!SquareService.verifyWebhookSignature(body, signature, process.env.SQUARE_WEBHOOK_SIGNATURE_KEY)) {
      return res.status(401).json({
        success: false,
        message: 'Invalid webhook signature'
      });
    }

    // Parse the webhook event
    const event = JSON.parse(body.toString());

    // Process the webhook event
    const result = await SquareService.processWebhookEvent(event);

    if (result.success) {
      res.status(200).json({
        success: true,
        message: result.message || 'Webhook processed successfully'
      });
    } else {
      res.status(500).json({
        success: false,
        message: result.error || 'Webhook processing failed'
      });
    }

  } catch (error) {
    console.error('Square webhook error:', error);
    res.status(500).json({
      success: false,
      message: 'Webhook processing failed',
      error: error.message
    });
  }
});

// @route   PUT /api/webhooks/payment-status
// @desc    Update payment status (for manual testing)
// @access  Private (Admin only)
router.put('/payment-status', authenticate, requireAdmin, webhookController.updatePaymentStatus);

// @route   GET /api/webhooks/payment-status/:orderId
// @desc    Get payment status for an order
// @access  Private (Admin only)
router.get('/payment-status/:orderId', authenticate, requireAdmin, webhookController.getPaymentStatus);

// @route   POST /api/webhooks/test
// @desc    Test webhook endpoint (for development)
// @access  Private (Admin only)
router.post('/test', authenticate, requireAdmin, webhookController.testWebhook);

// WhatsApp webhook endpoint
router.get('/whatsapp', (req, res) => {
  const mode = req.query['hub.mode'];
  const token = req.query['hub.verify_token'];
  const challenge = req.query['hub.challenge'];

  const verificationResult = whatsappService.verifyWebhook(mode, token, challenge);

  if (verificationResult) {
    res.status(200).send(verificationResult);
  } else {
    res.status(403).send('Forbidden');
  }
});

router.post('/whatsapp', express.json(), (req, res) => {
  try {
    const result = whatsappService.processWebhook(req.body);

    if (result.success) {
      res.status(200).send('OK');
    } else {
      res.status(400).json({ error: result.error });
    }
  } catch (error) {
    console.error('WhatsApp webhook error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

module.exports = router;
