import{r as ue,g as ce,a as fe}from"./vendor-DavUf6mE.js";function he(e,t){for(var r=0;r<t.length;r++){const n=t[r];if(typeof n!="string"&&!Array.isArray(n)){for(const a in n)if(a!=="default"&&!(a in e)){const l=Object.getOwnPropertyDescriptor(n,a);l&&Object.defineProperty(e,a,l.get?l:{enumerable:!0,get:()=>n[a]})}}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}))}var s=ue();const de=ce(s),pe=he({__proto__:null,default:de},[s]);fe();/**
 * @remix-run/router v1.23.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function N(){return N=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},N.apply(this,arguments)}var w;(function(e){e.Pop="POP",e.Push="PUSH",e.Replace="REPLACE"})(w||(w={}));const J="popstate";function me(e){e===void 0&&(e={});function t(n,a){let{pathname:l,search:i,hash:u}=n.location;return $("",{pathname:l,search:i,hash:u},a.state&&a.state.usr||null,a.state&&a.state.key||"default")}function r(n,a){return typeof a=="string"?a:I(a)}return ge(t,r,null,e)}function v(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}function Z(e,t){if(!e){typeof console<"u"&&console.warn(t);try{throw new Error(t)}catch{}}}function ve(){return Math.random().toString(36).substr(2,8)}function K(e,t){return{usr:e.state,key:e.key,idx:t}}function $(e,t,r,n){return r===void 0&&(r=null),N({pathname:typeof e=="string"?e:e.pathname,search:"",hash:""},typeof t=="string"?L(t):t,{state:r,key:t&&t.key||n||ve()})}function I(e){let{pathname:t="/",search:r="",hash:n=""}=e;return r&&r!=="?"&&(t+=r.charAt(0)==="?"?r:"?"+r),n&&n!=="#"&&(t+=n.charAt(0)==="#"?n:"#"+n),t}function L(e){let t={};if(e){let r=e.indexOf("#");r>=0&&(t.hash=e.substr(r),e=e.substr(0,r));let n=e.indexOf("?");n>=0&&(t.search=e.substr(n),e=e.substr(0,n)),e&&(t.pathname=e)}return t}function ge(e,t,r,n){n===void 0&&(n={});let{window:a=document.defaultView,v5Compat:l=!1}=n,i=a.history,u=w.Pop,o=null,f=h();f==null&&(f=0,i.replaceState(N({},i.state,{idx:f}),""));function h(){return(i.state||{idx:null}).idx}function c(){u=w.Pop;let d=h(),x=d==null?null:d-f;f=d,o&&o({action:u,location:m.location,delta:x})}function p(d,x){u=w.Push;let E=$(m.location,d,x);f=h()+1;let C=K(E,f),P=m.createHref(E);try{i.pushState(C,"",P)}catch(O){if(O instanceof DOMException&&O.name==="DataCloneError")throw O;a.location.assign(P)}l&&o&&o({action:u,location:m.location,delta:1})}function y(d,x){u=w.Replace;let E=$(m.location,d,x);f=h();let C=K(E,f),P=m.createHref(E);i.replaceState(C,"",P),l&&o&&o({action:u,location:m.location,delta:0})}function g(d){let x=a.location.origin!=="null"?a.location.origin:a.location.href,E=typeof d=="string"?d:I(d);return E=E.replace(/ $/,"%20"),v(x,"No window.location.(origin|href) available to create URL for href: "+E),new URL(E,x)}let m={get action(){return u},get location(){return e(a,i)},listen(d){if(o)throw new Error("A history only accepts one active listener");return a.addEventListener(J,c),o=d,()=>{a.removeEventListener(J,c),o=null}},createHref(d){return t(a,d)},createURL:g,encodeLocation(d){let x=g(d);return{pathname:x.pathname,search:x.search,hash:x.hash}},push:p,replace:y,go(d){return i.go(d)}};return m}var q;(function(e){e.data="data",e.deferred="deferred",e.redirect="redirect",e.error="error"})(q||(q={}));function ye(e,t,r){return r===void 0&&(r="/"),xe(e,t,r)}function xe(e,t,r,n){let a=typeof t=="string"?L(t):t,l=W(a.pathname||"/",r);if(l==null)return null;let i=ee(e);Ce(i);let u=null;for(let o=0;u==null&&o<i.length;++o){let f=je(l);u=Ue(i[o],f)}return u}function ee(e,t,r,n){t===void 0&&(t=[]),r===void 0&&(r=[]),n===void 0&&(n="");let a=(l,i,u)=>{let o={relativePath:u===void 0?l.path||"":u,caseSensitive:l.caseSensitive===!0,childrenIndex:i,route:l};o.relativePath.startsWith("/")&&(v(o.relativePath.startsWith(n),'Absolute route path "'+o.relativePath+'" nested under path '+('"'+n+'" is not valid. An absolute child route path ')+"must start with the combined path of all its parent routes."),o.relativePath=o.relativePath.slice(n.length));let f=b([n,o.relativePath]),h=r.concat(o);l.children&&l.children.length>0&&(v(l.index!==!0,"Index routes must not have child routes. Please remove "+('all child routes from route path "'+f+'".')),ee(l.children,t,h,f)),!(l.path==null&&!l.index)&&t.push({path:f,score:Oe(f,l.index),routesMeta:h})};return e.forEach((l,i)=>{var u;if(l.path===""||!((u=l.path)!=null&&u.includes("?")))a(l,i);else for(let o of te(l.path))a(l,i,o)}),t}function te(e){let t=e.split("/");if(t.length===0)return[];let[r,...n]=t,a=r.endsWith("?"),l=r.replace(/\?$/,"");if(n.length===0)return a?[l,""]:[l];let i=te(n.join("/")),u=[];return u.push(...i.map(o=>o===""?l:[l,o].join("/"))),a&&u.push(...i),u.map(o=>e.startsWith("/")&&o===""?"/":o)}function Ce(e){e.sort((t,r)=>t.score!==r.score?r.score-t.score:Le(t.routesMeta.map(n=>n.childrenIndex),r.routesMeta.map(n=>n.childrenIndex)))}const Ee=/^:[\w-]+$/,Pe=3,Re=2,we=1,be=10,Se=-2,G=e=>e==="*";function Oe(e,t){let r=e.split("/"),n=r.length;return r.some(G)&&(n+=Se),t&&(n+=Re),r.filter(a=>!G(a)).reduce((a,l)=>a+(Ee.test(l)?Pe:l===""?we:be),n)}function Le(e,t){return e.length===t.length&&e.slice(0,-1).every((n,a)=>n===t[a])?e[e.length-1]-t[t.length-1]:0}function Ue(e,t,r){let{routesMeta:n}=e,a={},l="/",i=[];for(let u=0;u<n.length;++u){let o=n[u],f=u===n.length-1,h=l==="/"?t:t.slice(l.length)||"/",c=Be({path:o.relativePath,caseSensitive:o.caseSensitive,end:f},h),p=o.route;if(!c)return null;Object.assign(a,c.params),i.push({params:a,pathname:b([l,c.pathname]),pathnameBase:$e(b([l,c.pathnameBase])),route:p}),c.pathnameBase!=="/"&&(l=b([l,c.pathnameBase]))}return i}function Be(e,t){typeof e=="string"&&(e={path:e,caseSensitive:!1,end:!0});let[r,n]=Ne(e.path,e.caseSensitive,e.end),a=t.match(r);if(!a)return null;let l=a[0],i=l.replace(/(.)\/+$/,"$1"),u=a.slice(1);return{params:n.reduce((f,h,c)=>{let{paramName:p,isOptional:y}=h;if(p==="*"){let m=u[c]||"";i=l.slice(0,l.length-m.length).replace(/(.)\/+$/,"$1")}const g=u[c];return y&&!g?f[p]=void 0:f[p]=(g||"").replace(/%2F/g,"/"),f},{}),pathname:l,pathnameBase:i,pattern:e}}function Ne(e,t,r){t===void 0&&(t=!1),r===void 0&&(r=!0),Z(e==="*"||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were '+('"'+e.replace(/\*$/,"/*")+'" because the `*` character must ')+"always follow a `/` in the pattern. To get rid of this warning, "+('please change the route path to "'+e.replace(/\*$/,"/*")+'".'));let n=[],a="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(i,u,o)=>(n.push({paramName:u,isOptional:o!=null}),o?"/?([^\\/]+)?":"/([^\\/]+)"));return e.endsWith("*")?(n.push({paramName:"*"}),a+=e==="*"||e==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):r?a+="\\/*$":e!==""&&e!=="/"&&(a+="(?:(?=\\/|$))"),[new RegExp(a,t?void 0:"i"),n]}function je(e){try{return e.split("/").map(t=>decodeURIComponent(t).replace(/\//g,"%2F")).join("/")}catch(t){return Z(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent '+("encoding ("+t+").")),e}}function W(e,t){if(t==="/")return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let r=t.endsWith("/")?t.length-1:t.length,n=e.charAt(r);return n&&n!=="/"?null:e.slice(r)||"/"}function Ie(e,t){t===void 0&&(t="/");let{pathname:r,search:n="",hash:a=""}=typeof e=="string"?L(e):e;return{pathname:r?r.startsWith("/")?r:Te(r,t):t,search:ke(n),hash:Fe(a)}}function Te(e,t){let r=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(a=>{a===".."?r.length>1&&r.pop():a!=="."&&r.push(a)}),r.length>1?r.join("/"):"/"}function _(e,t,r,n){return"Cannot include a '"+e+"' character in a manually specified "+("`to."+t+"` field ["+JSON.stringify(n)+"].  Please separate it out to the ")+("`to."+r+"` field. Alternatively you may provide the full path as ")+'a string in <Link to="..."> and the router will parse it for you.'}function _e(e){return e.filter((t,r)=>r===0||t.route.path&&t.route.path.length>0)}function A(e,t){let r=_e(e);return t?r.map((n,a)=>a===r.length-1?n.pathname:n.pathnameBase):r.map(n=>n.pathnameBase)}function D(e,t,r,n){n===void 0&&(n=!1);let a;typeof e=="string"?a=L(e):(a=N({},e),v(!a.pathname||!a.pathname.includes("?"),_("?","pathname","search",a)),v(!a.pathname||!a.pathname.includes("#"),_("#","pathname","hash",a)),v(!a.search||!a.search.includes("#"),_("#","search","hash",a)));let l=e===""||a.pathname==="",i=l?"/":a.pathname,u;if(i==null)u=r;else{let c=t.length-1;if(!n&&i.startsWith("..")){let p=i.split("/");for(;p[0]==="..";)p.shift(),c-=1;a.pathname=p.join("/")}u=c>=0?t[c]:"/"}let o=Ie(a,u),f=i&&i!=="/"&&i.endsWith("/"),h=(l||i===".")&&r.endsWith("/");return!o.pathname.endsWith("/")&&(f||h)&&(o.pathname+="/"),o}const b=e=>e.join("/").replace(/\/\/+/g,"/"),$e=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),ke=e=>!e||e==="?"?"":e.startsWith("?")?e:"?"+e,Fe=e=>!e||e==="#"?"":e.startsWith("#")?e:"#"+e;function Me(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.internal=="boolean"&&"data"in e}const re=["post","put","patch","delete"];new Set(re);const We=["get",...re];new Set(We);/**
 * React Router v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function j(){return j=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},j.apply(this,arguments)}const V=s.createContext(null),Ae=s.createContext(null),S=s.createContext(null),T=s.createContext(null),R=s.createContext({outlet:null,matches:[],isDataRoute:!1}),ne=s.createContext(null);function De(e,t){let{relative:r}=t===void 0?{}:t;U()||v(!1);let{basename:n,navigator:a}=s.useContext(S),{hash:l,pathname:i,search:u}=le(e,{relative:r}),o=i;return n!=="/"&&(o=i==="/"?n:b([n,i])),a.createHref({pathname:o,search:u,hash:l})}function U(){return s.useContext(T)!=null}function B(){return U()||v(!1),s.useContext(T).location}function ae(e){s.useContext(S).static||s.useLayoutEffect(e)}function z(){let{isDataRoute:e}=s.useContext(R);return e?tt():Ve()}function Ve(){U()||v(!1);let e=s.useContext(V),{basename:t,future:r,navigator:n}=s.useContext(S),{matches:a}=s.useContext(R),{pathname:l}=B(),i=JSON.stringify(A(a,r.v7_relativeSplatPath)),u=s.useRef(!1);return ae(()=>{u.current=!0}),s.useCallback(function(f,h){if(h===void 0&&(h={}),!u.current)return;if(typeof f=="number"){n.go(f);return}let c=D(f,JSON.parse(i),l,h.relative==="path");e==null&&t!=="/"&&(c.pathname=c.pathname==="/"?t:b([t,c.pathname])),(h.replace?n.replace:n.push)(c,h.state,h)},[t,n,i,l,e])}function gt(){let{matches:e}=s.useContext(R),t=e[e.length-1];return t?t.params:{}}function le(e,t){let{relative:r}=t===void 0?{}:t,{future:n}=s.useContext(S),{matches:a}=s.useContext(R),{pathname:l}=B(),i=JSON.stringify(A(a,n.v7_relativeSplatPath));return s.useMemo(()=>D(e,JSON.parse(i),l,r==="path"),[e,i,l,r])}function ze(e,t){return Je(e,t)}function Je(e,t,r,n){U()||v(!1);let{navigator:a}=s.useContext(S),{matches:l}=s.useContext(R),i=l[l.length-1],u=i?i.params:{};i&&i.pathname;let o=i?i.pathnameBase:"/";i&&i.route;let f=B(),h;if(t){var c;let d=typeof t=="string"?L(t):t;o==="/"||(c=d.pathname)!=null&&c.startsWith(o)||v(!1),h=d}else h=f;let p=h.pathname||"/",y=p;if(o!=="/"){let d=o.replace(/^\//,"").split("/");y="/"+p.replace(/^\//,"").split("/").slice(d.length).join("/")}let g=ye(e,{pathname:y}),m=He(g&&g.map(d=>Object.assign({},d,{params:Object.assign({},u,d.params),pathname:b([o,a.encodeLocation?a.encodeLocation(d.pathname).pathname:d.pathname]),pathnameBase:d.pathnameBase==="/"?o:b([o,a.encodeLocation?a.encodeLocation(d.pathnameBase).pathname:d.pathnameBase])})),l,r,n);return t&&m?s.createElement(T.Provider,{value:{location:j({pathname:"/",search:"",hash:"",state:null,key:"default"},h),navigationType:w.Pop}},m):m}function Ke(){let e=et(),t=Me(e)?e.status+" "+e.statusText:e instanceof Error?e.message:JSON.stringify(e),r=e instanceof Error?e.stack:null,a={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"};return s.createElement(s.Fragment,null,s.createElement("h2",null,"Unexpected Application Error!"),s.createElement("h3",{style:{fontStyle:"italic"}},t),r?s.createElement("pre",{style:a},r):null,null)}const qe=s.createElement(Ke,null);class Ge extends s.Component{constructor(t){super(t),this.state={location:t.location,revalidation:t.revalidation,error:t.error}}static getDerivedStateFromError(t){return{error:t}}static getDerivedStateFromProps(t,r){return r.location!==t.location||r.revalidation!=="idle"&&t.revalidation==="idle"?{error:t.error,location:t.location,revalidation:t.revalidation}:{error:t.error!==void 0?t.error:r.error,location:r.location,revalidation:t.revalidation||r.revalidation}}componentDidCatch(t,r){console.error("React Router caught the following error during render",t,r)}render(){return this.state.error!==void 0?s.createElement(R.Provider,{value:this.props.routeContext},s.createElement(ne.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function Xe(e){let{routeContext:t,match:r,children:n}=e,a=s.useContext(V);return a&&a.static&&a.staticContext&&(r.route.errorElement||r.route.ErrorBoundary)&&(a.staticContext._deepestRenderedBoundaryId=r.route.id),s.createElement(R.Provider,{value:t},n)}function He(e,t,r,n){var a;if(t===void 0&&(t=[]),r===void 0&&(r=null),n===void 0&&(n=null),e==null){var l;if(!r)return null;if(r.errors)e=r.matches;else if((l=n)!=null&&l.v7_partialHydration&&t.length===0&&!r.initialized&&r.matches.length>0)e=r.matches;else return null}let i=e,u=(a=r)==null?void 0:a.errors;if(u!=null){let h=i.findIndex(c=>c.route.id&&u?.[c.route.id]!==void 0);h>=0||v(!1),i=i.slice(0,Math.min(i.length,h+1))}let o=!1,f=-1;if(r&&n&&n.v7_partialHydration)for(let h=0;h<i.length;h++){let c=i[h];if((c.route.HydrateFallback||c.route.hydrateFallbackElement)&&(f=h),c.route.id){let{loaderData:p,errors:y}=r,g=c.route.loader&&p[c.route.id]===void 0&&(!y||y[c.route.id]===void 0);if(c.route.lazy||g){o=!0,f>=0?i=i.slice(0,f+1):i=[i[0]];break}}}return i.reduceRight((h,c,p)=>{let y,g=!1,m=null,d=null;r&&(y=u&&c.route.id?u[c.route.id]:void 0,m=c.route.errorElement||qe,o&&(f<0&&p===0?(rt("route-fallback"),g=!0,d=null):f===p&&(g=!0,d=c.route.hydrateFallbackElement||null)));let x=t.concat(i.slice(0,p+1)),E=()=>{let C;return y?C=m:g?C=d:c.route.Component?C=s.createElement(c.route.Component,null):c.route.element?C=c.route.element:C=h,s.createElement(Xe,{match:c,routeContext:{outlet:h,matches:x,isDataRoute:r!=null},children:C})};return r&&(c.route.ErrorBoundary||c.route.errorElement||p===0)?s.createElement(Ge,{location:r.location,revalidation:r.revalidation,component:m,error:y,children:E(),routeContext:{outlet:null,matches:x,isDataRoute:!0}}):E()},null)}var ie=function(e){return e.UseBlocker="useBlocker",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e}(ie||{}),oe=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}(oe||{});function Qe(e){let t=s.useContext(V);return t||v(!1),t}function Ye(e){let t=s.useContext(Ae);return t||v(!1),t}function Ze(e){let t=s.useContext(R);return t||v(!1),t}function se(e){let t=Ze(),r=t.matches[t.matches.length-1];return r.route.id||v(!1),r.route.id}function et(){var e;let t=s.useContext(ne),r=Ye(),n=se();return t!==void 0?t:(e=r.errors)==null?void 0:e[n]}function tt(){let{router:e}=Qe(ie.UseNavigateStable),t=se(oe.UseNavigateStable),r=s.useRef(!1);return ae(()=>{r.current=!0}),s.useCallback(function(a,l){l===void 0&&(l={}),r.current&&(typeof a=="number"?e.navigate(a):e.navigate(a,j({fromRouteId:t},l)))},[e,t])}const X={};function rt(e,t,r){X[e]||(X[e]=!0)}function nt(e,t){e?.v7_startTransition,e?.v7_relativeSplatPath}function yt(e){let{to:t,replace:r,state:n,relative:a}=e;U()||v(!1);let{future:l,static:i}=s.useContext(S),{matches:u}=s.useContext(R),{pathname:o}=B(),f=z(),h=D(t,A(u,l.v7_relativeSplatPath),o,a==="path"),c=JSON.stringify(h);return s.useEffect(()=>f(JSON.parse(c),{replace:r,state:n,relative:a}),[f,c,a,r,n]),null}function at(e){v(!1)}function lt(e){let{basename:t="/",children:r=null,location:n,navigationType:a=w.Pop,navigator:l,static:i=!1,future:u}=e;U()&&v(!1);let o=t.replace(/^\/*/,"/"),f=s.useMemo(()=>({basename:o,navigator:l,static:i,future:j({v7_relativeSplatPath:!1},u)}),[o,u,l,i]);typeof n=="string"&&(n=L(n));let{pathname:h="/",search:c="",hash:p="",state:y=null,key:g="default"}=n,m=s.useMemo(()=>{let d=W(h,o);return d==null?null:{location:{pathname:d,search:c,hash:p,state:y,key:g},navigationType:a}},[o,h,c,p,y,g,a]);return m==null?null:s.createElement(S.Provider,{value:f},s.createElement(T.Provider,{children:r,value:m}))}function xt(e){let{children:t,location:r}=e;return ze(k(t),r)}new Promise(()=>{});function k(e,t){t===void 0&&(t=[]);let r=[];return s.Children.forEach(e,(n,a)=>{if(!s.isValidElement(n))return;let l=[...t,a];if(n.type===s.Fragment){r.push.apply(r,k(n.props.children,l));return}n.type!==at&&v(!1),!n.props.index||!n.props.children||v(!1);let i={id:n.props.id||l.join("-"),caseSensitive:n.props.caseSensitive,element:n.props.element,Component:n.props.Component,index:n.props.index,path:n.props.path,loader:n.props.loader,action:n.props.action,errorElement:n.props.errorElement,ErrorBoundary:n.props.ErrorBoundary,hasErrorBoundary:n.props.ErrorBoundary!=null||n.props.errorElement!=null,shouldRevalidate:n.props.shouldRevalidate,handle:n.props.handle,lazy:n.props.lazy};n.props.children&&(i.children=k(n.props.children,l)),r.push(i)}),r}/**
 * React Router DOM v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function F(){return F=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},F.apply(this,arguments)}function it(e,t){if(e==null)return{};var r={},n=Object.keys(e),a,l;for(l=0;l<n.length;l++)a=n[l],!(t.indexOf(a)>=0)&&(r[a]=e[a]);return r}function ot(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}function st(e,t){return e.button===0&&(!t||t==="_self")&&!ot(e)}function M(e){return e===void 0&&(e=""),new URLSearchParams(typeof e=="string"||Array.isArray(e)||e instanceof URLSearchParams?e:Object.keys(e).reduce((t,r)=>{let n=e[r];return t.concat(Array.isArray(n)?n.map(a=>[r,a]):[[r,n]])},[]))}function ut(e,t){let r=M(e);return t&&t.forEach((n,a)=>{r.has(a)||t.getAll(a).forEach(l=>{r.append(a,l)})}),r}const ct=["onClick","relative","reloadDocument","replace","state","target","to","preventScrollReset","viewTransition"],ft="6";try{window.__reactRouterVersion=ft}catch{}const ht="startTransition",H=pe[ht];function Ct(e){let{basename:t,children:r,future:n,window:a}=e,l=s.useRef();l.current==null&&(l.current=me({window:a,v5Compat:!0}));let i=l.current,[u,o]=s.useState({action:i.action,location:i.location}),{v7_startTransition:f}=n||{},h=s.useCallback(c=>{f&&H?H(()=>o(c)):o(c)},[o,f]);return s.useLayoutEffect(()=>i.listen(h),[i,h]),s.useEffect(()=>nt(n),[n]),s.createElement(lt,{basename:t,children:r,location:u.location,navigationType:u.action,navigator:i,future:n})}const dt=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u",pt=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,Et=s.forwardRef(function(t,r){let{onClick:n,relative:a,reloadDocument:l,replace:i,state:u,target:o,to:f,preventScrollReset:h,viewTransition:c}=t,p=it(t,ct),{basename:y}=s.useContext(S),g,m=!1;if(typeof f=="string"&&pt.test(f)&&(g=f,dt))try{let C=new URL(window.location.href),P=f.startsWith("//")?new URL(C.protocol+f):new URL(f),O=W(P.pathname,y);P.origin===C.origin&&O!=null?f=O+P.search+P.hash:m=!0}catch{}let d=De(f,{relative:a}),x=mt(f,{replace:i,state:u,target:o,preventScrollReset:h,relative:a,viewTransition:c});function E(C){n&&n(C),C.defaultPrevented||x(C)}return s.createElement("a",F({},p,{href:g||d,onClick:m||l?n:E,ref:r,target:o}))});var Q;(function(e){e.UseScrollRestoration="useScrollRestoration",e.UseSubmit="useSubmit",e.UseSubmitFetcher="useSubmitFetcher",e.UseFetcher="useFetcher",e.useViewTransitionState="useViewTransitionState"})(Q||(Q={}));var Y;(function(e){e.UseFetcher="useFetcher",e.UseFetchers="useFetchers",e.UseScrollRestoration="useScrollRestoration"})(Y||(Y={}));function mt(e,t){let{target:r,replace:n,state:a,preventScrollReset:l,relative:i,viewTransition:u}=t===void 0?{}:t,o=z(),f=B(),h=le(e,{relative:i});return s.useCallback(c=>{if(st(c,r)){c.preventDefault();let p=n!==void 0?n:I(f)===I(h);o(e,{replace:p,state:a,preventScrollReset:l,relative:i,viewTransition:u})}},[f,o,h,n,a,r,e,l,i,u])}function Pt(e){let t=s.useRef(M(e)),r=s.useRef(!1),n=B(),a=s.useMemo(()=>ut(n.search,r.current?null:t.current),[n.search]),l=z(),i=s.useCallback((u,o)=>{const f=M(typeof u=="function"?u(a):u);r.current=!0,l("?"+f,o)},[l,a]);return[a,i]}export{Ct as B,Et as L,yt as N,de as R,B as a,Pt as b,gt as c,xt as d,at as e,s as r,z as u};
