#!/bin/bash

# Nirvana Organics - Production Deployment Script
# Deploy to shopnirvanaorganics.com

set -e  # Exit on any error

# Configuration
DEPLOY_USER="deploy"
DEPLOY_PATH="/var/www/nirvana-backend"
FRONTEND_PATH="/var/www/nirvana-frontend"
BACKUP_PATH="/var/backups/nirvana-organics"
LOG_FILE="/var/log/deploy/production-$(date +%Y%m%d-%H%M%S).log"
DOMAIN="shopnirvanaorganics.com"
ADMIN_DOMAIN="admin.shopnirvanaorganics.com"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$LOG_FILE"
    exit 1
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a "$LOG_FILE"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$LOG_FILE"
}

# Create log directory
sudo mkdir -p /var/log/deploy

log "🚀 Starting Nirvana Organics Production Deployment"
log "📅 Deployment Date: $(date)"
log "🌐 Target Domain: $DOMAIN"
log "🔧 Admin Domain: $ADMIN_DOMAIN"

# Verify we're in the production deployment folder
if [ ! -f ".env.production" ] || [ ! -f ".env.admin" ]; then
    error "❌ Missing production environment files. Are you in the nirvana-production folder?"
fi

# Create backup of existing deployment
if [ -d "$DEPLOY_PATH" ]; then
    log "📦 Creating backup of existing deployment..."
    sudo mkdir -p "$BACKUP_PATH"
    sudo cp -r "$DEPLOY_PATH" "$BACKUP_PATH/backup-$(date +%Y%m%d-%H%M%S)"
    success "✅ Backup created"
fi

# Create deployment directories
log "📁 Creating deployment directories..."
sudo mkdir -p "$DEPLOY_PATH"
sudo mkdir -p "$FRONTEND_PATH/main"
sudo mkdir -p "$FRONTEND_PATH/admin"

# Deploy backend files
log "🔧 Deploying backend files..."
sudo cp -r server/ "$DEPLOY_PATH/"
sudo cp package.json "$DEPLOY_PATH/"
sudo cp package-lock.json "$DEPLOY_PATH/"
sudo cp ecosystem.config.production.js "$DEPLOY_PATH/"
sudo cp .env.production "$DEPLOY_PATH/"
sudo cp .env.admin "$DEPLOY_PATH/"
success "✅ Backend files deployed"

# Deploy frontend files
log "🎨 Deploying frontend files..."
if [ -d "dist" ]; then
    sudo cp -r dist/* "$FRONTEND_PATH/main/"
    success "✅ Main frontend deployed"
else
    warning "⚠️ Main frontend dist directory not found"
fi

if [ -d "dist-admin" ]; then
    sudo cp -r dist-admin/* "$FRONTEND_PATH/admin/"
    success "✅ Admin frontend deployed"
else
    warning "⚠️ Admin frontend dist-admin directory not found"
fi

# Set proper permissions
log "🔐 Setting file permissions..."
sudo chown -R $DEPLOY_USER:$DEPLOY_USER "$DEPLOY_PATH"
sudo chown -R www-data:www-data "$FRONTEND_PATH"
sudo chmod -R 755 "$DEPLOY_PATH"
sudo chmod -R 755 "$FRONTEND_PATH"
success "✅ Permissions set"

# Install dependencies
log "📦 Installing Node.js dependencies..."
cd "$DEPLOY_PATH"
sudo -u $DEPLOY_USER npm ci --production
success "✅ Dependencies installed"

# Start/restart PM2 processes
log "🔄 Starting PM2 processes..."
sudo -u $DEPLOY_USER pm2 delete all || true
sudo -u $DEPLOY_USER pm2 start ecosystem.config.production.js --env production
success "✅ PM2 processes started"

# Verify deployment
log "🔍 Verifying deployment..."
sleep 5

# Check if processes are running
if sudo -u $DEPLOY_USER pm2 list | grep -q "online"; then
    success "✅ Backend services are running"
else
    error "❌ Backend services failed to start"
fi

# Test health endpoints
if curl -f -s "https://$DOMAIN/api/health" > /dev/null; then
    success "✅ Main server health check passed"
else
    warning "⚠️ Main server health check failed"
fi

if curl -f -s "https://$ADMIN_DOMAIN/api/health" > /dev/null; then
    success "✅ Admin server health check passed"
else
    warning "⚠️ Admin server health check failed"
fi

log "🎉 Production deployment completed successfully!"
log "🌐 Main site: https://$DOMAIN"
log "🔧 Admin panel: https://$ADMIN_DOMAIN"
log "📊 PM2 status: sudo -u $DEPLOY_USER pm2 status"
log "📋 Logs: sudo -u $DEPLOY_USER pm2 logs"

success "✅ Deployment completed at $(date)"
