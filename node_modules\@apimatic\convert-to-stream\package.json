{"name": "@apimatic/convert-to-stream", "version": "0.1.6", "description": "converts the content to stream", "main": "lib/index.js", "module": "lib/index.js", "types": "lib/index.d.ts", "engines": {"node": ">=14.15.0 || >=16.0.0"}, "scripts": {"clean": "rimraf lib es umd tsconfig.tsbuildinfo", "test": "jest --passWithNoTests", "build": "npm run clean && tsc && rollup -c --bundleConfigAsCjs && npm run annotate:es", "annotate:es": "babel es --out-dir es --no-babelrc --plugins annotate-pure-calls", "preversion": "npm run test", "prepublishOnly": "npm run build", "size": "size-limit", "analyze": "size-limit --why", "lint": "tslint --project .", "lint:fix": "tslint --project . --fix", "check-style": "prettier --check \"{src,test}/**/*.ts\"", "check-style:fix": "prettier --write \"{src,test}/**/*.ts\""}, "author": "APIMatic Ltd.", "license": "ISC", "devDependencies": {"@babel/cli": "^7.26.4", "@babel/core": "^7.26.10", "@types/jest": "^27.4.1", "@types/node": "^17.0.29", "lerna-alias": "3.0.3-0", "rollup": "^3.29.5", "rollup-plugin-typescript2": "^0.34.0", "typescript": "^4.6.3"}, "browser": {"./lib/convertToStream.js": "./lib/convertToBlob.js", "./lib/convertFromStream.js": "./lib/convertFromBlob.js"}, "dependencies": {"tslib": "2.8.1"}, "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "**************:apimatic/apimatic-js-runtime.git", "directory": "packages/convert-to-stream"}, "gitHead": "25d6a0e48d0a7860b36c72b959577aaf49bfc02a"}