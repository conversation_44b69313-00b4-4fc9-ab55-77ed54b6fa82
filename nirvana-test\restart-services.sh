#!/bin/bash
# Make script executable: chmod +x restart-services.sh

# Nirvana Organics Test Environment - Service Restart Script
# Ensures proper deployment of updated server configurations

set -e

echo "🔄 Nirvana Organics Test Environment - Service Restart"
echo "====================================================="

# Color codes
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}✅ $1${NC}"
}

warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check user
if [ "$USER" != "Nirvana" ]; then
    error "This script must be run as the 'Nirvana' user"
    echo "Please run: sudo -u Nirvana $0"
    exit 1
fi

# Change to deployment directory
cd /var/www/nirvana-backend-test

log "Current directory: $(pwd)"

# Step 1: Stop existing processes
log "Step 1: Stopping existing PM2 processes..."
pm2 stop nirvana-backend-main-test nirvana-backend-admin-test 2>/dev/null || warning "Some processes may not have been running"
pm2 delete nirvana-backend-main-test nirvana-backend-admin-test 2>/dev/null || warning "Some processes may not have existed"
success "Existing processes stopped"

# Step 2: Verify server files are updated
log "Step 2: Verifying server files..."

if [ ! -f "server/admin-server.js" ]; then
    error "Missing server/admin-server.js"
    exit 1
fi

if [ ! -f "server/index.js" ]; then
    error "Missing server/index.js"
    exit 1
fi

# Check if files contain the updated environment-aware paths
if grep -q "process.env.NODE_ENV === 'test'" server/admin-server.js; then
    success "Admin server has updated environment-aware paths"
else
    error "Admin server does not have updated paths - deployment may have failed"
    exit 1
fi

if grep -q "process.env.NODE_ENV === 'test'" server/index.js; then
    success "Main server has updated environment-aware paths"
else
    error "Main server does not have updated paths - deployment may have failed"
    exit 1
fi

# Step 3: Verify environment files
log "Step 3: Verifying environment configuration..."

if [ ! -f ".env.test" ]; then
    error "Missing .env.test file"
    exit 1
fi

if [ ! -f ".env.admin.test" ]; then
    error "Missing .env.admin.test file"
    exit 1
fi

# Check NODE_ENV values
if grep -q "NODE_ENV=test" .env.test; then
    success ".env.test has correct NODE_ENV"
else
    warning ".env.test NODE_ENV may not be set correctly"
fi

if grep -q "NODE_ENV=test" .env.admin.test; then
    success ".env.admin.test has correct NODE_ENV"
else
    warning ".env.admin.test NODE_ENV may not be set correctly"
fi

# Step 4: Verify ecosystem configuration
log "Step 4: Verifying PM2 ecosystem configuration..."

if [ ! -f "ecosystem.config.js" ]; then
    error "Missing ecosystem.config.js file"
    exit 1
fi

if grep -q "NODE_ENV: 'test'" ecosystem.config.js; then
    success "PM2 ecosystem has correct NODE_ENV"
else
    warning "PM2 ecosystem NODE_ENV may not be set correctly"
fi

# Step 5: Start services
log "Step 5: Starting PM2 services..."

pm2 start ecosystem.config.js

# Wait for services to start
sleep 3

# Step 6: Verify services are running
log "Step 6: Verifying service status..."

if pm2 list | grep -q "nirvana-backend-main-test.*online"; then
    success "Main server is online"
else
    error "Main server failed to start"
    pm2 logs nirvana-backend-main-test --lines 10
    exit 1
fi

if pm2 list | grep -q "nirvana-backend-admin-test.*online"; then
    success "Admin server is online"
else
    error "Admin server failed to start"
    pm2 logs nirvana-backend-admin-test --lines 10
    exit 1
fi

# Step 7: Save PM2 configuration
log "Step 7: Saving PM2 configuration..."
pm2 save
success "PM2 configuration saved"

# Step 8: Display status
log "Step 8: Service restart completed!"

echo ""
echo "📊 CURRENT STATUS"
echo "================="
pm2 status

echo ""
echo "🔍 VERIFICATION COMMANDS"
echo "======================="
echo "View logs:           pm2 logs"
echo "Main server logs:    pm2 logs nirvana-backend-main-test"
echo "Admin server logs:   pm2 logs nirvana-backend-admin-test"
echo "Test health:         curl https://test.shopnirvanaorganics.com/api/health"
echo "Test admin health:   curl https://test.shopnirvanaorganics.com/admin/api/health"

echo ""
success "🎉 Service restart completed successfully!"

# Optional: Show recent logs
echo ""
log "Recent logs (last 5 lines from each service):"
echo "Main Server:"
pm2 logs nirvana-backend-main-test --lines 5 --nostream
echo ""
echo "Admin Server:"
pm2 logs nirvana-backend-admin-test --lines 5 --nostream
