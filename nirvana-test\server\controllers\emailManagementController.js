const { models } = require('../models');
const { validationResult } = require('express-validator');
const emailNotificationService = require('../services/emailNotificationService');
const { Op } = require('sequelize');

/**
 * Email Management Controller
 * Handles admin email management, templates, logs, and settings
 */
class EmailManagementController {
  /**
   * Get all email templates
   * @route GET /api/admin/email-management/templates
   * @access Private (Admin)
   */
  static async getEmailTemplates(req, res) {
    try {
      const { category = '', isActive = '' } = req.query;
      const whereClause = {};

      if (category) {
        whereClause.category = category;
      }

      if (isActive !== '') {
        whereClause.isActive = isActive === 'true';
      }

      const templates = await models.EmailTemplate.findAll({
        where: whereClause,
        include: [
          {
            model: models.User,
            as: 'creator',
            attributes: ['firstName', 'lastName']
          }
        ],
        order: [['createdAt', 'DESC']]
      });

      res.json({
        success: true,
        data: templates
      });

    } catch (error) {
      console.error('Get email templates error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch email templates',
        error: error.message
      });
    }
  }

  /**
   * Create email template
   * @route POST /api/admin/email-management/templates
   * @access Private (Admin)
   */
  static async createEmailTemplate(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      const template = await models.EmailTemplate.create({
        ...req.body,
        createdBy: req.user.id
      });

      res.status(201).json({
        success: true,
        data: template,
        message: 'Email template created successfully'
      });

    } catch (error) {
      console.error('Create email template error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to create email template',
        error: error.message
      });
    }
  }

  /**
   * Update email template
   * @route PUT /api/admin/email-management/templates/:id
   * @access Private (Admin)
   */
  static async updateEmailTemplate(req, res) {
    try {
      const { id } = req.params;
      const errors = validationResult(req);
      
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      const template = await models.EmailTemplate.findByPk(id);
      if (!template) {
        return res.status(404).json({
          success: false,
          message: 'Email template not found'
        });
      }

      await template.update(req.body);

      res.json({
        success: true,
        data: template,
        message: 'Email template updated successfully'
      });

    } catch (error) {
      console.error('Update email template error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to update email template',
        error: error.message
      });
    }
  }

  /**
   * Delete email template
   * @route DELETE /api/admin/email-management/templates/:id
   * @access Private (Admin)
   */
  static async deleteEmailTemplate(req, res) {
    try {
      const { id } = req.params;

      const template = await models.EmailTemplate.findByPk(id);
      if (!template) {
        return res.status(404).json({
          success: false,
          message: 'Email template not found'
        });
      }

      await template.destroy();

      res.json({
        success: true,
        message: 'Email template deleted successfully'
      });

    } catch (error) {
      console.error('Delete email template error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to delete email template',
        error: error.message
      });
    }
  }

  /**
   * Get email delivery logs
   * @route GET /api/admin/email-management/logs
   * @access Private (Admin)
   */
  static async getEmailLogs(req, res) {
    try {
      const {
        page = 1,
        limit = 50,
        emailType = '',
        status = '',
        startDate = '',
        endDate = ''
      } = req.query;

      const offset = (page - 1) * limit;
      const whereClause = {};

      if (emailType) {
        whereClause.emailType = emailType;
      }

      if (status) {
        whereClause.status = status;
      }

      if (startDate || endDate) {
        whereClause.createdAt = {};
        if (startDate) {
          whereClause.createdAt[Op.gte] = new Date(startDate);
        }
        if (endDate) {
          whereClause.createdAt[Op.lte] = new Date(endDate);
        }
      }

      const { count, rows: logs } = await models.EmailLog.findAndCountAll({
        where: whereClause,
        order: [['createdAt', 'DESC']],
        limit: parseInt(limit),
        offset: parseInt(offset)
      });

      res.json({
        success: true,
        data: {
          logs,
          pagination: {
            currentPage: parseInt(page),
            totalPages: Math.ceil(count / limit),
            totalLogs: count,
            limit: parseInt(limit)
          }
        }
      });

    } catch (error) {
      console.error('Get email logs error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch email logs',
        error: error.message
      });
    }
  }

  /**
   * Get email statistics
   * @route GET /api/admin/email-management/stats
   * @access Private (Admin)
   */
  static async getEmailStats(req, res) {
    try {
      const { startDate = '', endDate = '' } = req.query;
      
      const dateRange = {};
      if (startDate) dateRange.startDate = startDate;
      if (endDate) dateRange.endDate = endDate;

      const stats = await emailNotificationService.getEmailStats(dateRange);

      // Get additional statistics
      const totalEmails = stats.sent + stats.failed + stats.pending;
      const deliveryRate = totalEmails > 0 ? ((stats.sent / totalEmails) * 100).toFixed(2) : 0;

      // Get email type breakdown
      const whereClause = {};
      if (startDate || endDate) {
        whereClause.createdAt = {};
        if (startDate) whereClause.createdAt[Op.gte] = new Date(startDate);
        if (endDate) whereClause.createdAt[Op.lte] = new Date(endDate);
      }

      const emailTypeStats = await models.EmailLog.findAll({
        attributes: [
          'emailType',
          [models.sequelize.fn('COUNT', models.sequelize.col('id')), 'count'],
          [models.sequelize.fn('SUM', models.sequelize.literal('CASE WHEN status = \'sent\' THEN 1 ELSE 0 END')), 'sent'],
          [models.sequelize.fn('SUM', models.sequelize.literal('CASE WHEN status = \'failed\' THEN 1 ELSE 0 END')), 'failed']
        ],
        where: whereClause,
        group: ['emailType'],
        raw: true
      });

      res.json({
        success: true,
        data: {
          overview: {
            ...stats,
            total: totalEmails,
            deliveryRate: parseFloat(deliveryRate)
          },
          byType: emailTypeStats.map(stat => ({
            emailType: stat.emailType,
            total: parseInt(stat.count),
            sent: parseInt(stat.sent),
            failed: parseInt(stat.failed),
            deliveryRate: stat.count > 0 ? ((stat.sent / stat.count) * 100).toFixed(2) : 0
          }))
        }
      });

    } catch (error) {
      console.error('Get email stats error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch email statistics',
        error: error.message
      });
    }
  }

  /**
   * Get admin email settings
   * @route GET /api/admin/email-management/settings
   * @access Private (Admin)
   */
  static async getAdminEmailSettings(req, res) {
    try {
      // Get admin notification emails setting
      const adminEmailsSetting = await models.SystemSetting.findOne({
        where: { key: 'admin_notification_emails' }
      });

      // Get other email settings
      const emailFromNameSetting = await models.SystemSetting.findOne({
        where: { key: 'email_from_name' }
      });

      const emailFromAddressSetting = await models.SystemSetting.findOne({
        where: { key: 'email_from_address' }
      });

      const orderNotificationsSetting = await models.SystemSetting.findOne({
        where: { key: 'enable_order_notifications' }
      });

      const statusUpdatesSetting = await models.SystemSetting.findOne({
        where: { key: 'enable_status_updates' }
      });

      const marketingEmailsSetting = await models.SystemSetting.findOne({
        where: { key: 'enable_marketing_emails' }
      });

      const settings = {
        adminNotificationEmails: adminEmailsSetting ? JSON.parse(adminEmailsSetting.value) : ['<EMAIL>'],
        emailFromName: emailFromNameSetting?.value || 'Nirvana Organics',
        emailFromAddress: emailFromAddressSetting?.value || '<EMAIL>',
        enableOrderNotifications: orderNotificationsSetting ? orderNotificationsSetting.value === 'true' : true,
        enableStatusUpdates: statusUpdatesSetting ? statusUpdatesSetting.value === 'true' : true,
        enableMarketingEmails: marketingEmailsSetting ? marketingEmailsSetting.value === 'true' : true
      };

      res.json({
        success: true,
        data: settings
      });

    } catch (error) {
      console.error('Get admin email settings error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch admin email settings',
        error: error.message
      });
    }
  }

  /**
   * Update admin email settings
   * @route PUT /api/admin/email-management/settings
   * @access Private (Admin)
   */
  static async updateAdminEmailSettings(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      const {
        adminNotificationEmails,
        emailFromName,
        emailFromAddress,
        enableOrderNotifications,
        enableStatusUpdates,
        enableMarketingEmails
      } = req.body;

      // Update or create settings
      const settingsToUpdate = [
        { key: 'admin_notification_emails', value: JSON.stringify(adminNotificationEmails) },
        { key: 'email_from_name', value: emailFromName },
        { key: 'email_from_address', value: emailFromAddress },
        { key: 'enable_order_notifications', value: enableOrderNotifications.toString() },
        { key: 'enable_status_updates', value: enableStatusUpdates.toString() },
        { key: 'enable_marketing_emails', value: enableMarketingEmails.toString() }
      ];

      for (const setting of settingsToUpdate) {
        await models.SystemSetting.upsert({
          key: setting.key,
          value: setting.value,
          category: 'email',
          isActive: true
        });
      }

      res.json({
        success: true,
        message: 'Email settings updated successfully'
      });

    } catch (error) {
      console.error('Update admin email settings error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to update email settings',
        error: error.message
      });
    }
  }

  /**
   * Test email configuration
   * @route POST /api/admin/email-management/test
   * @access Private (Admin)
   */
  static async testEmailConfiguration(req, res) {
    try {
      const result = await emailNotificationService.testEmailConfiguration();

      if (result.success) {
        res.json({
          success: true,
          message: 'Test email sent successfully',
          data: { messageId: result.messageId }
        });
      } else {
        res.status(400).json({
          success: false,
          message: 'Test email failed',
          error: result.error
        });
      }

    } catch (error) {
      console.error('Test email configuration error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to test email configuration',
        error: error.message
      });
    }
  }

  /**
   * Retry failed email
   * @route POST /api/admin/email-management/logs/:id/retry
   * @access Private (Admin)
   */
  static async retryFailedEmail(req, res) {
    try {
      const { id } = req.params;

      const emailLog = await models.EmailLog.findByPk(id);
      if (!emailLog) {
        return res.status(404).json({
          success: false,
          message: 'Email log not found'
        });
      }

      if (emailLog.status !== 'failed') {
        return res.status(400).json({
          success: false,
          message: 'Only failed emails can be retried'
        });
      }

      // Reset status to queued for retry
      await emailLog.update({
        status: 'queued',
        attempts: 0,
        errorMessage: null
      });

      res.json({
        success: true,
        message: 'Email queued for retry'
      });

    } catch (error) {
      console.error('Retry failed email error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to retry email',
        error: error.message
      });
    }
  }
}

module.exports = EmailManagementController;
