#!/bin/bash
# Make script executable: chmod +x test-admin-assets-fix.sh

# Nirvana Organics - Test Admin Assets Fix
# Comprehensive testing script to verify admin assets are properly served

set -e

echo "🧪 Nirvana Organics - Test Admin Assets Fix"
echo "==========================================="

# Color codes
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}✅ $1${NC}"
}

warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

error() {
    echo -e "${RED}❌ $1${NC}"
}

# Configuration variables
TARGET_ADMIN="/var/www/nirvana-frontend-test/admin"
MISSING_JS="admin-DitisM-I.js"
MISSING_CSS="admin-TY7ZtfqV.css"
BASE_URL="https://test.shopnirvanaorganics.com"

# Test results tracking
TESTS_PASSED=0
TESTS_FAILED=0

run_test() {
    local test_name="$1"
    local test_command="$2"
    local expected_result="$3"
    
    echo ""
    echo "🧪 Test: $test_name"
    echo "   Command: $test_command"
    
    result=$(eval "$test_command" 2>/dev/null || echo "FAILED")
    
    if [ "$result" = "$expected_result" ]; then
        success "PASSED - Got expected result: $result"
        ((TESTS_PASSED++))
    else
        error "FAILED - Expected: $expected_result, Got: $result"
        ((TESTS_FAILED++))
    fi
}

# Step 1: File system tests
log "Step 1: File system tests..."

echo "📁 Testing file system presence and permissions:"

# Test admin directory exists
if [ -d "$TARGET_ADMIN" ]; then
    success "Admin directory exists: $TARGET_ADMIN"
else
    error "Admin directory missing: $TARGET_ADMIN"
    exit 1
fi

# Test admin.html exists
if [ -f "$TARGET_ADMIN/admin.html" ]; then
    success "admin.html exists"
else
    error "admin.html missing"
    exit 1
fi

# Test assets directory exists
if [ -d "$TARGET_ADMIN/assets" ]; then
    success "Assets directory exists: $TARGET_ADMIN/assets"
else
    error "Assets directory missing: $TARGET_ADMIN/assets"
    exit 1
fi

# Test JavaScript asset
if [ -f "$TARGET_ADMIN/assets/$MISSING_JS" ]; then
    JS_SIZE=$(stat -c%s "$TARGET_ADMIN/assets/$MISSING_JS")
    success "JavaScript asset exists: $MISSING_JS ($JS_SIZE bytes)"
else
    error "JavaScript asset missing: $MISSING_JS"
    exit 1
fi

# Test CSS asset
if [ -f "$TARGET_ADMIN/assets/$MISSING_CSS" ]; then
    CSS_SIZE=$(stat -c%s "$TARGET_ADMIN/assets/$MISSING_CSS")
    success "CSS asset exists: $MISSING_CSS ($CSS_SIZE bytes)"
else
    error "CSS asset missing: $MISSING_CSS"
    exit 1
fi

# Step 2: HTTP response tests
log "Step 2: HTTP response tests..."

echo "🌐 Testing HTTP responses:"

# Test admin.html accessibility
run_test "Admin HTML accessibility" \
    "curl -s -o /dev/null -w '%{http_code}' '$BASE_URL/admin/'" \
    "200"

# Test JavaScript asset accessibility
run_test "JavaScript asset accessibility" \
    "curl -s -o /dev/null -w '%{http_code}' '$BASE_URL/admin/assets/$MISSING_JS'" \
    "200"

# Test CSS asset accessibility
run_test "CSS asset accessibility" \
    "curl -s -o /dev/null -w '%{http_code}' '$BASE_URL/admin/assets/$MISSING_CSS'" \
    "200"

# Step 3: Content type tests
log "Step 3: Content type tests..."

echo "📄 Testing content types:"

# Test JavaScript content type
JS_CONTENT_TYPE=$(curl -s -I "$BASE_URL/admin/assets/$MISSING_JS" | grep -i "content-type" | cut -d: -f2 | tr -d ' \r\n' || echo "unknown")
if [[ "$JS_CONTENT_TYPE" == *"javascript"* ]] || [[ "$JS_CONTENT_TYPE" == *"application/javascript"* ]]; then
    success "JavaScript has correct content type: $JS_CONTENT_TYPE"
    ((TESTS_PASSED++))
else
    error "JavaScript has incorrect content type: $JS_CONTENT_TYPE"
    ((TESTS_FAILED++))
fi

# Test CSS content type
CSS_CONTENT_TYPE=$(curl -s -I "$BASE_URL/admin/assets/$MISSING_CSS" | grep -i "content-type" | cut -d: -f2 | tr -d ' \r\n' || echo "unknown")
if [[ "$CSS_CONTENT_TYPE" == *"css"* ]] || [[ "$CSS_CONTENT_TYPE" == *"text/css"* ]]; then
    success "CSS has correct content type: $CSS_CONTENT_TYPE"
    ((TESTS_PASSED++))
else
    error "CSS has incorrect content type: $CSS_CONTENT_TYPE"
    ((TESTS_FAILED++))
fi

# Step 4: Response size tests
log "Step 4: Response size tests..."

echo "📊 Testing response sizes:"

# Test JavaScript response size
JS_RESPONSE_SIZE=$(curl -s -I "$BASE_URL/admin/assets/$MISSING_JS" | grep -i "content-length" | cut -d: -f2 | tr -d ' \r\n' || echo "0")
if [ "$JS_RESPONSE_SIZE" -gt 100000 ]; then
    success "JavaScript response size is reasonable: $JS_RESPONSE_SIZE bytes"
    ((TESTS_PASSED++))
else
    error "JavaScript response size is too small: $JS_RESPONSE_SIZE bytes"
    ((TESTS_FAILED++))
fi

# Test CSS response size
CSS_RESPONSE_SIZE=$(curl -s -I "$BASE_URL/admin/assets/$MISSING_CSS" | grep -i "content-length" | cut -d: -f2 | tr -d ' \r\n' || echo "0")
if [ "$CSS_RESPONSE_SIZE" -gt 10000 ]; then
    success "CSS response size is reasonable: $CSS_RESPONSE_SIZE bytes"
    ((TESTS_PASSED++))
else
    error "CSS response size is too small: $CSS_RESPONSE_SIZE bytes"
    ((TESTS_FAILED++))
fi

# Step 5: Cache headers test
log "Step 5: Cache headers test..."

echo "🗄️ Testing cache headers:"

# Test JavaScript cache headers
JS_CACHE_CONTROL=$(curl -s -I "$BASE_URL/admin/assets/$MISSING_JS" | grep -i "cache-control" | cut -d: -f2 | tr -d ' \r\n' || echo "none")
if [[ "$JS_CACHE_CONTROL" == *"public"* ]]; then
    success "JavaScript has proper cache headers: $JS_CACHE_CONTROL"
    ((TESTS_PASSED++))
else
    warning "JavaScript cache headers may need optimization: $JS_CACHE_CONTROL"
    ((TESTS_FAILED++))
fi

# Step 6: Admin panel loading test
log "Step 6: Admin panel loading test..."

echo "🖥️ Testing admin panel loading:"

# Test if admin.html contains references to the assets
ADMIN_HTML_CONTENT=$(curl -s "$BASE_URL/admin/" || echo "")

if [[ "$ADMIN_HTML_CONTENT" == *"$MISSING_JS"* ]]; then
    success "admin.html references JavaScript asset correctly"
    ((TESTS_PASSED++))
else
    error "admin.html does not reference JavaScript asset"
    ((TESTS_FAILED++))
fi

if [[ "$ADMIN_HTML_CONTENT" == *"$MISSING_CSS"* ]]; then
    success "admin.html references CSS asset correctly"
    ((TESTS_PASSED++))
else
    error "admin.html does not reference CSS asset"
    ((TESTS_FAILED++))
fi

# Step 7: Test summary
log "Step 7: Test summary"

echo ""
echo "📊 TEST RESULTS SUMMARY"
echo "======================="
echo "Tests Passed: $TESTS_PASSED"
echo "Tests Failed: $TESTS_FAILED"
echo "Total Tests: $((TESTS_PASSED + TESTS_FAILED))"

if [ $TESTS_FAILED -eq 0 ]; then
    success "🎉 ALL TESTS PASSED!"
    success "✅ Admin assets are properly configured and accessible"
    success "✅ HTTP 404 errors should be completely resolved"
    success "✅ Admin panel should load with full styling and functionality"
else
    error "❌ $TESTS_FAILED tests failed"
    echo ""
    echo "🔧 TROUBLESHOOTING RECOMMENDATIONS:"
    
    if [ $TESTS_FAILED -gt 3 ]; then
        echo "1. Run the Nginx admin assets fix script: sudo ./fix-nginx-admin-assets.sh"
        echo "2. Verify admin assets are deployed: ls -la $TARGET_ADMIN/assets/"
        echo "3. Check Nginx configuration: sudo nginx -t"
        echo "4. Check Nginx error logs: sudo tail -f /var/log/nginx/nirvana-test-main-error.log"
    else
        echo "1. Minor issues detected - check specific failed tests above"
        echo "2. Consider reloading Nginx: sudo systemctl reload nginx"
        echo "3. Verify file permissions: sudo chown -R Nirvana:Nirvana /var/www/nirvana-frontend-test/"
    fi
fi

echo ""
echo "🔍 MANUAL VERIFICATION STEPS"
echo "============================"
echo "1. Open browser: $BASE_URL/admin/"
echo "2. Open browser developer tools (F12)"
echo "3. Check Network tab for any 404 errors"
echo "4. Verify admin panel loads with proper styling"
echo "5. Test admin functionality (login, navigation, etc.)"

echo ""
echo "📊 ASSET URLS FOR MANUAL TESTING"
echo "================================="
echo "Admin Panel: $BASE_URL/admin/"
echo "JavaScript: $BASE_URL/admin/assets/$MISSING_JS"
echo "CSS: $BASE_URL/admin/assets/$MISSING_CSS"

echo ""
success "🎉 Admin assets testing completed!"
