/// <reference types="node" />
import { FileWrapper } from '@apimatic/file-wrapper';
import { ApiResponse, AuthenticatorInterface, HttpContext, HttpMethod, HttpRequest, HttpInterceptorInterface, RequestOptions, RetryConfiguration, ApiLoggerInterface, HttpClientInterface, PagedAsyncIterable } from '../coreInterfaces';
import { Schema } from '../schema';
import { PathTemplatePrimitiveTypes, PathTemplateTypes, SkipEncode } from './pathTemplate';
import { ArrayPrefixFunction } from './queryString';
import { prepareArgs } from './validate';
import { RequestRetryOption } from './retryConfiguration';
import { XmlSerializerInterface } from '../xml/xmlSerializer';
import { PathParam } from './pathParam';
export type RequestBuilderFactory<BaseUrlParamType, AuthParams> = (httpMethod: HttpMethod, path?: string) => RequestBuilder<BaseUrlParamType, AuthParams>;
export declare function skipEncode<T extends PathTemplatePrimitiveTypes>(value: T, key?: string): SkipEncode<T>;
export declare function pathParam<T extends PathTemplatePrimitiveTypes>(value: T, key: string): PathParam<T>;
export type ApiErrorConstructor = new (response: HttpContext, message: string) => any;
export interface ErrorType<ErrorCtorArgs extends any[]> {
    statusCode: number | [number, number];
    errorConstructor: new (response: HttpContext, ...args: ErrorCtorArgs) => any;
    isTemplate?: boolean;
    args: ErrorCtorArgs;
}
export interface ApiErrorFactory {
    apiErrorCtor: ApiErrorConstructor;
    message?: string | undefined;
}
export interface RequestBuilder<BaseUrlParamType, AuthParams> {
    deprecated(methodName: string, message?: string): void;
    prepareArgs: typeof prepareArgs;
    method(httpMethodName: HttpMethod): void;
    baseUrl(arg: BaseUrlParamType): void;
    authenticate(params: AuthParams): void;
    appendPath(path: string): void;
    appendTemplatePath(strings: TemplateStringsArray, ...args: PathTemplateTypes[]): void;
    acceptJson(): void;
    accept(acceptHeaderValue: string): void;
    contentType(contentTypeHeaderValue: string): void;
    header(name: string, value?: unknown): void;
    headers(headersToMerge: Record<string, string>): void;
    query(name: string, value: unknown | Record<string, unknown>, prefixFormat?: ArrayPrefixFunction): void;
    query(parameters?: Record<string, unknown> | null, prefixFormat?: ArrayPrefixFunction): void;
    form(parameters: Record<string, unknown>, prefixFormat?: ArrayPrefixFunction): void;
    formData(parameters: Record<string, unknown>, prefixFormat?: ArrayPrefixFunction): void;
    text(body: string | number | bigint | boolean | null | undefined): void;
    json(data: unknown): void;
    requestRetryOption(option: RequestRetryOption): void;
    xml<T>(argName: string, data: T, rootName: string, schema: Schema<T, any>): void;
    stream(file?: FileWrapper): void;
    toRequest(): HttpRequest;
    intercept(interceptor: HttpInterceptorInterface<RequestOptions | undefined>): void;
    interceptRequest(interceptor: (request: HttpRequest) => HttpRequest): void;
    interceptResponse(interceptor: (response: HttpContext) => HttpContext): void;
    defaultToError(apiErrorCtor: ApiErrorConstructor, message?: string): void;
    validateResponse(validate: boolean): void;
    throwOn<ErrorCtorArgs extends any[]>(statusCode: number | [number, number], errorConstructor: new (response: HttpContext, ...args: ErrorCtorArgs) => any, ...args: ErrorCtorArgs): void;
    throwOn<ErrorCtorArgs extends any[]>(statusCode: number | [number, number], errorConstructor: new (response: HttpContext, ...args: ErrorCtorArgs) => any, isTemplate: boolean, ...args: ErrorCtorArgs): void;
    updateByJsonPointer(pointer: string | null, updater: (value: any) => any): RequestBuilder<BaseUrlParamType, AuthParams>;
    paginate<TItem, TPagedResponse>(createPagedIterable: (req: this, updater: (req: this) => (pointer: string | null, setter: (value: any) => any) => this) => PagedAsyncIterable<TItem, TPagedResponse>): PagedAsyncIterable<TItem, TPagedResponse>;
    call(requestOptions?: RequestOptions): Promise<ApiResponse<void>>;
    callAsJson<T>(schema: Schema<T, any>, requestOptions?: RequestOptions): Promise<ApiResponse<T>>;
    callAsStream(requestOptions?: RequestOptions): Promise<ApiResponse<NodeJS.ReadableStream | Blob>>;
    callAsText(requestOptions?: RequestOptions): Promise<ApiResponse<string>>;
    callAsOptionalText(requestOptions?: RequestOptions): Promise<ApiResponse<string | undefined>>;
    callAsXml<T>(rootName: string, schema: Schema<T, any>, requestOptions?: RequestOptions): Promise<ApiResponse<T>>;
    callAsXml<T>(rootName: string, schema: Schema<T, any>, requestOptions?: RequestOptions): Promise<ApiResponse<T>>;
}
export declare class DefaultRequestBuilder<BaseUrlParamType, AuthParams> implements RequestBuilder<BaseUrlParamType, AuthParams> {
    protected _httpClient: HttpClientInterface;
    protected _baseUrlProvider: (arg?: BaseUrlParamType) => string;
    protected _apiErrorCtr: ApiErrorConstructor;
    protected _authenticationProvider: AuthenticatorInterface<AuthParams>;
    protected _httpMethod: HttpMethod;
    protected _xmlSerializer: XmlSerializerInterface;
    protected _retryConfig: RetryConfiguration;
    protected _path?: string | undefined;
    protected _apiLogger?: ApiLoggerInterface | undefined;
    protected _queryParams: Record<string, unknown>;
    protected _pathParams: Record<string, unknown>;
    protected _headerParams: Record<string, unknown>;
    protected _body?: any;
    protected _accept?: string;
    protected _contentType?: string;
    protected _contentTypeOptional?: string;
    protected _bodyType?: 'text' | 'json' | 'xml' | 'form' | 'form-data';
    protected _formPrefixFormat?: ArrayPrefixFunction;
    protected _queryParamsPrefixFormat: Record<string, ArrayPrefixFunction>;
    protected _stream?: FileWrapper;
    protected _pathStrings?: TemplateStringsArray;
    protected _pathArgs?: PathTemplateTypes[];
    protected _baseUrlArg?: BaseUrlParamType;
    protected _validateResponse: boolean;
    protected _interceptors: Array<HttpInterceptorInterface<RequestOptions | undefined>>;
    protected _authParams?: AuthParams;
    protected _retryOption: RequestRetryOption;
    protected _apiErrorFactory: ApiErrorFactory;
    protected _errorTypes: Array<ErrorType<any>>;
    prepareArgs: typeof prepareArgs;
    constructor(_httpClient: HttpClientInterface, _baseUrlProvider: (arg?: BaseUrlParamType) => string, _apiErrorCtr: ApiErrorConstructor, _authenticationProvider: AuthenticatorInterface<AuthParams>, _httpMethod: HttpMethod, _xmlSerializer: XmlSerializerInterface, _retryConfig: RetryConfiguration, _path?: string | undefined, _apiLogger?: ApiLoggerInterface | undefined);
    authenticate(params: AuthParams): void;
    requestRetryOption(option: RequestRetryOption): void;
    deprecated(methodName: string, message?: string): void;
    appendTemplatePath(strings: TemplateStringsArray, ...args: PathTemplateTypes[]): void;
    method(httpMethodName: HttpMethod): void;
    baseUrl(arg: BaseUrlParamType): void;
    appendPath(path: string): void;
    acceptJson(): void;
    accept(acceptHeaderValue: string): void;
    contentType(contentTypeHeaderValue: string): void;
    header(name: string, value?: unknown): void;
    headers(headersToMerge: Record<string, string>): void;
    query(name: string, value: unknown | Record<string, unknown>, prefixFormat?: ArrayPrefixFunction): void;
    query(parameters?: Record<string, unknown> | null, prefixFormat?: ArrayPrefixFunction): void;
    private setPrefixFormats;
    private setQueryParams;
    text(body: string | number | bigint | boolean | null | undefined): void;
    json(data: unknown): void;
    xml<T>(argName: string, data: T, rootName: string, schema: Schema<T, any>): void;
    stream(file?: FileWrapper): void;
    form(parameters: Record<string, unknown>, prefixFormat?: ArrayPrefixFunction): void;
    formData(parameters: Record<string, unknown>, prefixFormat?: ArrayPrefixFunction): void;
    toRequest(): HttpRequest;
    intercept(interceptor: HttpInterceptorInterface<RequestOptions | undefined>): void;
    interceptRequest(interceptor: (httpRequest: HttpRequest) => HttpRequest): void;
    interceptResponse(interceptor: (response: HttpContext) => HttpContext): void;
    defaultToError(apiErrorCtor: ApiErrorConstructor, message?: string): void;
    validateResponse(validate: boolean): void;
    throwOn<ErrorCtorArgs extends any[]>(statusCode: number | [number, number], errorConstructor: new (response: HttpContext, ...args: ErrorCtorArgs) => any, ...args: ErrorCtorArgs): void;
    call(requestOptions?: RequestOptions): Promise<ApiResponse<void>>;
    callAsText(requestOptions?: RequestOptions): Promise<ApiResponse<string>>;
    callAsOptionalText(requestOptions?: RequestOptions): Promise<ApiResponse<string | undefined>>;
    callAsStream(requestOptions?: RequestOptions): Promise<ApiResponse<NodeJS.ReadableStream | Blob>>;
    callAsJson<T>(schema: Schema<T>, requestOptions?: RequestOptions): Promise<ApiResponse<T>>;
    callAsXml<T>(rootName: string, schema: Schema<T, any>, requestOptions?: RequestOptions): Promise<ApiResponse<T>>;
    paginate<TItem, TPagedResponse>(createPagedIterable: (req: this, updater: (req: this) => (pointer: string | null, setter: (value: any) => any) => this) => PagedAsyncIterable<TItem, TPagedResponse>): PagedAsyncIterable<TItem, TPagedResponse>;
    updateByJsonPointer(pointer: string | null, updater: (value: any) => any): RequestBuilder<BaseUrlParamType, AuthParams>;
    private _clone;
    private cloneParameters;
    private _addResponseValidator;
    private _addApiLoggerInterceptors;
    private _getQueryUrl;
    private _buildPath;
    private _getHttpRequestHeaders;
    private _getHttpRequestBody;
    private _addAuthentication;
    private _addRetryInterceptor;
    private _addErrorHandlingInterceptor;
}
export declare function createRequestBuilderFactory<BaseUrlParamType, AuthParams>(httpClient: HttpClientInterface, baseUrlProvider: (arg?: BaseUrlParamType) => string, apiErrorConstructor: ApiErrorConstructor, authenticationProvider: AuthenticatorInterface<AuthParams>, retryConfig: RetryConfiguration, xmlSerializer?: XmlSerializerInterface, apiLogger?: ApiLoggerInterface): RequestBuilderFactory<BaseUrlParamType, AuthParams>;
//# sourceMappingURL=requestBuilder.d.ts.map