var n,t;n=this,t=function(n){"use strict";var t=function(){return t=Object.assign||function(n){for(var t,e=1,r=arguments.length;e<r;e++)for(var a in t=arguments[e])Object.prototype.hasOwnProperty.call(t,a)&&(n[a]=t[a]);return n},t.apply(this,arguments)};function e(n,t){var e={};for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&t.indexOf(r)<0&&(e[r]=n[r]);if(null!=n&&"function"==typeof Object.getOwnPropertySymbols){var a=0;for(r=Object.getOwnPropertySymbols(n);a<r.length;a++)t.indexOf(r[a])<0&&Object.prototype.propertyIsEnumerable.call(n,r[a])&&(e[r[a]]=n[r[a]])}return e}function r(n){var t="function"==typeof Symbol&&Symbol.iterator,e=t&&n[t],r=0;if(e)return e.call(n);if(n&&"number"==typeof n.length)return{next:function(){return n&&r>=n.length&&(n=void 0),{value:n&&n[r++],done:!n}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}function a(n,t){var e="function"==typeof Symbol&&n[Symbol.iterator];if(!e)return n;var r,a,o=e.call(n),i=[];try{for(;(void 0===t||t-- >0)&&!(r=o.next()).done;)i.push(r.value)}catch(n){a={error:n}}finally{try{r&&!r.done&&(e=o.return)&&e.call(o)}finally{if(a)throw a.error}}return i}function o(n,t,e){if(e||2===arguments.length)for(var r,a=0,o=t.length;a<o;a++)!r&&a in t||(r||(r=Array.prototype.slice.call(t,0,a)),r[a]=t[a]);return n.concat(r||Array.prototype.slice.call(t))}function i(n){for(var t=[],e=0;e<n.length;e++){var r=n[e];t.push([e,r])}return t}function u(n){for(var t=Object.keys(n),e=t.length,r=new Array(e);e--;)r[e]=[t[e],n[t[e]]];return r}function l(n){return"string"==typeof n?'"'.concat(n.replace(/"/g,'"'),'"'):"".concat(n)}function f(n){return n}function c(n){return function(t,e){return n(t,e.strictValidation)?[]:e.fail()}}function p(n){return e={type:function(){return n.type},validateBeforeMap:n.validate,validateBeforeUnmap:n.validate,map:n.map,unmap:n.map},t(t({},e),{validateBeforeMapXml:e.validateBeforeUnmap,mapXml:e.map,unmapXml:e.unmap});var e}function m(n,t){return t?"number"==typeof n:"number"==typeof n||"string"==typeof n&&!isNaN(n)}function v(n){return"number"==typeof n?n:+n}function d(n){return"bigint"==typeof n?n:BigInt(n)}function y(n,t){var e=new Set(t),r={};for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&!e.has(a)&&(r[a]=n[a]);return r}function h(n){return-1!==n.indexOf(" ")?l(n):n}function s(n){return null==n}function b(n,t){return function(n){return n.startsWith("Optional<Nullable<")||n.startsWith("Nullable<Optional<")}(n)&&s(t)}function X(n,t){var e=j(g(n,t.type())),r=t.validateBeforeMap(n,e);return 0===r.length?b(t.type(),n)?{errors:!1,result:n}:{errors:!1,result:t.map(n,e)}:{errors:r}}function B(n,t){var e=j(g(n,t.type())),r=t.validateBeforeUnmap(n,e);return 0===r.length?{errors:!1,result:t.unmap(n,e)}:{errors:r}}function g(n,t,e){return{value:n,type:t,branch:[n],path:[],strictValidation:e}}function j(n){var e=function(t,e,r){return j({value:e,type:r.type(),branch:n.branch.concat(e),path:n.path.concat(t),strictValidation:n.strictValidation})},i=function(n,t,r){return n.map((function(n){return r(n,e(n[0],n[1],t))}))};return t(t({},n),{createChild:e,flatmapChildren:function(){for(var n=[],t=0;t<arguments.length;t++)n[t]=arguments[t];return function(n){var t,e,a,o,i=[];try{for(var u=r(n),l=u.next();!l.done;l=u.next()){var f=l.value;try{for(var c=(a=void 0,r(f)),p=c.next();!p.done;p=c.next()){var m=p.value;i.push(m)}}catch(n){a={error:n}}finally{try{p&&!p.done&&(o=c.return)&&o.call(c)}finally{if(a)throw a.error}}}}catch(n){t={error:n}}finally{try{l&&!l.done&&(e=u.return)&&e.call(u)}finally{if(t)throw t.error}}return i}(i.apply(void 0,o([],a(n),!1)))},mapChildren:i,fail:function(t){return[{value:n.value,type:n.type,branch:n.branch,path:n.path,message:O(n,t)}]}})}function O(n,t){var e=JSON.stringify(n.value,(function(n,t){return"bigint"==typeof t?t.toString():t}));if(t=(null!=t?t:"Expected value to be of type '".concat(n.type,"' but found '").concat(typeof n.value,"'."))+"\n"+"\nGiven value: ".concat(e)+"\nType: '".concat(typeof n.value,"'")+"\nExpected type: '".concat(n.type,"'"),n.path.length>0){var r=n.path.map((function(n){return h(n.toString())})).join(" › ");t+="\nPath: ".concat(r)}return t}function x(n,t,e){var a,o,i=[];try{for(var u=r(n),l=u.next();!l.done;l=u.next()){var f=l.value;0===f.validateBeforeMap(t,e).length&&i.push(f)}}catch(n){a={error:n}}finally{try{l&&!l.done&&(o=u.return)&&o.call(u)}finally{if(a)throw a.error}}return S(i,e)}function M(n,t,e){var a,o,i=[];try{for(var u=r(n),l=u.next();!l.done;l=u.next()){var f=l.value;0===f.validateBeforeUnmap(t,e).length&&i.push(f)}}catch(n){a={error:n}}finally{try{l&&!l.done&&(o=u.return)&&o.call(u)}finally{if(a)throw a.error}}return S(i,e)}function w(n,t,e){var a,o,i=[];try{for(var u=r(n),l=u.next();!l.done;l=u.next()){var f=l.value;0===f.validateBeforeMapXml(t,e).length&&i.push(f)}}catch(n){a={error:n}}finally{try{l&&!l.done&&(o=u.return)&&o.call(u)}finally{if(a)throw a.error}}return S(i,e)}function S(n,t){return n.length>0?[]:t.fail("Could not match against any acceptable type.")}function A(n,t,e){var a,o,i=[];try{for(var u=r(n),l=u.next();!l.done;l=u.next()){var f=l.value;0===f.validateBeforeMap(t,e).length&&i.push(f)}}catch(n){a={error:n}}finally{try{l&&!l.done&&(o=u.return)&&o.call(u)}finally{if(a)throw a.error}}return i.length>0?i[0].map(t,e):void 0}function U(n,t,e){var a,o,i=[];try{for(var u=r(n),l=u.next();!l.done;l=u.next()){var f=l.value;0===f.validateBeforeUnmap(t,e).length&&i.push(f)}}catch(n){a={error:n}}finally{try{l&&!l.done&&(o=u.return)&&o.call(u)}finally{if(a)throw a.error}}return i.length>0?i[0].unmap(t,e):void 0}function P(n,t,e){var a,o,i=[];try{for(var u=r(n),l=u.next();!l.done;l=u.next()){var f=l.value;0===f.validateBeforeMapXml(t,e).length&&i.push(f)}}catch(n){a={error:n}}finally{try{l&&!l.done&&(o=u.return)&&o.call(u)}finally{if(a)throw a.error}}return i.length>0?i[0].mapXml(t,e):void 0}function N(n,t,e){var a,o,i=[];try{for(var u=r(n),l=u.next();!l.done;l=u.next()){var f=l.value;0===f.validateBeforeMapXml(t,e).length&&i.push(f)}}catch(n){a={error:n}}finally{try{l&&!l.done&&(o=u.return)&&o.call(u)}finally{if(a)throw a.error}}return i.length>0?i[0].unmapXml(t,e):void 0}function E(n,t){return t?"bigint"==typeof n:"bigint"==typeof n||"number"==typeof n||"string"==typeof n&&/^-?\d+$/.test(n)}function C(n,t){return t?"boolean"==typeof n:"boolean"==typeof n||"string"==typeof n&&("true"===n||"false"===n)}function k(n,t){return null==n||n===t}function T(n){var t=function(t,e,r){if("object"!=typeof e||null===e)return r.fail();var a=e;return r.flatmapChildren(u(a),n,(function(e,r){return n[t](e[1],r)}))};return{type:function(){return"Record<string,".concat(n.type(),">")},validateBeforeMap:function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];return t.apply(void 0,o(["validateBeforeMap"],a(n),!1))},validateBeforeUnmap:function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];return t.apply(void 0,o(["validateBeforeUnmap"],a(n),!1))},map:function(t,e){var r={};for(var a in t)if(Object.prototype.hasOwnProperty.call(t,a)){var o=t[a];r[a]=n.map(o,e.createChild(a,o,n))}return r},unmap:function(t,e){var r={};for(var a in t)if(Object.prototype.hasOwnProperty.call(t,a)){var o=t[a];r[a]=n.unmap(o,e.createChild(a,o,n))}return r},validateBeforeMapXml:function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];return t.apply(void 0,o(["validateBeforeMapXml"],a(n),!1))},mapXml:function(t,e){var r={};for(var a in t)if(Object.prototype.hasOwnProperty.call(t,a)){var o=t[a];r[a]=n.mapXml(o,e.createChild(a,o,n))}return r},unmapXml:function(t,e){var r={};for(var a in t)if(Object.prototype.hasOwnProperty.call(t,a)){var o=t[a];r[a]=n.unmapXml(o,e.createChild(a,o,n))}return r}}}function $(n){return{type:function(){return"Optional<".concat(n.type(),">")},validateBeforeMap:function(t,e){return s(t)?[]:n.validateBeforeMap(t,e)},validateBeforeUnmap:function(t,e){return void 0===t?[]:n.validateBeforeUnmap(t,e)},map:function(t,e){return s(t)?void 0:n.map(t,e)},unmap:function(t,e){return void 0===t?void 0:n.unmap(t,e)},validateBeforeMapXml:function(t,e){return void 0===t?[]:n.validateBeforeMapXml(t,e)},mapXml:function(t,e){return void 0===t?void 0:n.mapXml(t,e)},unmapXml:function(t,e){return void 0===t?void 0:n.unmapXml(t,e)}}}function V(n){var t=L(n,!1,!1);return t.type=function(){return"StrictObject<{".concat(Object.keys(n).map(h).join(","),"}>")},t}function I(n){return L(n,!0,!0)}function W(n){var t=L(n,!0,!1);return t.type=function(){return"Object<{".concat(Object.keys(n).map(h).join(","),"}>")},t}function L(n,t,e){var r=Object.keys(n),o=H(n),i=function(n){var t,e,r={},o={};for(var i in n)if(Object.prototype.hasOwnProperty.call(n,i)){var u=a(n[i],3),l=u[0],f=u[2];!0===(null==f?void 0:f.isAttr)?o[null!==(t=f.xmlName)&&void 0!==t?t:l]=i:r[null!==(e=null==f?void 0:f.xmlName)&&void 0!==e?e:l]=i}return{elementsToProps:r,attributesToProps:o}}(n),u=function(n){var t,e={},r={};for(var o in n)if(Object.prototype.hasOwnProperty.call(n,o)){var i=a(n[o],3),u=i[0],l=i[1],f=i[2];((null==f?void 0:f.isAttr)?r:e)[o]=[null!==(t=null==f?void 0:f.xmlName)&&void 0!==t?t:u,l,f]}return{elementsSchema:e,attributesSchema:r}}(n),l=function(n){return{attributesSchema:H(n.attributesSchema),elementsSchema:H(n.elementsSchema)}}(u);return{type:function(){return"Object<{".concat(r.map(h).join(","),",...}>")},validateBeforeMap:q(n,"validateBeforeMap",t,e),validateBeforeUnmap:q(o,"validateBeforeUnmap",t,e),map:F(n,"map",e),unmap:F(o,"unmap",e),validateBeforeMapXml:_(n,i,t,e),mapXml:z(u,e),unmapXml:D(l,e),objectSchema:n}}function _(n,r,a,o){var i=r.elementsToProps,u=r.attributesToProps;return function(r,l){if("object"!=typeof r||null===r)return l.fail();if(Array.isArray(r))return l.fail("Expected value to be of type '".concat(l.type,"' but found 'Array<").concat(typeof r,">'."));var f=r,c=f.$,p=e(f,["$"]),m={validationMethod:"validateBeforeMapXml",propTypeName:"child elements",propTypePrefix:"element",valueTypeName:"element",propMapping:i,objectSchema:n,valueObject:p,ctxt:l,skipAdditionalPropValidation:a,mapAdditionalProps:o},v=G(m),d=G(m=t(t({},m),{propTypeName:"attributes",propTypePrefix:"@",propMapping:u,valueObject:null!=c?c:{}}));return v.concat(d)}}function z(n,r){var o=n.elementsSchema,i=n.attributesSchema,l=F(o,"mapXml",r),f=F(i,"mapXml",!1),c=u(i).map((function(n){var t=a(n,2);return t[0],a(t[1],1)[0]}));return function(n,a){var o=n,i=o.$,u=e(o,["$"]),p=null!=i?i:{},m=t(t({},f(p,a)),l(u,a));if(r){var v=y(p,c);Object.keys(v).length>0&&(m.$=v)}return m}}function D(n,r){var o=n.elementsSchema,i=n.attributesSchema,l=F(o,"unmapXml",r),f=F(i,"unmapXml",!1),c=u(i).map((function(n){var t=a(n,2);return t[0],a(t[1],1)[0]}));return function(n,a){var o=n,i=o.$,u=e(o,["$"]),p="object"==typeof i&&null!==i&&r?i:{};return t(t({},l(y(u,c),a)),{$:t(t({},p),f(n,a))})}}function G(n){var t,e,o,i=n.validationMethod,u=n.propTypeName,l=n.propTypePrefix,f=n.valueTypeName,c=n.propMapping,p=n.objectSchema,m=n.valueObject,v=n.ctxt,d=n.skipAdditionalPropValidation,y=n.mapAdditionalProps,h=[],s=new Set,b=new Set,X=new Set(Object.keys(m));if("validateBeforeMap"!==i&&"boolean"!=typeof y&&y[0]in m)try{for(var B=r(Object.entries(m[y[0]])),g=B.next();!g.done;g=B.next()){var j=a(g.value,2),O=j[0];j[1],Object.prototype.hasOwnProperty.call(p,O)&&b.add(O)}}catch(n){t={error:n}}finally{try{g&&!g.done&&(e=B.return)&&e.call(B)}finally{if(t)throw t.error}}for(var O in R(b,(function(n){return J("Some keys in additional properties are conflicting with the keys in",f,n)}),h,v),c)if(Object.prototype.hasOwnProperty.call(c,O)){var x=p[c[O]][1];X.delete(O),O in m?x[i](m[O],v.createChild(l+O,m[O],x)).forEach((function(n){return h.push(n)})):(o=x.type()).startsWith("Optional<")||o.startsWith("Nullable<")||s.add(O)}return d||R(X,(function(n){return J("Some unknown ".concat(u," were found in the"),f,n)}),h,v),R(s,(function(n){return J("Some ".concat(u," are missing in the"),f,n)}),h,v),h}function J(n,t,e){return"".concat(n," ").concat(t,": ").concat(e.map(l).join(", "),".")}function R(n,t,e,r){var a=Array.from(n);if(a.length>0){var o=t(a);r.fail(o).forEach((function(n){return e.push(n)}))}}function q(n,t,e,r){var a=function(n){var t={};for(var e in n)Object.prototype.hasOwnProperty.call(n,e)&&(t[n[e][0]]=e);return t}(n);return function(o,i){return"object"!=typeof o||null===o?i.fail():Array.isArray(o)?i.fail("Expected value to be of type '".concat(i.type,"' but found 'Array<").concat(typeof o,">'.")):G({validationMethod:t,propTypeName:"properties",propTypePrefix:"",valueTypeName:"object",propMapping:a,objectSchema:n,valueObject:o,ctxt:i,skipAdditionalPropValidation:e,mapAdditionalProps:r})}}function F(n,e,r){return function(o,i){var u={},l=t({},o),f="unmap"===e||"unmapXml"===e;return f&&"boolean"!=typeof r&&r[0]in l&&(Object.entries(l[r[0]]).forEach((function(n){var t=a(n,2),e=t[0],r=t[1];return l[e]=r})),delete l[r[0]]),Object.entries(n).forEach((function(n){var t=a(n,2),r=t[0],o=t[1],f=o[0],c=l[f];if(delete l[f],b(o[1].type(),c)){if(void 0===c)return;u[r]=null}else(function(n,t){return n.startsWith("Optional<")&&void 0===t})(o[1].type(),c)||(u[r]=o[1][e](c,i.createChild(f,c,o[1])))})),Object.entries(function(n,t,e){var r,o={};return e?"boolean"==typeof e?(Object.entries(n).forEach((function(n){var t=a(n,2),e=t[0],r=t[1];return o[e]=r})),o):(Object.entries(n).forEach((function(n){var r,i=a(n,2),u=i[0],l=i[1],f=((r={})[u]=l,r),c=t?B(f,e[1]):X(f,e[1]);c.errors||(o[u]=c.result[u])})),t||0===Object.entries(o).length?o:((r={})[e[0]]=o,r)):o}(l,f,r)).forEach((function(n){var t=a(n,2),e=t[0],r=t[1];return u[e]=r})),u}}var H=function(n){return Object.entries(n).reduce((function(n,e){var r,o=a(e,2),i=o[0],u=o[1];return t(t({},n),((r={})[u[0]]=[i,u[1],u[2]],r))}),{})};function K(n,t,e){var a,o,i=[];e.strictValidation=!0;try{for(var u=r(n),l=u.next();!l.done;l=u.next()){var f=l.value;0===f.validateBeforeMap(t,e).length&&i.push(f)}}catch(n){a={error:n}}finally{try{l&&!l.done&&(o=u.return)&&o.call(u)}finally{if(a)throw a.error}}return Z(i,e)}function Q(n,t,e){var a,o,i=[];e.strictValidation=!0;try{for(var u=r(n),l=u.next();!l.done;l=u.next()){var f=l.value;0===f.validateBeforeUnmap(t,e).length&&i.push(f)}}catch(n){a={error:n}}finally{try{l&&!l.done&&(o=u.return)&&o.call(u)}finally{if(a)throw a.error}}return Z(i,e)}function Y(n,t,e){var a,o,i=[];e.strictValidation=!0;try{for(var u=r(n),l=u.next();!l.done;l=u.next()){var f=l.value;0===f.validateBeforeMapXml(t,e).length&&i.push(f)}}catch(n){a={error:n}}finally{try{l&&!l.done&&(o=u.return)&&o.call(u)}finally{if(a)throw a.error}}return Z(i,e)}function Z(n,t){return 1===n.length?[]:0===n.length?t.fail("Could not match against any acceptable type."):t.fail("Matched more than one type. Matched types include: ".concat(n.map((function(n){return n.type()})).join(", ")))}function nn(n,t,e){var a,o,i=[];e.strictValidation=!0;try{for(var u=r(n),l=u.next();!l.done;l=u.next()){var f=l.value;0===f.validateBeforeMap(t,e).length&&i.push(f)}}catch(n){a={error:n}}finally{try{l&&!l.done&&(o=u.return)&&o.call(u)}finally{if(a)throw a.error}}return 1===i.length?i[0].map(t,e):void 0}function tn(n,t,e){var a,o,i=[];e.strictValidation=!0;try{for(var u=r(n),l=u.next();!l.done;l=u.next()){var f=l.value;0===f.validateBeforeUnmap(t,e).length&&i.push(f)}}catch(n){a={error:n}}finally{try{l&&!l.done&&(o=u.return)&&o.call(u)}finally{if(a)throw a.error}}return 1===i.length?i[0].unmap(t,e):void 0}function en(n,t,e){var a,o,i=[];e.strictValidation=!0;try{for(var u=r(n),l=u.next();!l.done;l=u.next()){var f=l.value;0===f.validateBeforeMapXml(t,e).length&&i.push(f)}}catch(n){a={error:n}}finally{try{l&&!l.done&&(o=u.return)&&o.call(u)}finally{if(a)throw a.error}}return 1===i.length?i[0].mapXml(t,e):void 0}function rn(n,t,e){var a,o,i=[];e.strictValidation=!0;try{for(var u=r(n),l=u.next();!l.done;l=u.next()){var f=l.value;0===f.validateBeforeMapXml(t,e).length&&i.push(f)}}catch(n){a={error:n}}finally{try{l&&!l.done&&(o=u.return)&&o.call(u)}finally{if(a)throw a.error}}return 1===i.length?i[0].unmapXml(t,e):void 0}function an(n){return"string"==typeof n}n.anyOf=function(n,t,e){return t&&e?function(n,t,e){return{type:function(){return"OneOf<".concat(n.map((function(n){return n.type()})).join(" | "),">")},validateBeforeMap:function(r,a){var o=r&&"object"==typeof r&&r[e];return o&&t[o]?t[o].validateBeforeMap(r,a):x(n,r,a)},validateBeforeUnmap:function(r,a){var o=r&&"object"==typeof r&&r[e];return o&&t[o]?t[o].validateBeforeUnmap(r,a):M(n,r,a)},map:function(r,a){var o=r&&r[e];return o&&t[o]?t[o].map(r,a):A(n,r,a)},unmap:function(r,a){var o=r&&r[e];return o&&t[o]?t[o].unmap(r,a):U(n,r,a)},validateBeforeMapXml:function(r,a){var o=r&&"object"==typeof r&&r[e];return o&&t[o]?t[o].validateBeforeMapXml(r,a):w(n,r,a)},mapXml:function(r,a){var o=r&&r[e];return o&&t[o]?t[o].mapXml(r,a):P(n,r,a)},unmapXml:function(r,a){var o=r&&r[e];return o&&t[o]?t[o].unmapXml(r,a):N(n,r,a)}}}(n,t,e):function(n){return{type:function(){return"OneOf<".concat(n.map((function(n){return n.type()})).join(" | "),">")},validateBeforeMap:function(t,e){return x(n,t,e)},validateBeforeUnmap:function(t,e){return M(n,t,e)},map:function(t,e){return A(n,t,e)},unmap:function(t,e){return U(n,t,e)},validateBeforeMapXml:function(t,e){return w(n,t,e)},mapXml:function(t,e){return P(n,t,e)},unmapXml:function(t,e){return N(n,t,e)}}}(n)},n.array=function(n,t){return{type:function(){return"Array<".concat(n.type(),">")},validateBeforeMap:function(t,e){return Array.isArray(t)?e.flatmapChildren(i(t),n,(function(t,e){return n.validateBeforeMap(t[1],e)})):e.fail()},validateBeforeUnmap:function(t,e){return Array.isArray(t)?e.flatmapChildren(i(t),n,(function(t,e){return n.validateBeforeUnmap(t[1],e)})):e.fail()},map:function(t,e){return e.mapChildren(i(t),n,(function(t,e){return n.map(t[1],e)}))},unmap:function(t,e){return e.mapChildren(i(t),n,(function(t,e){return n.unmap(t[1],e)}))},mapXml:function(e,r){var a=e;return(null==t?void 0:t.xmlItemName)&&(a=e[t.xmlItemName],r=r.createChild(t.xmlItemName,a,n)),r.mapChildren(i(a),n,(function(t,e){return n.mapXml(t[1],e)}))},unmapXml:function(e,r){var a,o=r.mapChildren(i(e),n,(function(t,e){return n.unmapXml(t[1],e)}));return(null==t?void 0:t.xmlItemName)?((a={})[t.xmlItemName]=o,a):o},validateBeforeMapXml:function(e,r){var a=e;if(null==t?void 0:t.xmlItemName){var o="Expected array to be wrapped with XML element ".concat(t.xmlItemName,".");if("object"!=typeof e||null===e||!(t.xmlItemName in e))return r.fail(o);a=e[t.xmlItemName],r=r.createChild(t.xmlItemName,a,n)}return Array.isArray(a)?r.flatmapChildren(i(a),n,(function(t,e){return n.validateBeforeMapXml(t[1],e)})):r.fail()}}},n.bigint=function(){return p({type:"bigint",validate:c(E),map:d})},n.boolean=function(){return p({type:"boolean",validate:c(C),map:function(n){return"boolean"==typeof n?n:"true"===n}})},n.defaults=function(n,t){return{type:function(){return"Defaults<".concat(n.type(),",").concat(l(t),">")},validateBeforeMap:function(e,r){return k(e,t)?[]:n.validateBeforeMap(e,r)},validateBeforeUnmap:function(e,r){return k(e,t)?[]:n.validateBeforeUnmap(e,r)},map:function(e,r){return k(e,t)?t:n.map(e,r)},unmap:function(e,r){return k(e,t)?t:n.unmap(e,r)},validateBeforeMapXml:function(e,r){return k(e,t)?[]:n.validateBeforeMapXml(e,r)},mapXml:function(e,r){return k(e,t)?t:n.mapXml(e,r)},unmapXml:function(e,r){return k(e,t)?t:n.unmapXml(e,r)}}},n.dict=T,n.dictWithXmlEntries=function(n){var e=T(n),o=t({},e);return o.unmapXml=function(n,t){return{entry:u(e.unmapXml(n,t)).map((function(n){var t=a(n,2);return{$:{key:t[0]},_:t[1]}}))}},o.mapXml=function(n,t){var a,o;if(!("entry"in n))return{};var i=n.entry;Array.isArray(i)||(i=[i]);var u={};try{for(var l=r(i),f=l.next();!f.done;f=l.next()){var c=f.value;u[c.$.key]=c._}}catch(n){a={error:n}}finally{try{f&&!f.done&&(o=l.return)&&o.call(l)}finally{if(a)throw a.error}}return e.mapXml(u,t)},o.validateBeforeMapXml=function(n,t){var a,o;if("object"!=typeof n||null===n)return t.fail();if(!("entry"in n))return[];var i=n.entry;Array.isArray(i)||(i=[i]);var u={};try{for(var l=r(i),f=l.next();!f.done;f=l.next()){var c=f.value;if("object"!=typeof c||null===c)return t.fail('Expected "entry" to be an XML element.');if(!("$"in c)||!("key"in c.$))return t.fail('Expected "entry" element to have an attribute named "key".');var p=c;u[p.$.key]=p._}}catch(n){a={error:n}}finally{try{f&&!f.done&&(o=l.return)&&o.call(l)}finally{if(a)throw a.error}}return e.validateBeforeMapXml(u,t)},o},n.discriminatedObject=function(n,t,e,r,o){var i=Object.values(e).reverse(),l=function(n,t,a,o){var u=function(n,t,r){if("object"==typeof n&&null!==n&&(r&&function(n,t){return"$"in n&&"object"==typeof n.$&&t in n.$}(n,t)||!r&&t in n)){var a=r?n.$[t]:n[t];if("string"==typeof a&&a in e)return e[a]}}(n,t,o);if(void 0!==u)return u;for(var l in i)if(0===a(i[l]).length)return i[l];return e[r]},f=function(n,e){return l(n,t,(function(t){return t.validateBeforeMap(n,e)}))},c=function(n,e){var r;return l(n,null!==(r=null==o?void 0:o.xmlName)&&void 0!==r?r:t,(function(t){return t.validateBeforeMapXml(n,e)}),null==o?void 0:o.isAttr)},p=function(t,e){return l(t,n,(function(n){return n.validateBeforeUnmap(t,e)}))};return{type:function(){return"DiscriminatedUnion<".concat(t,",[").concat(u(e).map((function(n){var t=a(n,2);return t[0],t[1].type})).join(","),"]>")},map:function(n,t){return f(n,t).map(n,t)},unmap:function(n,t){return p(n,t).unmap(n,t)},validateBeforeMap:function(n,t){return f(n,t).validateBeforeMap(n,t)},validateBeforeUnmap:function(n,t){return p(n,t).validateBeforeUnmap(n,t)},mapXml:function(n,t){return c(n,t).mapXml(n,t)},unmapXml:function(n,t){return p(n,t).unmapXml(n,t)},validateBeforeMapXml:function(n,t){return c(n,t).validateBeforeMapXml(n,t)}}},n.expandoObject=I,n.extendExpandoObject=function(n,e){return I(t(t({},n.objectSchema),e))},n.extendObject=function(n,e){return W(t(t({},n.objectSchema),e))},n.extendStrictObject=function(n,e){return V(t(t({},n.objectSchema),e))},n.lazy=function(n){var t,e,r,i=(t=n,r=!1,function(){for(var n=[],a=0;a<arguments.length;a++)n[a]=arguments[a];return r?e:(r=!0,e=t.apply(this,n))});return{type:function(){return"Lazy<".concat(i().type(),">")},map:function(){for(var n,t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return(n=i()).map.apply(n,o([],a(t),!1))},unmap:function(){for(var n,t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return(n=i()).unmap.apply(n,o([],a(t),!1))},validateBeforeMap:function(){for(var n,t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return(n=i()).validateBeforeMap.apply(n,o([],a(t),!1))},validateBeforeUnmap:function(){for(var n,t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return(n=i()).validateBeforeUnmap.apply(n,o([],a(t),!1))},mapXml:function(){for(var n,t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return(n=i()).mapXml.apply(n,o([],a(t),!1))},unmapXml:function(){for(var n,t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return(n=i()).unmapXml.apply(n,o([],a(t),!1))},validateBeforeMapXml:function(){for(var n,t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return(n=i()).validateBeforeMapXml.apply(n,o([],a(t),!1))}}},n.literal=function(n){return p({type:"Literal<".concat(l(n),">"),validate:c((function(t){return n===t})),map:function(){return n}})},n.nullable=function(n){return{type:function(){return"Nullable<".concat(n.type(),">")},validateBeforeMap:function(t,e){return s(t)?[]:n.validateBeforeMap(t,e)},validateBeforeUnmap:function(t,e){return null===t?[]:n.validateBeforeUnmap(t,e)},map:function(t,e){return s(t)?null:n.map(t,e)},unmap:function(t,e){return null===t?null:n.unmap(t,e)},validateBeforeMapXml:function(t,e){return null===t?[]:n.validateBeforeMapXml(t,e)},mapXml:function(t,e){return null===t?null:n.mapXml(t,e)},unmapXml:function(t,e){return null===t?null:n.unmapXml(t,e)}}},n.number=function(){return p({type:"number",validate:c(m),map:v})},n.numberEnum=function(n,t){void 0===t&&(t=!1);var e=c(function(n,t){void 0===t&&(t=!1);var e=Object.values(n);return t?function(n){return m(n)}:function(n){return m(n)&&e.includes(v(n))}}(n,t));return p({type:"Enum<".concat(Object.values(n).filter((function(n){return"number"==typeof n})).join(","),">"),map:v,validate:e})},n.object=W,n.oneOf=function(n,t,e){return t&&e?function(n,t,e){return{type:function(){return"OneOf<".concat(n.map((function(n){return n.type()})).join(" | "),">")},validateBeforeMap:function(r,a){var o=r&&"object"==typeof r&&r[e];return o&&t[o]?t[o].validateBeforeMap(r,a):K(n,r,a)},validateBeforeUnmap:function(r,a){var o=r&&"object"==typeof r&&r[e];return o&&t[o]?t[o].validateBeforeUnmap(r,a):Q(n,r,a)},map:function(r,a){var o=r&&r[e];return o&&t[o]?t[o].map(r,a):nn(n,r,a)},unmap:function(r,a){var o=r&&r[e];return o&&t[o]?t[o].unmap(r,a):tn(n,r,a)},validateBeforeMapXml:function(r,a){var o=r&&"object"==typeof r&&r[e];return o&&t[o]?t[o].validateBeforeMapXml(r,a):Y(n,r,a)},mapXml:function(r,a){var o=r&&r[e];return o&&t[o]?t[o].mapXml(r,a):en(n,r,a)},unmapXml:function(r,a){var o=r&&r[e];return o&&t[o]?t[o].unmapXml(r,a):rn(n,r,a)}}}(n,t,e):function(n){return{type:function(){return"OneOf<".concat(n.map((function(n){return n.type()})).join(" | "),">")},validateBeforeMap:function(t,e){return K(n,t,e)},validateBeforeUnmap:function(t,e){return Q(n,t,e)},map:function(t,e){return nn(n,t,e)},unmap:function(t,e){return tn(n,t,e)},validateBeforeMapXml:function(t,e){return Y(n,t,e)},mapXml:function(t,e){return en(n,t,e)},unmapXml:function(t,e){return rn(n,t,e)}}}(n)},n.optional=$,n.strictObject=V,n.string=function(){return p({type:"string",validate:c(an),map:f})},n.stringEnum=function(n,t){void 0===t&&(t=!1);var e=c(function(n,t){void 0===t&&(t=!1);var e=Object.values(n);return t?function(n){return"string"==typeof n}:function(n){return"string"==typeof n&&e.includes(n)}}(n,t));return p({type:"Enum<".concat(Object.values(n).map(l).join(","),">"),map:f,validate:e})},n.typedExpandoObject=function(n,t,e){return L(n,!0,[t,$(T(e))])},n.unknown=function(){return p({type:"unknown",validate:function(){return[]},map:f})},n.validateAndMap=X,n.validateAndMapXml=function(n,t){var e=j(g(n,t.type())),r=t.validateBeforeMapXml(n,e);return 0===r.length?{errors:!1,result:t.mapXml(n,e)}:{errors:r}},n.validateAndUnmap=B,n.validateAndUnmapXml=function(n,t){var e=j(g(n,t.type())),r=t.validateBeforeUnmap(n,e);return 0===r.length?{errors:!1,result:t.unmapXml(n,e)}:{errors:r}}},"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((n="undefined"!=typeof globalThis?globalThis:n||self).ApimaticSchema={});
