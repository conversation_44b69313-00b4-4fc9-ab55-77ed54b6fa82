# Nirvana Organics - Test Environment Nginx Configuration
# Test Site: test.shopnirvanaorganics.com
# Deploy this file to: /etc/nginx/sites-available/nirvana-organics-test
# Enable with: sudo ln -s /etc/nginx/sites-available/nirvana-organics-test /etc/nginx/sites-enabled/

# Rate limiting zones
limit_req_zone $binary_remote_addr zone=test_api_limit:10m rate=20r/s;
limit_req_zone $binary_remote_addr zone=test_admin_limit:10m rate=10r/s;
limit_req_zone $binary_remote_addr zone=test_general_limit:10m rate=50r/s;

# Upstream servers
upstream nirvana_main_backend_test {
    least_conn;
    server 127.0.0.1:5000 max_fails=3 fail_timeout=30s;
    keepalive 32;
}

upstream nirvana_admin_backend_test {
    least_conn;
    server 127.0.0.1:3001 max_fails=3 fail_timeout=30s;
    keepalive 16;
}

# Test Site - test.shopnirvanaorganics.com
server {
    listen 80;
    server_name test.shopnirvanaorganics.com;
    
    # Redirect HTTP to HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name test.shopnirvanaorganics.com;
    
    # SSL Configuration
    ssl_certificate /etc/letsencrypt/live/test.shopnirvanaorganics.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/test.shopnirvanaorganics.com/privkey.pem;
    ssl_trusted_certificate /etc/letsencrypt/live/test.shopnirvanaorganics.com/chain.pem;
    
    # SSL Security Settings
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    ssl_stapling on;
    ssl_stapling_verify on;
    
    # Security Headers (relaxed for testing)
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Content-Security-Policy "default-src 'self' 'unsafe-inline' 'unsafe-eval'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://js.squareup.com https://sandbox.web.squarecdn.com https://www.googletagmanager.com https://www.google-analytics.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https: blob:; connect-src 'self' https://connect.squareup.com https://connect.squareupsandbox.com https://www.google-analytics.com; frame-src 'self' https://js.squareup.com https://sandbox.web.squarecdn.com;" always;
    
    # Test environment banner
    add_header X-Environment "TEST" always;
    
    # Root directory for static files (main frontend)
    root /var/www/nirvana-frontend-test/main;
    index index.html;
    
    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;
    
    # Rate limiting (more lenient for testing)
    limit_req zone=test_general_limit burst=100 nodelay;
    
    # API routes - proxy to backend
    location /api/ {
        limit_req zone=test_api_limit burst=50 nodelay;
        
        proxy_pass http://nirvana_main_backend_test;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port $server_port;
        
        proxy_cache_bypass $http_upgrade;
        proxy_redirect off;
        proxy_buffering off;
        
        # Timeouts
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
        
        # Error handling
        proxy_next_upstream error timeout invalid_header http_500 http_502 http_503 http_504;
    }
    
    # Admin assets - specific handling (must come before general static assets)
    location /admin/assets/ {
        alias /var/www/nirvana-frontend-test/admin/assets/;
        expires 1h;
        add_header Cache-Control "public";
        add_header Vary "Accept-Encoding";

        # CORS for fonts and assets
        add_header Access-Control-Allow-Origin "*";
        add_header Access-Control-Allow-Methods "GET, OPTIONS";
        add_header Access-Control-Allow-Headers "Origin, X-Requested-With, Content-Type, Accept";

        # Security headers for admin assets
        add_header X-Environment "TEST-ADMIN-ASSETS" always;
    }

    # Static assets with shorter cache for testing
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1h;
        add_header Cache-Control "public";
        add_header Vary "Accept-Encoding";

        # CORS for fonts and assets
        add_header Access-Control-Allow-Origin "*";
        add_header Access-Control-Allow-Methods "GET, OPTIONS";
        add_header Access-Control-Allow-Headers "Origin, X-Requested-With, Content-Type, Accept";

        try_files $uri =404;
    }
    
    # Handle uploads
    location /uploads/ {
        alias /var/www/nirvana-backend-test/uploads/;
        expires 1h;
        add_header Cache-Control "public";
        
        # Security for uploads
        location ~* \.(php|pl|py|jsp|asp|sh|cgi)$ {
            deny all;
        }
    }
    
    # Admin Panel - Path-based routing for test environment
    location /admin {
        alias /var/www/nirvana-frontend-test/admin;
        index admin.html;
        try_files $uri $uri/ /admin/admin.html;

        # Admin-specific security headers for test
        add_header X-Frame-Options "SAMEORIGIN" always;
        add_header X-Environment "TEST-ADMIN" always;
        add_header Content-Security-Policy "default-src 'self' 'unsafe-inline' 'unsafe-eval'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https: blob:; connect-src 'self'; frame-ancestors 'self';" always;

        # No cache for HTML in test
        add_header Cache-Control "no-cache, no-store, must-revalidate";
        add_header Pragma "no-cache";
        add_header Expires "0";
    }

    # Admin API routes - proxy to admin backend
    location /admin/api {
        proxy_pass http://nirvana_admin_backend_test;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;

        # Admin API specific rate limiting
        limit_req zone=test_admin_limit burst=20 nodelay;

        # Admin API timeout settings
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }

    # SPA fallback - serve index.html for all non-API, non-admin routes
    location / {
        try_files $uri $uri/ /index.html;

        # No cache for HTML in test
        add_header Cache-Control "no-cache, no-store, must-revalidate";
        add_header Pragma "no-cache";
        add_header Expires "0";
    }
    
    # Health check endpoint
    location /health {
        access_log off;
        return 200 "test-healthy\n";
        add_header Content-Type text/plain;
    }
    
    # Block access to sensitive files
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    location ~ \.(env|log|config)$ {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    # Logging
    access_log /var/log/nginx/nirvana-test-main-access.log;
    error_log /var/log/nginx/nirvana-test-main-error.log;
}
