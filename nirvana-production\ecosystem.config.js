module.exports = {
  apps: [
    {
      name: 'nirvana-backend-main-production',
      script: './server/index.js',
      cwd: '/var/www/nirvana-backend-production',
      env: {
        NODE_ENV: 'production'
      },
      instances: 1,
      exec_mode: 'fork',
      watch: false,
      max_memory_restart: '1G',
      error_file: '/home/<USER>/.pm2/logs/nirvana-backend-main-production-error.log',
      out_file: '/home/<USER>/.pm2/logs/nirvana-backend-main-production-out.log',
      log_file: '/home/<USER>/.pm2/logs/nirvana-backend-main-production.log',
      time: true,
      restart_delay: 1000,
      max_restarts: 5,
      min_uptime: '30s'
    },
    {
      name: 'nirvana-backend-admin-production',
      script: './server/admin-server.js',
      cwd: '/var/www/nirvana-backend-production',
      env: {
        NODE_ENV: 'production'
      },
      instances: 1,
      exec_mode: 'fork',
      watch: false,
      max_memory_restart: '1G',
      error_file: '/home/<USER>/.pm2/logs/nirvana-backend-admin-production-error.log',
      out_file: '/home/<USER>/.pm2/logs/nirvana-backend-admin-production-out.log',
      log_file: '/home/<USER>/.pm2/logs/nirvana-backend-admin-production.log',
      time: true,
      restart_delay: 1000,
      max_restarts: 5,
      min_uptime: '30s'
    }
  ]
};
