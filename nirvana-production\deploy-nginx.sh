#!/bin/bash

# Nirvana Organics - Production Environment Nginx Deployment Script
# This script deploys the Nginx configuration for the production environment

echo "🌐 Deploying Nginx Configuration for Production Environment"
echo "=========================================================="

# Configuration variables
NGINX_SITE_NAME="nirvana-organics-production"
NGINX_CONFIG_FILE="nginx-site.conf"
SITES_AVAILABLE="/etc/nginx/sites-available"
SITES_ENABLED="/etc/nginx/sites-enabled"

# Check if running as root
if [[ $EUID -ne 0 ]]; then
   echo "❌ This script must be run as root (use sudo)"
   exit 1
fi

# Check if nginx is installed
if ! command -v nginx &> /dev/null; then
    echo "❌ Nginx is not installed. Please install nginx first:"
    echo "   sudo apt update && sudo apt install nginx"
    exit 1
fi

# Check if the nginx config file exists
if [[ ! -f "$NGINX_CONFIG_FILE" ]]; then
    echo "❌ Nginx configuration file '$NGINX_CONFIG_FILE' not found in current directory"
    exit 1
fi

echo "📋 Deploying Nginx configuration..."

# Copy the configuration file to sites-available
cp "$NGINX_CONFIG_FILE" "$SITES_AVAILABLE/$NGINX_SITE_NAME"
if [[ $? -eq 0 ]]; then
    echo "✅ Configuration copied to $SITES_AVAILABLE/$NGINX_SITE_NAME"
else
    echo "❌ Failed to copy configuration file"
    exit 1
fi

# Set proper permissions
chmod 644 "$SITES_AVAILABLE/$NGINX_SITE_NAME"
chown root:root "$SITES_AVAILABLE/$NGINX_SITE_NAME"

# Create symbolic link to enable the site
if [[ -L "$SITES_ENABLED/$NGINX_SITE_NAME" ]]; then
    echo "⚠️  Site already enabled, removing old symlink..."
    rm "$SITES_ENABLED/$NGINX_SITE_NAME"
fi

ln -s "$SITES_AVAILABLE/$NGINX_SITE_NAME" "$SITES_ENABLED/$NGINX_SITE_NAME"
if [[ $? -eq 0 ]]; then
    echo "✅ Site enabled successfully"
else
    echo "❌ Failed to enable site"
    exit 1
fi

# Test nginx configuration
echo "🔍 Testing Nginx configuration..."
nginx -t
if [[ $? -eq 0 ]]; then
    echo "✅ Nginx configuration test passed"
else
    echo "❌ Nginx configuration test failed"
    echo "   Please check the configuration and fix any errors"
    exit 1
fi

# Reload nginx
echo "🔄 Reloading Nginx..."
systemctl reload nginx
if [[ $? -eq 0 ]]; then
    echo "✅ Nginx reloaded successfully"
else
    echo "❌ Failed to reload Nginx"
    exit 1
fi

# Check nginx status
echo "📊 Checking Nginx status..."
systemctl is-active --quiet nginx
if [[ $? -eq 0 ]]; then
    echo "✅ Nginx is running"
else
    echo "⚠️  Nginx is not running, attempting to start..."
    systemctl start nginx
    if [[ $? -eq 0 ]]; then
        echo "✅ Nginx started successfully"
    else
        echo "❌ Failed to start Nginx"
        exit 1
    fi
fi

echo ""
echo "🎉 Nginx deployment completed successfully!"
echo ""
echo "📝 Next Steps:"
echo "   1. Ensure SSL certificates are configured for shopnirvanaorganics.com"
echo "   2. Verify frontend files are deployed to /var/www/nirvana-frontend/"
echo "   3. Ensure backend services are running on ports 5000 (main) and 3001 (admin)"
echo "   4. Test the site: https://shopnirvanaorganics.com"
echo "   5. Test admin panel: https://shopnirvanaorganics.com/admin"
echo ""
echo "🔍 Useful commands:"
echo "   - Check Nginx status: sudo systemctl status nginx"
echo "   - View Nginx logs: sudo tail -f /var/log/nginx/nirvana-production-main-*.log"
echo "   - Test configuration: sudo nginx -t"
echo "   - Reload configuration: sudo systemctl reload nginx"
echo ""
echo "⚠️  PRODUCTION DEPLOYMENT CHECKLIST:"
echo "   □ SSL certificates are valid and up-to-date"
echo "   □ Backend services are running and healthy"
echo "   □ Frontend builds are optimized and deployed"
echo "   □ Database connections are configured correctly"
echo "   □ Environment variables are set for production"
echo "   □ Rate limiting and security headers are active"
echo "   □ Monitoring and logging are configured"
