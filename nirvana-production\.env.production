# Nirvana Organics - Production Environment Configuration
# Main Server Configuration for PRODUCTION ENVIRONMENT
# Deploy to shopnirvanaorganics.com

# Environment
NODE_ENV=production

# Server Configuration
PORT=5000
FRONTEND_URL=https://shopnirvanaorganics.com
BACKEND_URL=https://shopnirvanaorganics.com

# Database Configuration (Production)
DB_HOST=srv1921.hstgr.io
DB_PORT=3306
DB_NAME=u106832845_nirvana
DB_USER=u106832845_root
DB_PASSWORD=Hrishikesh@0912
DB_SSL=true
DB_POOL_MAX=10
DB_POOL_MIN=2

# CORS Configuration
CORS_ORIGIN=https://shopnirvanaorganics.com

# Security Configuration
JWT_SECRET=your-super-secure-jwt-secret-key-here-64-characters-minimum
JWT_REFRESH_SECRET=your-super-secure-refresh-secret-key-here-64-characters
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d
BCRYPT_ROUNDS=12

# Rate Limiting (Production)
RATE_LIMIT_WINDOW_MS=900000
MAIN_RATE_LIMIT_MAX_REQUESTS=500

# Email Configuration (Production)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
SMTP_SECURE=false
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-production-email-app-password
EMAIL_FROM=<EMAIL>
EMAIL_FROM_NAME=Nirvana Organics

# Email Addresses (Production Environment)
EMAIL_ORDERS=<EMAIL>
EMAIL_ORDERS_USER=<EMAIL>
EMAIL_ORDERS_PASS=your-orders-email-app-password
EMAIL_ORDERS_HOST=smtp.gmail.com
EMAIL_ORDERS_PORT=587

EMAIL_SUPPORT=<EMAIL>
EMAIL_SUPPORT_USER=<EMAIL>
EMAIL_SUPPORT_PASS=your-support-email-app-password

EMAIL_CUSTOMER_SERVICE=<EMAIL>
EMAIL_NO_REPLY=<EMAIL>
EMAIL_ADMIN=<EMAIL>

# VAPID Configuration (Production)
# Generate with: npx web-push generate-vapid-keys
VAPID_PUBLIC_KEY=BGSeufHC3qXpetuNrZmPuQAp34DAHFUm3l6ZWoqGYKLY252IwUpPiZe9PjcVlsueX3JTJ8sBhZiJ49rnZuI73kQ
VAPID_PRIVATE_KEY=FZ4eME5YyL0IIF4SZsSW0_PZ8ISMM5mtfGFKPF7a3AQ
VAPID_EMAIL=mailto:<EMAIL>

# Google OAuth Configuration (Production)
GOOGLE_CLIENT_ID=your-production-google-client-id
GOOGLE_CLIENT_SECRET=your-production-google-client-secret
GOOGLE_REDIRECT_URI=https://shopnirvanaorganics.com/auth/google/callback

# Square Payment Configuration (Production)
SQUARE_APPLICATION_ID=your-production-square-application-id
SQUARE_ACCESS_TOKEN=your-production-square-access-token
SQUARE_LOCATION_ID=your-production-square-location-id
SQUARE_ENVIRONMENT=production
SQUARE_WEBHOOK_SIGNATURE_KEY=your-production-square-webhook-signature

# Square OAuth Configuration (Admin Only - Production)
SQUARE_OAUTH_CLIENT_ID=your-production-square-oauth-client-id
SQUARE_OAUTH_CLIENT_SECRET=your-production-square-oauth-client-secret
SQUARE_OAUTH_REDIRECT_URI=https://admin.shopnirvanaorganics.com/auth/square/callback

# Social Media Configuration
FACEBOOK_APP_ID=your-production-facebook-app-id
FACEBOOK_APP_SECRET=your-production-facebook-app-secret
INSTAGRAM_ACCESS_TOKEN=your-production-instagram-access-token
TWITTER_API_KEY=your-production-twitter-api-key
TWITTER_API_SECRET=your-production-twitter-api-secret

# Shipping Configuration
USPS_USER_ID=your-production-usps-user-id
USPS_API_URL=https://secure.shippingapis.com/ShippingAPI.dll

# Session Configuration
SESSION_SECRET=your-production-super-secure-session-secret-64-characters
SESSION_COOKIE_SECURE=true
SESSION_COOKIE_HTTPONLY=true
SESSION_COOKIE_SAMESITE=strict
SESSION_TIMEOUT=3600000

# API Keys
ENCRYPTION_KEY=your-production-32-character-encryption-key
API_KEY_SECRET=your-production-api-key-secret-64-characters

# Monitoring
HEALTH_CHECK_INTERVAL=30000
PERFORMANCE_MONITORING=true

# Security Configuration (Production)
MAIN_SECURITY_MODE=true
ENABLE_CORS_DEBUG=false
SQL_LOGGING=false

# Backup Configuration
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
