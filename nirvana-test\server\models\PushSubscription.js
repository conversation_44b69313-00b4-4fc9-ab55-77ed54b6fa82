const { DataTypes } = require('sequelize');
const { sequelize } = require('./database');

const PushSubscription = sequelize.define('PushSubscription', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  userId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'Users',
      key: 'id'
    }
  },
  endpoint: {
    type: DataTypes.TEXT,
    allowNull: false
  },
  p256dh: {
    type: DataTypes.TEXT,
    allowNull: false
  },
  auth: {
    type: DataTypes.TEXT,
    allowNull: false
  },
  isActive: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  },
  userAgent: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  lastUsed: {
    type: DataTypes.DATE,
    allowNull: true
  }
}, {
  tableName: 'push_subscriptions',
  timestamps: true,
  indexes: [
    {
      fields: ['userId']
    },
    {
      fields: ['endpoint'],
      unique: true
    },
    {
      fields: ['isActive']
    }
  ]
});

// Associations
PushSubscription.associate = (models) => {
  PushSubscription.belongsTo(models.User, {
    foreignKey: 'userId',
    as: 'user'
  });
};

module.exports = PushSubscription;
